# ChainOps Demo Guide

This guide will walk you through the key features of ChainOps, the complete CI/CD platform.

## 🎯 Demo Scenario

We'll demonstrate a complete CI/CD workflow:
1. **User Authentication** - Login to the platform
2. **Project Management** - Create and manage projects
3. **Pipeline Creation** - Set up CI/CD pipelines
4. **Job Execution** - Run jobs with real-time monitoring
5. **Artifact Management** - Store and retrieve build artifacts
6. **Environment Management** - Deploy to different environments

## 🚀 Getting Started

### 1. Start the Platform
```bash
# Quick setup (first time)
./setup.sh

# Or if already set up
make docker-up
```

### 2. Access the Web Interface
Open your browser and navigate to: http://localhost:3000

### 3. Login with Demo Credentials
- **Admin User**: `<EMAIL>` / `admin123`
- **Demo User**: `<EMAIL>` / `demo123`

## 📊 Dashboard Overview

After logging in, you'll see the main dashboard with:

### Key Metrics
- **Total Pipelines**: Number of configured pipelines
- **Active Jobs**: Currently running jobs
- **Success Rate**: Overall pipeline success percentage
- **Average Duration**: Mean execution time

### Real-time Components
- **Pipeline Status**: Live status of recent pipeline runs
- **Recent Activity**: Timeline of recent actions
- **System Health**: Status of all platform components
- **Quick Actions**: Common tasks and shortcuts

## 🏗️ Project Management

### Creating a New Project
1. Click **"Create Project"** from Quick Actions
2. Fill in project details:
   - **Name**: "Demo Web App"
   - **Slug**: "demo-web-app"
   - **Description**: "Demo application for testing"
   - **Repository URL**: "https://github.com/demo/web-app.git"
   - **Default Branch**: "main"

### Project Features
- **Environments**: Development, Staging, Production
- **Secrets Management**: Encrypted environment variables
- **Team Members**: Role-based access control
- **Pipeline Configuration**: YAML-based pipeline definitions

## ⚡ Pipeline Creation

### Sample Pipeline Configuration
```yaml
name: Build and Test
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Deployment commands here
```

### Pipeline Features
- **Multi-step Jobs**: Sequential and parallel execution
- **Conditional Logic**: Branch-based and status-based conditions
- **Artifact Handling**: Upload and download build artifacts
- **Environment Variables**: Secure secret injection
- **Real-time Logs**: Live log streaming during execution

## 🔄 Job Execution Flow

### 1. Trigger Pipeline
- **Manual Trigger**: Click "Run Pipeline" button
- **Git Webhook**: Automatic trigger on push/PR
- **Scheduled**: Cron-based scheduling

### 2. Job Processing
1. **Queue**: Job added to Redis queue
2. **Runner Pickup**: Available runner claims job
3. **Container Creation**: Docker container spawned
4. **Execution**: Steps run in isolated environment
5. **Artifact Collection**: Build outputs stored in MinIO
6. **Cleanup**: Container removed, logs preserved

### 3. Real-time Monitoring
- **Live Status Updates**: WebSocket-powered updates
- **Log Streaming**: Real-time log output
- **Progress Tracking**: Step-by-step progress
- **Resource Usage**: CPU, memory, duration metrics

## 📦 Artifact Management

### Artifact Storage
- **MinIO Backend**: S3-compatible object storage
- **Versioning**: Automatic artifact versioning
- **Retention**: Configurable retention policies
- **Access Control**: Role-based download permissions

### Artifact Types
- **Build Outputs**: Compiled applications, bundles
- **Test Results**: Coverage reports, test artifacts
- **Documentation**: Generated docs, API specs
- **Container Images**: Docker images (with registry integration)

## 🌍 Environment Management

### Environment Types
1. **Development**: Feature development and testing
2. **Staging**: Pre-production validation
3. **Production**: Live application deployment

### Environment Features
- **Variable Management**: Environment-specific configuration
- **Secret Injection**: Secure credential management
- **Deployment Tracking**: Deployment history and rollbacks
- **Health Monitoring**: Environment health checks

## 🔐 Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure, stateless authentication
- **Role-Based Access**: Admin, User, Viewer roles
- **Project Permissions**: Owner, Maintainer, Developer, Viewer
- **API Keys**: Programmatic access tokens

### Secret Management
- **AES-256 Encryption**: Industry-standard encryption
- **Scope-based Access**: Project and environment scoping
- **Audit Logging**: Complete access tracking
- **Rotation Support**: Automated secret rotation

## 📈 Monitoring & Analytics

### Built-in Metrics
- **Pipeline Success Rates**: Success/failure trends
- **Execution Times**: Performance analytics
- **Resource Usage**: Runner utilization
- **Error Patterns**: Failure analysis

### Health Monitoring
- **Service Health**: All component status
- **Database Performance**: Query performance metrics
- **Queue Status**: Job queue health
- **Storage Usage**: Artifact storage metrics

## 🔧 Advanced Features

### Webhook Integration
- **GitHub**: Push, PR, release events
- **GitLab**: Push, merge request, tag events
- **Bitbucket**: Push, pull request events
- **Custom Webhooks**: Generic webhook support

### Notification Channels
- **Slack**: Pipeline status notifications
- **Discord**: Team collaboration updates
- **Email**: Critical alerts and summaries
- **Webhooks**: Custom notification endpoints

### API Integration
- **REST API**: Complete platform API
- **OpenAPI Docs**: Interactive API documentation
- **CLI Tools**: Command-line interface
- **SDK Support**: Language-specific SDKs

## 🎮 Interactive Demo

### Try These Scenarios

1. **Basic Pipeline**
   - Create a simple "Hello World" pipeline
   - Watch real-time execution
   - Download generated artifacts

2. **Multi-Environment Deployment**
   - Deploy to staging environment
   - Validate deployment
   - Promote to production

3. **Failure Handling**
   - Create a pipeline with intentional failure
   - Observe error handling
   - Retry failed jobs

4. **Team Collaboration**
   - Add team members to project
   - Set different permission levels
   - Collaborate on pipeline development

## 📚 Next Steps

After exploring the demo:

1. **Read Documentation**: Comprehensive guides in `/docs`
2. **Explore API**: Interactive API docs at `/api/docs`
3. **Join Community**: GitHub discussions and issues
4. **Contribute**: Submit PRs and feature requests
5. **Deploy**: Production deployment guides

## 🆘 Troubleshooting

### Common Issues
- **Services not starting**: Check Docker and ports
- **Database connection**: Verify PostgreSQL is running
- **Authentication errors**: Check JWT secret configuration
- **Job failures**: Verify Docker daemon access

### Getting Help
- **Logs**: `make logs` for service logs
- **Health Check**: `make health` for service status
- **Documentation**: Check README and docs folder
- **Issues**: GitHub issues for bug reports

---

**Enjoy exploring ChainOps!** 🚀
