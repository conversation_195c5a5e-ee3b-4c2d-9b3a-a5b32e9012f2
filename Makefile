# ChainOps Makefile

.PHONY: help install dev build start stop clean test lint docker-up docker-down docker-build

# Default target
help:
	@echo "ChainOps - CI/CD Platform"
	@echo ""
	@echo "Available commands:"
	@echo "  install      Install all dependencies"
	@echo "  dev          Start development servers"
	@echo "  build        Build all services"
	@echo "  start        Start production services"
	@echo "  stop         Stop all services"
	@echo "  clean        Clean build artifacts"
	@echo "  test         Run all tests"
	@echo "  lint         Run linting"
	@echo "  docker-up    Start Docker services"
	@echo "  docker-down  Stop Docker services"
	@echo "  docker-build Build Docker images"
	@echo "  setup        Initial setup for development"

# Install dependencies
install:
	@echo "Installing dependencies..."
	npm install
	cd backend && npm install
	cd frontend && npm install
	cd runner && npm install

# Development
dev:
	@echo "Starting development servers..."
	npm run dev

dev-services:
	@echo "Starting development services (DB, Redis, MinIO)..."
	docker-compose -f docker-compose.dev.yml up -d

dev-backend:
	@echo "Starting backend development server..."
	cd backend && npm run dev

dev-frontend:
	@echo "Starting frontend development server..."
	cd frontend && npm run dev

dev-runner:
	@echo "Starting runner development server..."
	cd runner && npm run dev

# Build
build:
	@echo "Building all services..."
	npm run build

build-backend:
	@echo "Building backend..."
	cd backend && npm run build

build-frontend:
	@echo "Building frontend..."
	cd frontend && npm run build

build-runner:
	@echo "Building runner..."
	cd runner && npm run build

# Production
start:
	@echo "Starting production services..."
	docker-compose up -d

stop:
	@echo "Stopping all services..."
	docker-compose down

# Testing
test:
	@echo "Running all tests..."
	npm run test

test-backend:
	@echo "Running backend tests..."
	cd backend && npm test

test-frontend:
	@echo "Running frontend tests..."
	cd frontend && npm test

test-runner:
	@echo "Running runner tests..."
	cd runner && npm test

# Linting
lint:
	@echo "Running linting..."
	cd backend && npm run lint
	cd frontend && npm run lint
	cd runner && npm run lint

lint-fix:
	@echo "Fixing linting issues..."
	cd backend && npm run lint:fix
	cd frontend && npm run lint:fix
	cd runner && npm run lint:fix

# Docker
docker-up:
	@echo "Starting Docker services..."
	docker-compose up -d

docker-down:
	@echo "Stopping Docker services..."
	docker-compose down

docker-build:
	@echo "Building Docker images..."
	docker-compose build

docker-dev-up:
	@echo "Starting development Docker services..."
	docker-compose -f docker-compose.dev.yml up -d

docker-dev-down:
	@echo "Stopping development Docker services..."
	docker-compose -f docker-compose.dev.yml down

# Database
db-migrate:
	@echo "Running database migrations..."
	cd backend && npx prisma migrate dev

db-generate:
	@echo "Generating Prisma client..."
	cd backend && npx prisma generate

db-seed:
	@echo "Seeding database..."
	cd backend && npm run db:seed

db-studio:
	@echo "Opening Prisma Studio..."
	cd backend && npx prisma studio

db-reset:
	@echo "Resetting database..."
	cd backend && npx prisma migrate reset

# Setup
setup: install docker-dev-up db-generate db-migrate db-seed
	@echo "Setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Copy environment files:"
	@echo "   cp backend/.env.example backend/.env"
	@echo "   cp frontend/.env.local.example frontend/.env.local"
	@echo "   cp runner/.env.example runner/.env"
	@echo ""
	@echo "2. Start development:"
	@echo "   make dev"
	@echo ""
	@echo "3. Access the application:"
	@echo "   Frontend: http://localhost:4000"
	@echo "   Backend API: http://localhost:4001"
	@echo "   MinIO Console: http://localhost:9003"

# Clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf backend/dist
	rm -rf frontend/.next
	rm -rf frontend/out
	rm -rf runner/dist
	rm -rf node_modules/.cache

clean-docker:
	@echo "Cleaning Docker resources..."
	docker-compose down -v
	docker system prune -f

# Logs
logs:
	@echo "Showing Docker logs..."
	docker-compose logs -f

logs-backend:
	@echo "Showing backend logs..."
	docker-compose logs -f backend

logs-frontend:
	@echo "Showing frontend logs..."
	docker-compose logs -f frontend

logs-runner:
	@echo "Showing runner logs..."
	docker-compose logs -f runner

# Health checks
health:
	@echo "Checking service health..."
	@curl -f http://localhost:4001/health || echo "Backend: DOWN"
	@curl -f http://localhost:4000 || echo "Frontend: DOWN"
	@curl -f http://localhost:8080/health || echo "Runner: DOWN"

# Backup
backup-db:
	@echo "Backing up database..."
	docker exec chainops-postgres pg_dump -U chainops chainops > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Security
security-scan:
	@echo "Running security scan..."
	cd backend && npm audit
	cd frontend && npm audit
	cd runner && npm audit
