# ChainOps - Complete CI/CD Platform

A modern, enterprise-grade CI/CD platform with a beautiful web interface, built with React, Node.js, PostgreSQL, and Docker. ChainOps provides everything you need to build, test, and deploy your applications with confidence.

## ✨ Key Features

### 🎯 **Core CI/CD Capabilities**
- **Pipeline Management** - Visual pipeline editor with YAML configuration
- **Multi-Runner Support** - Distributed job execution with Docker containers
- **Real-time Monitoring** - Live status updates via WebSockets
- **Artifact Management** - Secure storage and versioning with MinIO
- **Environment Management** - Multiple deployment environments
- **Secret Management** - Encrypted secrets with role-based access

### 🌐 **Modern Web Interface**
- **React + Next.js** - Fast, responsive web dashboard
- **Tailwind CSS** - Beautiful, consistent design system
- **Real-time Updates** - Live pipeline status and logs
- **Mobile Responsive** - Works on all devices
- **Dark/Light Mode** - User preference support

### 🔧 **Developer Experience**
- **Git Integration** - Webhook support for GitHub, Git<PERSON>ab, Bitbucket
- **Multiple Languages** - Node.js, Python, Go, Java, .NET support
- **Docker Native** - Container-based job execution
- **API First** - Complete REST API with OpenAPI documentation
- **CLI Tools** - Command-line interface for automation

### 🔐 **Security & Compliance**
- **JWT Authentication** - Secure token-based authentication
- **Role-Based Access Control** - Fine-grained permissions
- **Audit Logging** - Complete activity tracking
- **Secret Encryption** - AES-256 encrypted secrets storage
- **HTTPS/TLS** - Secure communication

### 📊 **Monitoring & Analytics**
- **Built-in Metrics** - Performance and usage analytics
- **Health Checks** - Service monitoring and alerting
- **Log Aggregation** - Centralized logging with search
- **Notifications** - Slack, Discord, Email integrations
- **Reporting** - Pipeline success rates and trends

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (React)       │◄──►│   (Node.js)     │◄──►│   (PostgreSQL)  │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Runner        │    │   Job Queue     │    │   Artifacts     │
│   (Docker)      │◄──►│   (Redis)       │    │   (MinIO)       │
│                 │    │   Port: 6379    │    │   Port: 9000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Tech Stack

### Frontend
- **React 18** with Next.js 14
- **Tailwind CSS** for styling
- **TypeScript** for type safety
- **Socket.io** for real-time updates
- **React Query** for data fetching

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **Prisma** ORM for database
- **Socket.io** for WebSockets
- **Bull** for job queues
- **JWT** for authentication

### Infrastructure
- **PostgreSQL** for data persistence
- **Redis** for job queue and caching
- **MinIO** for artifact storage
- **Docker** for containerization

## 🚀 Quick Start

### Prerequisites
- **Docker & Docker Compose** - For containerized services
- **Node.js 18+** - For local development
- **Git** - For version control
- **Make** (optional) - For convenient commands

### 🎬 One-Command Setup
```bash
# Clone the repository
git clone <repository-url>
cd ChainOps

# Run the automated setup script
./setup.sh

# OR use the custom port startup script
./start-chainops.sh
```

The setup script will:
1. ✅ Check prerequisites
2. 📦 Install all dependencies
3. ⚙️ Setup environment files
4. 🐳 Start Docker services
5. 🗄️ Setup and seed database
6. 🔨 Build all applications

### 🐳 Docker Quick Start
```bash
# Start all services with Docker
make docker-up

# Or manually with docker-compose
docker-compose up -d

# View logs
make logs

# Stop services
make docker-down
```

### 💻 Development Mode
```bash
# Start development services (DB, Redis, MinIO)
make dev-services

# Start all development servers
make dev

# Or start individual services
make dev-backend    # Backend API server
make dev-frontend   # Frontend web interface
make dev-runner     # Job runner agent
```

### 🌐 Access the Application
- **Web Dashboard**: http://localhost:4000
- **Backend API**: http://localhost:4001
- **API Documentation**: http://localhost:4001/docs
- **Health Check**: http://localhost:4001/health
- **MinIO Console**: http://localhost:9003
- **Prisma Studio**: `make db-studio`

### 🔑 Default Credentials
- **Admin User**: `<EMAIL>` / `admin123`
- **Demo User**: `<EMAIL>` / `demo123`
- **MinIO**: `chainops` / `chainops_password`

## 🔧 Development Guide

### 📁 Project Structure
```
ChainOps/
├── 🎨 frontend/          # React + Next.js web interface
│   ├── src/
│   │   ├── app/          # Next.js app router pages
│   │   ├── components/   # Reusable UI components
│   │   ├── hooks/        # Custom React hooks
│   │   ├── services/     # API service layer
│   │   ├── store/        # State management
│   │   ├── types/        # TypeScript type definitions
│   │   └── utils/        # Utility functions
│   └── public/           # Static assets
├── 🚀 backend/           # Node.js API server
│   ├── src/
│   │   ├── controllers/  # Request handlers
│   │   ├── middleware/   # Express middleware
│   │   ├── models/       # Data models
│   │   ├── routes/       # API routes
│   │   ├── services/     # Business logic
│   │   ├── types/        # TypeScript types
│   │   └── utils/        # Utility functions
│   ├── database/         # Database scripts
│   └── prisma/           # Database schema & migrations
├── 🏃 runner/            # Job runner agent
│   ├── src/
│   │   ├── services/     # Core runner services
│   │   ├── types/        # TypeScript types
│   │   └── utils/        # Utility functions
│   └── Dockerfile        # Runner container image
├── 📚 docs/              # Documentation
├── 🐳 docker-compose.yml # Production Docker setup
├── 🔧 docker-compose.dev.yml # Development services
├── 📋 Makefile           # Development commands
└── 🚀 setup.sh           # Automated setup script
```

### 🛠️ Development Commands
```bash
# Install dependencies
make install

# Start development
make dev                 # All services
make dev-backend         # Backend only
make dev-frontend        # Frontend only
make dev-runner          # Runner only

# Database operations
make db-migrate          # Run migrations
make db-generate         # Generate Prisma client
make db-seed             # Seed with demo data
make db-studio           # Open database admin
make db-reset            # Reset database

# Testing
make test                # Run all tests
make test-backend        # Backend tests only
make test-frontend       # Frontend tests only

# Code quality
make lint                # Run linting
make lint-fix            # Fix linting issues

# Docker operations
make docker-up           # Start all services
make docker-down         # Stop all services
make docker-build        # Build images
make logs                # View logs

# Utilities
make health              # Check service health
make clean               # Clean build artifacts
make backup-db           # Backup database
```

### 🔄 Development Workflow
1. **Setup**: Run `./setup.sh` for initial setup
2. **Services**: Start `make dev-services` for databases
3. **Development**: Run `make dev` for all dev servers
4. **Changes**: Edit code, hot reload handles updates
5. **Testing**: Run `make test` before committing
6. **Database**: Use `make db-studio` for data inspection

## 📁 Project Structure

```
ChainOps/
├── frontend/          # React + Next.js web interface
├── backend/           # Node.js API server
├── runner/            # Job runner agent
├── docs/              # Documentation
├── docker-compose.yml # Development environment
└── package.json       # Root package configuration
```

## 🔐 Environment Variables

Create `.env` files in each service directory:

### Backend (.env)
```
DATABASE_URL=postgresql://chainops:chainops_password@localhost:5432/chainops
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secret-jwt-key
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=chainops
MINIO_SECRET_KEY=chainops_password
```

### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
```

## 📖 API Documentation

Once the backend is running, visit:
- **Swagger UI**: http://localhost:3001/docs
- **API Health**: http://localhost:3001/health

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests
npm run test:backend

# Run frontend tests
npm run test:frontend
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
