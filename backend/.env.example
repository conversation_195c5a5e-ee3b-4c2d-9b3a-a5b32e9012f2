# Database
DATABASE_URL="postgresql://chainops:chainops_password@localhost:5433/chainops"

# Redis
REDIS_URL="redis://localhost:6380"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production-2024"
JWT_EXPIRES_IN="7d"

# MinIO / S3
MINIO_ENDPOINT="localhost:9002"
MINIO_ACCESS_KEY="chainops"
MINIO_SECRET_KEY="chainops_password"
MINIO_USE_SSL=false
MINIO_BUCKET="chainops-artifacts"

# Server
PORT=4001
NODE_ENV="development"

# CORS
CORS_ORIGIN="http://localhost:4000"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email (Optional)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM="<EMAIL>"

# Webhooks
WEBHOOK_SECRET="webhook-secret-key"

# Notifications
SLACK_WEBHOOK_URL=""
DISCORD_WEBHOOK_URL=""

# Runner
RUNNER_TOKEN="runner-secret-token"
RUNNER_TIMEOUT=3600

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/chainops.log"
