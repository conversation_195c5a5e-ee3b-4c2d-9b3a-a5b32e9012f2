-- ChainOps Database Initialization Script
-- This script sets up the initial database structure and data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance
-- Note: <PERSON>risma will handle most of this, but we can add custom indexes here

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users("isActive");

CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug);
CREATE INDEX IF NOT EXISTS idx_projects_organization ON projects("organizationId");
CREATE INDEX IF NOT EXISTS idx_projects_active ON projects("isActive");

CREATE INDEX IF NOT EXISTS idx_pipelines_project ON pipelines("projectId");
CREATE INDEX IF NOT EXISTS idx_pipelines_status ON pipelines(status);
CREATE INDEX IF NOT EXISTS idx_pipelines_active ON pipelines("isActive");

CREATE INDEX IF NOT EXISTS idx_pipeline_runs_pipeline ON pipeline_runs("pipelineId");
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_status ON pipeline_runs(status);
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_created ON pipeline_runs("createdAt");

CREATE INDEX IF NOT EXISTS idx_jobs_pipeline_run ON jobs("pipelineRunId");
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_created ON jobs("createdAt");
CREATE INDEX IF NOT EXISTS idx_jobs_user ON jobs("userId");

CREATE INDEX IF NOT EXISTS idx_project_members_project ON project_members("projectId");
CREATE INDEX IF NOT EXISTS idx_project_members_user ON project_members("userId");

CREATE INDEX IF NOT EXISTS idx_organization_members_org ON organization_members("organizationId");
CREATE INDEX IF NOT EXISTS idx_organization_members_user ON organization_members("userId");

CREATE INDEX IF NOT EXISTS idx_secrets_project ON secrets("projectId");
CREATE INDEX IF NOT EXISTS idx_api_tokens_user ON api_tokens("userId");
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications("userId");
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications("isRead");

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_projects_search ON projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_pipelines_search ON pipelines USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_pipeline_runs_pipeline_status ON pipeline_runs("pipelineId", status);
CREATE INDEX IF NOT EXISTS idx_jobs_pipeline_run_status ON jobs("pipelineRunId", status);
CREATE INDEX IF NOT EXISTS idx_project_members_project_role ON project_members("projectId", role);

-- Functions for common operations
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updatedAt columns
-- Note: Prisma handles this automatically, but keeping for reference

-- Views for common queries
CREATE OR REPLACE VIEW user_project_access AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    p.id as project_id,
    p.name as project_name,
    p.slug as project_slug,
    pm.role as project_role,
    o.id as organization_id,
    o.name as organization_name,
    om.role as organization_role
FROM users u
LEFT JOIN project_members pm ON u.id = pm."userId"
LEFT JOIN projects p ON pm."projectId" = p.id
LEFT JOIN organizations o ON p."organizationId" = o.id
LEFT JOIN organization_members om ON u.id = om."userId" AND o.id = om."organizationId"
WHERE u."isActive" = true AND (p.id IS NULL OR p."isActive" = true);

-- View for pipeline statistics
CREATE OR REPLACE VIEW pipeline_stats AS
SELECT 
    p.id as pipeline_id,
    p.name as pipeline_name,
    COUNT(pr.id) as total_runs,
    COUNT(CASE WHEN pr.status = 'SUCCESS' THEN 1 END) as successful_runs,
    COUNT(CASE WHEN pr.status = 'FAILED' THEN 1 END) as failed_runs,
    COUNT(CASE WHEN pr.status = 'RUNNING' THEN 1 END) as running_runs,
    AVG(EXTRACT(EPOCH FROM (pr."finishedAt" - pr."startedAt"))) as avg_duration_seconds,
    MAX(pr."createdAt") as last_run_at
FROM pipelines p
LEFT JOIN pipeline_runs pr ON p.id = pr."pipelineId"
WHERE p."isActive" = true
GROUP BY p.id, p.name;

-- View for job statistics
CREATE OR REPLACE VIEW job_stats AS
SELECT 
    j.id as job_id,
    j.name as job_name,
    pr.id as pipeline_run_id,
    p.id as pipeline_id,
    p.name as pipeline_name,
    proj.id as project_id,
    proj.name as project_name,
    j.status,
    j."startedAt",
    j."finishedAt",
    EXTRACT(EPOCH FROM (j."finishedAt" - j."startedAt")) as duration_seconds
FROM jobs j
JOIN pipeline_runs pr ON j."pipelineRunId" = pr.id
JOIN pipelines p ON pr."pipelineId" = p.id
JOIN projects proj ON p."projectId" = proj.id;

-- Function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    cutoff_date TIMESTAMP;
BEGIN
    cutoff_date := CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    -- Delete old pipeline runs and their associated jobs
    WITH deleted_runs AS (
        DELETE FROM pipeline_runs 
        WHERE "createdAt" < cutoff_date 
        AND status IN ('SUCCESS', 'FAILED', 'CANCELLED')
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted_runs;
    
    -- Delete old notifications
    DELETE FROM notifications 
    WHERE "createdAt" < cutoff_date AND "isRead" = true;
    
    -- Delete expired API tokens
    DELETE FROM api_tokens 
    WHERE "expiresAt" IS NOT NULL AND "expiresAt" < CURRENT_TIMESTAMP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data(90);');

COMMENT ON FUNCTION cleanup_old_data IS 'Cleans up old pipeline runs, notifications, and expired tokens';
COMMENT ON VIEW user_project_access IS 'Provides user access information across projects and organizations';
COMMENT ON VIEW pipeline_stats IS 'Aggregated statistics for pipelines';
COMMENT ON VIEW job_stats IS 'Detailed job execution statistics';
