{"name": "chainops-backend", "version": "1.0.0", "description": "ChainOps Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "jest --config jest.e2e.config.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "^5.7.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "minio": "^7.1.3", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-vault": "^0.9.22", "nodemailer": "^6.9.7", "prom-client": "^14.2.0", "socket.io": "^4.7.4", "tsx": "^4.19.4", "uuid": "^9.0.1", "winston": "^3.11.0", "yaml": "^2.3.4", "ajv": "^8.12.0", "js-yaml": "^4.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/minio": "^7.0.13", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/js-yaml": "^4.0.9", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}