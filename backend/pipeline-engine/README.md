# ChainOps Pipeline Engine

A powerful Go-based pipeline orchestration engine with YAML parsing, DAG building, matrix expansion, and Redis-based job queue scheduling.

## Features

- **YAML Pipeline Parsing**: Parse complex pipeline configurations with validation
- **DAG Building**: Build and validate Directed Acyclic Graphs from job dependencies
- **Matrix Strategy**: Expand jobs with matrix configurations for parallel execution
- **Conditional Execution**: Support for conditional job execution with `if` statements
- **Job Queue**: Redis-based job queue with priority scheduling
- **Worker System**: Scalable worker system for job execution
- **Cycle Detection**: Automatic detection of circular dependencies
- **Execution Planning**: Generate optimal execution plans with parallelization

## Directory Structure

```
pipeline-engine/
├── jobqueue/
│   └── queue.go          # Redis-based job queue implementation
├── pipeline/
│   ├── pipeline.go       # Pipeline/job structs and helpers
│   ├── parser.go         # YAML parsing and validation logic
│   ├── dag.go           # DAG building and validation
│   └── main.go          # Example: parse pipeline, schedule jobs
├── example.chainops.yml  # Complex example pipeline
├── simple.chainops.yml   # Simple example pipeline
├── go.mod               # Go module definition
└── README.md            # This file
```

## Installation

1. Install Go 1.21 or later
2. Install Redis server
3. Get dependencies:

```bash
go mod tidy
```

## Usage

### Basic Usage

```bash
# Run with simple pipeline
go run pipeline/main.go simple.chainops.yml

# Run with complex pipeline
go run pipeline/main.go example.chainops.yml
```

### Pipeline YAML Format

```yaml
name: My Pipeline
version: "1.0"

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: "18"

jobs:
  build:
    name: Build Application
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Build
        script:
          - make build

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: [build]
    strategy:
      matrix:
        node-version: [16, 18, 20]
        os: [ubuntu-latest, windows-latest]
    steps:
      - name: Test
        script:
          - npm test
```

### Key Components

#### 1. Pipeline Parser

```go
// Parse pipeline from file
pipeline, err := pipeline.ParsePipelineYAML("my-pipeline.yml")

// Parse from string
pipeline, err := pipeline.ParsePipelineYAMLFromString(yamlContent)

// Validate pipeline
err := pipeline.ValidatePipeline(pipeline)
```

#### 2. DAG Builder

```go
// Build DAG from pipeline
dag, err := pipeline.BuildDAG(pipeline)

// Get execution plan
executionPlan := dag.GetExecutionPlan()

// Get ready jobs
readyJobs := dag.ReadyJobs(completedJobs)
```

#### 3. Job Queue

```go
// Create job queue
queue := jobqueue.NewJobQueue("localhost:6379", "my-queue")

// Enqueue job
job := &jobqueue.Job{
    ID:      "job-1",
    Name:    "Build",
    Payload: map[string]string{"script": "make build"},
}
queue.Enqueue(ctx, job)

// Dequeue job
job, err := queue.Dequeue(ctx)
```

#### 4. Worker System

```go
// Create worker
worker := jobqueue.NewWorker("worker-1", queue, jobHandler)

// Start worker
go worker.Start(ctx)

// Job handler function
func jobHandler(ctx context.Context, job *jobqueue.Job) error {
    // Process the job
    return nil
}
```

## Advanced Features

### Matrix Strategy

Expand jobs across multiple dimensions:

```yaml
jobs:
  test:
    strategy:
      matrix:
        node-version: [16, 18, 20]
        os: [ubuntu-latest, windows-latest]
        include:
          - node-version: 18
            os: macos-latest
        exclude:
          - node-version: 16
            os: windows-latest
```

### Conditional Execution

```yaml
jobs:
  deploy:
    if: github.ref == 'refs/heads/main'
    needs: [test]
```

### Services and Containers

```yaml
jobs:
  test:
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
    container:
      image: node:18
```

### Environment Configuration

```yaml
jobs:
  deploy:
    environment:
      name: production
      url: https://myapp.com
```

## API Reference

### Pipeline Types

- `Pipeline`: Main pipeline configuration
- `Job`: Individual job configuration
- `Step`: Job step configuration
- `Strategy`: Matrix and execution strategy
- `Trigger`: Pipeline trigger configuration

### DAG Types

- `DAG`: Directed Acyclic Graph representation
- `DAGNode`: Individual node in the DAG
- `MatrixJob`: Expanded matrix job

### Queue Types

- `Job`: Job in the queue
- `JobQueue`: Redis-based job queue
- `Worker`: Job processing worker

## Examples

### Simple Pipeline

```yaml
jobs:
  build:
    name: Build
    steps:
      - script: [make build]
    tags: [docker]

  test:
    name: Test
    needs: [build]
    steps:
      - script: [make test]
    tags: [docker]

  deploy:
    name: Deploy
    needs: [test]
    steps:
      - script: [./deploy.sh]
    tags: [docker]
```

### Matrix Pipeline

```yaml
jobs:
  test:
    strategy:
      matrix:
        version: [16, 18, 20]
        os: [ubuntu, windows]
    steps:
      - name: Test on ${{ matrix.os }} with Node ${{ matrix.version }}
        script:
          - npm test
```

## Testing

Run the pipeline engine with Redis:

```bash
# Start Redis
redis-server

# Run pipeline engine
go run pipeline/main.go example.chainops.yml
```

Without Redis (simulation mode):

```bash
# Pipeline engine will detect missing Redis and simulate
go run pipeline/main.go simple.chainops.yml
```

## Performance

- **Parallel Execution**: Jobs at the same DAG level run in parallel
- **Priority Scheduling**: Jobs scheduled by DAG level (dependencies first)
- **Matrix Optimization**: Efficient expansion of matrix configurations
- **Memory Efficient**: Streaming job processing with Redis queues

## Error Handling

- **Cycle Detection**: Automatic detection of circular dependencies
- **Validation**: Comprehensive YAML and configuration validation
- **Retry Logic**: Built-in job retry mechanisms
- **Graceful Failures**: Continue-on-error support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details
