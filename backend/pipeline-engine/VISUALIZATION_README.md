# ChainOps Pipeline Visualization System

A comprehensive, modern pipeline visualization system with both 2D and 3D views, real-time updates, and interactive node details.

## 🎨 Features

### **Advanced 2D Visualization**
- **Interactive Pipeline Graph**: Drag, zoom, pan with React Flow
- **Real-time Updates**: Live status updates every 5 seconds
- **Multiple Layouts**: Horizontal, vertical, circular, and automatic DAG layout
- **Status Indicators**: Color-coded nodes with icons for different job states
- **Minimap & Controls**: Easy navigation for large pipelines
- **Node Details**: Click nodes to see detailed information, logs, and artifacts

### **Immersive 3D Visualization**
- **3D Pipeline Nodes**: Spheres, cubes, and octahedrons based on job status
- **Interactive 3D Environment**: Orbit controls, zoom, and pan
- **Animated Elements**: Running jobs rotate and emit particles
- **Spatial Layout**: Z-axis variation for complex pipeline visualization
- **Hover Effects**: Node scaling and information tooltips

### **Comprehensive Statistics**
- **Pipeline Metrics**: Success rate, duration, parallelism analysis
- **Interactive Charts**: Bar charts, pie charts, and duration analysis
- **Job Details Table**: Sortable table with all job information
- **Real-time Stats**: Live updating statistics during pipeline execution

### **Modern UI/UX**
- **Clean Design**: Inspired by Jenkins Blue Ocean and GitHub Actions
- **Dark/Light Themes**: Automatic theme detection and switching
- **Responsive Layout**: Works on desktop, tablet, and mobile
- **Accessibility**: Full keyboard navigation and screen reader support

## 🏗️ Architecture

### **Backend (Go)**
```
backend/pipeline-engine/
├── editor/
│   ├── dag_types.go      # Data structures for visualization
│   ├── dag_api.go        # REST API endpoints
│   └── main.go           # API server
├── webhook-server.go     # Webhook integration
└── test_visualization.go # Testing and demo
```

### **Frontend (React)**
```
frontend/src/
├── components/pipeline/
│   ├── PipelineGraph.jsx     # 2D visualization with React Flow
│   ├── PipelineNode.jsx      # Custom node component
│   ├── Pipeline3D.jsx        # 3D visualization with Three.js
│   ├── PipelineEditor.jsx    # Main editor interface
│   ├── PipelineStats.jsx     # Statistics and charts
│   └── NodeDetailsPanel.jsx  # Node detail sidebar
├── utils/
│   └── dagreLayout.js        # Automatic layout algorithms
└── App.jsx                   # Main application
```

## 🚀 Quick Start

### **1. Start the Backend API**
```bash
cd backend/pipeline-engine
go run test_visualization.go
```
The API server will start on `http://localhost:8081`

### **2. Start the Frontend**
```bash
cd frontend
npm install
npm run dev
```
The frontend will start on `http://localhost:5173`

### **3. Open in Browser**
Navigate to `http://localhost:5173` and explore:
- **2D View**: Interactive pipeline graph with real-time updates
- **3D View**: Immersive 3D pipeline visualization
- **Stats View**: Comprehensive pipeline analytics

## 📊 API Endpoints

### **Pipeline DAG**
```http
GET /api/editor/pipeline/dag?id=<pipeline_id>&layout=<layout>
```
Returns the complete pipeline DAG with node positions and status.

**Parameters:**
- `id` (required): Pipeline identifier
- `run_id` (optional): Specific run identifier
- `layout` (optional): Layout algorithm (`horizontal`, `vertical`, `circular`)

**Response:**
```json
{
  "id": "pipeline-1",
  "name": "ChainOps Demo Pipeline",
  "status": "running",
  "nodes": [...],
  "edges": [...],
  "stats": {...}
}
```

### **Pipeline Status**
```http
GET /api/editor/pipeline/status?run_id=<run_id>
```
Returns real-time status of a pipeline execution.

### **Pipeline Validation**
```http
POST /api/editor/pipeline/validate
```
Validates a pipeline configuration and returns errors/warnings.

## 🎯 Visualization Features

### **Node Types & Status**
- **Success**: Green circle with checkmark
- **Error**: Red octahedron with X icon
- **Running**: Blue sphere with pulse animation
- **Pending**: Gray cube with clock icon
- **Skipped**: Light gray with forward arrow

### **Interactive Elements**
- **Click Nodes**: Open detailed information panel
- **Hover Effects**: Highlight connections and show tooltips
- **Drag & Drop**: Rearrange nodes (in edit mode)
- **Zoom & Pan**: Navigate large pipelines easily

### **Real-time Updates**
- **Live Status**: Nodes update color and animation based on job status
- **Progress Indicators**: Running jobs show progress bars
- **Automatic Refresh**: Configurable update intervals (1-60 seconds)

## 🔧 Customization

### **Layout Algorithms**
- **Dagre**: Automatic hierarchical layout
- **Force-Directed**: Organic, physics-based positioning
- **Circular**: Nodes arranged in a circle
- **Matrix**: Grid-based layout for parallel jobs
- **Manual**: Custom positioning

### **Themes & Styling**
- **Color Schemes**: Customizable status colors
- **Node Shapes**: Different shapes for different job types
- **Edge Styles**: Curved, straight, or animated connections
- **Background**: Grid, dots, or solid colors

### **3D Customization**
- **Camera Controls**: Orbit, pan, zoom limits
- **Lighting**: Ambient, directional, and point lights
- **Materials**: Metallic, glass, or matte finishes
- **Animations**: Rotation, floating, and particle effects

## 📈 Performance

### **Optimization Features**
- **Virtualization**: Handle 1000+ nodes efficiently
- **Level-of-Detail**: Simplified rendering for distant nodes
- **Caching**: Smart caching of layout calculations
- **Lazy Loading**: Load node details on demand

### **Scalability**
- **Large Pipelines**: Tested with 500+ jobs
- **Real-time Updates**: Efficient WebSocket connections
- **Memory Management**: Automatic cleanup of unused resources

## 🧪 Testing

### **Run Backend Tests**
```bash
cd backend/pipeline-engine
go run test_visualization.go
```

### **Test API Endpoints**
```bash
# Health check
curl http://localhost:8081/health

# Get pipeline DAG
curl "http://localhost:8081/api/editor/pipeline/dag?id=pipeline-1"

# Test different layouts
curl "http://localhost:8081/api/editor/pipeline/dag?id=pipeline-1&layout=circular"
```

### **Frontend Testing**
```bash
cd frontend
npm test
npm run build  # Test production build
```

## 🔮 Advanced Features

### **WebSocket Integration**
Real-time updates via WebSocket for live pipeline monitoring:
```javascript
const ws = new WebSocket('ws://localhost:8081/ws/pipeline/pipeline-1');
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  updateNodeStatus(update.nodeId, update.status);
};
```

### **Export Capabilities**
- **PNG/SVG**: High-resolution image export
- **PDF**: Multi-page pipeline documentation
- **JSON**: Pipeline data export
- **DOT**: Graphviz format for external tools

### **Integration Options**
- **Webhook Integration**: Automatic pipeline triggering
- **CI/CD Platforms**: GitHub Actions, GitLab CI, Jenkins
- **Monitoring**: Prometheus metrics and Grafana dashboards
- **Notifications**: Slack, Teams, email alerts

## 🎨 UI/UX Design Principles

### **Visual Hierarchy**
- **Primary**: Pipeline flow and job status
- **Secondary**: Timing and metadata
- **Tertiary**: Configuration details

### **Color Psychology**
- **Green**: Success, completion, safety
- **Red**: Errors, failures, attention needed
- **Blue**: Running, in progress, information
- **Yellow**: Warnings, unstable, caution
- **Gray**: Pending, disabled, neutral

### **Interaction Patterns**
- **Progressive Disclosure**: Show details on demand
- **Contextual Actions**: Relevant actions based on job status
- **Keyboard Shortcuts**: Power user efficiency
- **Touch Gestures**: Mobile-friendly interactions

## 🚀 Future Enhancements

### **Planned Features**
- **VR Support**: Virtual reality pipeline exploration
- **AI Insights**: Intelligent failure prediction
- **Collaborative Editing**: Multi-user pipeline design
- **Time Travel**: Historical pipeline state visualization
- **Performance Profiling**: Detailed execution analysis

### **Integration Roadmap**
- **Kubernetes**: Native K8s job visualization
- **Docker**: Container-based pipeline steps
- **Cloud Providers**: AWS, GCP, Azure integration
- **Monitoring Tools**: Datadog, New Relic, Splunk

## 📚 Documentation

- **API Reference**: Complete endpoint documentation
- **Component Library**: Reusable React components
- **Theming Guide**: Custom theme creation
- **Plugin Development**: Extending functionality
- **Performance Tuning**: Optimization best practices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
