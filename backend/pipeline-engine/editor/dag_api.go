package editor

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"chainops-backend/pipeline"
)

// DAGAPIHandler handles HTTP requests for pipeline DAG operations
type DAGAPIHandler struct {
	// In a real implementation, these would be injected dependencies
	pipelineStore map[string]*pipeline.Pipeline
	executionStore map[string]*PipelineExecution
}

// NewDAGAPIHandler creates a new DAG API handler
func NewDAGAPIHandler() *DAGAPIHandler {
	return &DAGAPIHandler{
		pipelineStore:  make(map[string]*pipeline.Pipeline),
		executionStore: make(map[string]*PipelineExecution),
	}
}

// GetPipelineDAG returns the DAG representation of a pipeline
func (h *DAGAPIHandler) GetPipelineDAG(w http.ResponseWriter, r *http.Request) {
	pipelineID := r.URL.Query().Get("id")
	runID := r.URL.Query().Get("run_id")
	layout := r.URL.Query().Get("layout")
	
	if pipelineID == "" {
		http.Error(w, "pipeline ID is required", http.StatusBadRequest)
		return
	}

	// Get pipeline execution or create demo data
	var dag *PipelineDAG
	var err error
	
	if runID != "" {
		dag, err = h.getPipelineExecutionDAG(pipelineID, runID)
	} else {
		dag, err = h.getDemoPipelineDAG(pipelineID)
	}
	
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get pipeline DAG: %v", err), http.StatusInternalServerError)
		return
	}

	// Apply layout if specified
	if layout != "" {
		dag = h.applyLayout(dag, layout)
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	
	json.NewEncoder(w).Encode(dag)
}

// GetPipelineStatus returns real-time status of a pipeline execution
func (h *DAGAPIHandler) GetPipelineStatus(w http.ResponseWriter, r *http.Request) {
	runID := r.URL.Query().Get("run_id")
	
	if runID == "" {
		http.Error(w, "run ID is required", http.StatusBadRequest)
		return
	}

	execution, exists := h.executionStore[runID]
	if !exists {
		http.Error(w, "pipeline execution not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(w).Encode(execution)
}

// ValidatePipeline validates a pipeline configuration
func (h *DAGAPIHandler) ValidatePipeline(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var pipelineData map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&pipelineData); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Convert to YAML and validate
	yamlData, err := json.Marshal(pipelineData)
	if err != nil {
		http.Error(w, "Failed to process pipeline data", http.StatusInternalServerError)
		return
	}

	p, err := pipeline.ParsePipelineYAMLBytes(yamlData)
	if err != nil {
		result := ValidationResult{
			Valid: false,
			Errors: []ValidationError{
				{
					Type:    "error",
					Code:    "PARSE_ERROR",
					Message: err.Error(),
				},
			},
		}
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		json.NewEncoder(w).Encode(result)
		return
	}

	// Build DAG to validate structure
	_, err = pipeline.BuildDAG(p)
	if err != nil {
		result := ValidationResult{
			Valid: false,
			Errors: []ValidationError{
				{
					Type:    "error",
					Code:    "DAG_ERROR",
					Message: err.Error(),
				},
			},
		}
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		json.NewEncoder(w).Encode(result)
		return
	}

	result := ValidationResult{Valid: true}
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(w).Encode(result)
}

// getDemoPipelineDAG creates a demo pipeline DAG for visualization
func (h *DAGAPIHandler) getDemoPipelineDAG(pipelineID string) (*PipelineDAG, error) {
	now := time.Now()
	
	dag := &PipelineDAG{
		ID:      pipelineID,
		Name:    "ChainOps Demo Pipeline",
		Version: "1.0",
		Status:  "running",
		StartTime: &now,
		TriggerInfo: TriggerInfo{
			Type:      "push",
			User:      "developer",
			Commit:    "abc123def456",
			Branch:    "main",
			Message:   "Add new feature",
			Timestamp: now,
		},
		Nodes: []DAGNode{
			{
				ID:     "setup",
				Label:  "Setup",
				Type:   "job",
				Status: "success",
				Position: Position{X: 0, Y: 0, Z: 0},
				Metadata: map[string]string{
					"runner": "ubuntu-latest",
				},
				StartTime: &now,
				EndTime:   timePtr(now.Add(30 * time.Second)),
				Duration:  30000,
				RunsOn:    "ubuntu-latest",
				Steps: []StepInfo{
					{Name: "Checkout", Status: "success", Duration: 5000},
					{Name: "Setup Node", Status: "success", Duration: 25000},
				},
			},
			{
				ID:     "lint",
				Label:  "Lint Code",
				Type:   "job",
				Status: "success",
				Position: Position{X: 200, Y: 0, Z: 0},
				StartTime: timePtr(now.Add(30 * time.Second)),
				EndTime:   timePtr(now.Add(45 * time.Second)),
				Duration:  15000,
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "test-unit",
				Label:  "Unit Tests",
				Type:   "job",
				Status: "success",
				Position: Position{X: 400, Y: -50, Z: 0},
				StartTime: timePtr(now.Add(45 * time.Second)),
				EndTime:   timePtr(now.Add(90 * time.Second)),
				Duration:  45000,
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "test-integration",
				Label:  "Integration Tests",
				Type:   "job",
				Status: "running",
				Position: Position{X: 400, Y: 50, Z: 0},
				StartTime: timePtr(now.Add(45 * time.Second)),
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "build",
				Label:  "Build",
				Type:   "job",
				Status: "pending",
				Position: Position{X: 600, Y: 0, Z: 0},
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "security-scan",
				Label:  "Security Scan",
				Type:   "job",
				Status: "pending",
				Position: Position{X: 800, Y: 0, Z: 0},
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "deploy-staging",
				Label:  "Deploy Staging",
				Type:   "job",
				Status: "pending",
				Position: Position{X: 1000, Y: -50, Z: 0},
				Environment: "staging",
				RunsOn:    "ubuntu-latest",
			},
			{
				ID:     "deploy-production",
				Label:  "Deploy Production",
				Type:   "job",
				Status: "pending",
				Position: Position{X: 1000, Y: 50, Z: 0},
				Environment: "production",
				RunsOn:    "ubuntu-latest",
			},
		},
		Edges: []DAGEdge{
			{ID: "setup-lint", From: "setup", To: "lint", Type: "dependency"},
			{ID: "lint-test-unit", From: "lint", To: "test-unit", Type: "dependency"},
			{ID: "lint-test-integration", From: "lint", To: "test-integration", Type: "dependency"},
			{ID: "test-unit-build", From: "test-unit", To: "build", Type: "dependency"},
			{ID: "test-integration-build", From: "test-integration", To: "build", Type: "dependency"},
			{ID: "build-security", From: "build", To: "security-scan", Type: "dependency"},
			{ID: "security-staging", From: "security-scan", To: "deploy-staging", Type: "dependency"},
			{ID: "security-production", From: "security-scan", To: "deploy-production", Type: "dependency"},
		},
		Stats: PipelineStats{
			TotalJobs:      8,
			SuccessJobs:    2,
			FailedJobs:     0,
			SkippedJobs:    0,
			RunningJobs:    1,
			PendingJobs:    5,
			TotalLevels:    5,
			MaxParallelism: 2,
			MatrixJobs:     0,
		},
	}

	return dag, nil
}

// getPipelineExecutionDAG gets DAG for a specific pipeline execution
func (h *DAGAPIHandler) getPipelineExecutionDAG(pipelineID, runID string) (*PipelineDAG, error) {
	execution, exists := h.executionStore[runID]
	if !exists {
		// Return demo data if execution not found
		return h.getDemoPipelineDAG(pipelineID)
	}
	
	return &execution.DAG, nil
}

// applyLayout applies automatic layout to the DAG
func (h *DAGAPIHandler) applyLayout(dag *PipelineDAG, layoutType string) *PipelineDAG {
	switch layoutType {
	case "horizontal":
		return h.applyHorizontalLayout(dag)
	case "vertical":
		return h.applyVerticalLayout(dag)
	case "circular":
		return h.applyCircularLayout(dag)
	default:
		return dag
	}
}

// applyHorizontalLayout applies horizontal layout
func (h *DAGAPIHandler) applyHorizontalLayout(dag *PipelineDAG) *PipelineDAG {
	// Simple horizontal layout - in production, use dagre or similar
	levels := h.calculateLevels(dag)
	
	for i, node := range dag.Nodes {
		level := levels[node.ID]
		dag.Nodes[i].Position.X = float64(level * 200)
		dag.Nodes[i].Position.Y = float64((i % 3) * 100) // Simple vertical distribution
	}
	
	return dag
}

// applyVerticalLayout applies vertical layout
func (h *DAGAPIHandler) applyVerticalLayout(dag *PipelineDAG) *PipelineDAG {
	levels := h.calculateLevels(dag)
	
	for i, node := range dag.Nodes {
		level := levels[node.ID]
		dag.Nodes[i].Position.X = float64((i % 3) * 100)
		dag.Nodes[i].Position.Y = float64(level * 200)
	}
	
	return dag
}

// applyCircularLayout applies circular layout
func (h *DAGAPIHandler) applyCircularLayout(dag *PipelineDAG) *PipelineDAG {
	import "math"

	nodeCount := len(dag.Nodes)
	radius := float64(200)

	for i := range dag.Nodes {
		angle := 2 * math.Pi * float64(i) / float64(nodeCount)
		dag.Nodes[i].Position.X = radius * math.Cos(angle)
		dag.Nodes[i].Position.Y = radius * math.Sin(angle)
	}

	return dag
}

// calculateLevels calculates the level of each node in the DAG
func (h *DAGAPIHandler) calculateLevels(dag *PipelineDAG) map[string]int {
	levels := make(map[string]int)
	
	// Simple level calculation - in production, use proper topological sort
	for _, node := range dag.Nodes {
		levels[node.ID] = 0
	}
	
	// Calculate levels based on dependencies
	for _, edge := range dag.Edges {
		if levels[edge.To] <= levels[edge.From] {
			levels[edge.To] = levels[edge.From] + 1
		}
	}
	
	return levels
}

// timePtr returns a pointer to a time value
func timePtr(t time.Time) *time.Time {
	return &t
}

// SetupRoutes sets up HTTP routes for the DAG API
func (h *DAGAPIHandler) SetupRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/api/editor/pipeline/dag", h.GetPipelineDAG)
	mux.HandleFunc("/api/editor/pipeline/status", h.GetPipelineStatus)
	mux.HandleFunc("/api/editor/pipeline/validate", h.ValidatePipeline)
	
	// CORS preflight handler
	mux.HandleFunc("/api/editor/", func(w http.ResponseWriter, r *http.Request) {
		if r.Method == "OPTIONS" {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
			w.WriteHeader(http.StatusOK)
			return
		}
	})
}
