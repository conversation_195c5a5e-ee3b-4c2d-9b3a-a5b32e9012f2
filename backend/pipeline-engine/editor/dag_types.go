package editor

import (
	"time"
)

// DAGNode represents a node in the pipeline visualization
type DAGNode struct {
	ID          string            `json:"id"`
	Label       string            `json:"label"`
	Type        string            `json:"type"`     // "stage", "job", "matrix", "parallel"
	Status      string            `json:"status"`   // "pending", "running", "success", "error", "unstable", "skipped"
	Children    []string          `json:"children"` // for nested/parallel stages
	Parent      string            `json:"parent,omitempty"`
	Position    Position          `json:"position"`
	Metadata    map[string]string `json:"metadata"`
	StartTime   *time.Time        `json:"start_time,omitempty"`
	EndTime     *time.Time        `json:"end_time,omitempty"`
	Duration    int64             `json:"duration,omitempty"` // milliseconds
	LogURL      string            `json:"log_url,omitempty"`
	ArtifactURL string            `json:"artifact_url,omitempty"`
	Environment string            `json:"environment,omitempty"`
	RunsOn      string            `json:"runs_on,omitempty"`
	Steps       []StepInfo        `json:"steps,omitempty"`
}

// Position represents node position in 2D/3D space
type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z,omitempty"` // for 3D visualization
}

// StepInfo represents individual step information
type StepInfo struct {
	Name     string     `json:"name"`
	Status   string     `json:"status"`
	Duration int64      `json:"duration,omitempty"`
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	LogURL   string     `json:"log_url,omitempty"`
}

// DAGEdge represents a connection between nodes
type DAGEdge struct {
	ID     string `json:"id"`
	From   string `json:"from"`
	To     string `json:"to"`
	Type   string `json:"type"`   // "dependency", "parallel", "conditional"
	Label  string `json:"label,omitempty"`
	Style  EdgeStyle `json:"style,omitempty"`
}

// EdgeStyle defines visual properties of edges
type EdgeStyle struct {
	Color     string `json:"color,omitempty"`
	Width     int    `json:"width,omitempty"`
	Animated  bool   `json:"animated,omitempty"`
	Dashed    bool   `json:"dashed,omitempty"`
}

// PipelineDAG represents the complete pipeline visualization data
type PipelineDAG struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Status      string            `json:"status"`
	Nodes       []DAGNode         `json:"nodes"`
	Edges       []DAGEdge         `json:"edges"`
	Metadata    map[string]string `json:"metadata"`
	StartTime   *time.Time        `json:"start_time,omitempty"`
	EndTime     *time.Time        `json:"end_time,omitempty"`
	Duration    int64             `json:"duration,omitempty"`
	TriggerInfo TriggerInfo       `json:"trigger_info"`
	Stats       PipelineStats     `json:"stats"`
}

// TriggerInfo contains information about what triggered the pipeline
type TriggerInfo struct {
	Type      string    `json:"type"`       // "push", "pull_request", "manual", "schedule"
	User      string    `json:"user,omitempty"`
	Commit    string    `json:"commit,omitempty"`
	Branch    string    `json:"branch,omitempty"`
	Message   string    `json:"message,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// PipelineStats contains pipeline execution statistics
type PipelineStats struct {
	TotalJobs      int `json:"total_jobs"`
	SuccessJobs    int `json:"success_jobs"`
	FailedJobs     int `json:"failed_jobs"`
	SkippedJobs    int `json:"skipped_jobs"`
	RunningJobs    int `json:"running_jobs"`
	PendingJobs    int `json:"pending_jobs"`
	TotalLevels    int `json:"total_levels"`
	MaxParallelism int `json:"max_parallelism"`
	MatrixJobs     int `json:"matrix_jobs"`
}

// PipelineExecution represents a single pipeline run
type PipelineExecution struct {
	ID          string       `json:"id"`
	PipelineID  string       `json:"pipeline_id"`
	RunNumber   int          `json:"run_number"`
	Status      string       `json:"status"`
	DAG         PipelineDAG  `json:"dag"`
	StartTime   time.Time    `json:"start_time"`
	EndTime     *time.Time   `json:"end_time,omitempty"`
	Duration    int64        `json:"duration,omitempty"`
	TriggerInfo TriggerInfo  `json:"trigger_info"`
	JobResults  []JobResult  `json:"job_results"`
}

// JobResult represents the result of a single job execution
type JobResult struct {
	JobID      string     `json:"job_id"`
	Status     string     `json:"status"`
	StartTime  time.Time  `json:"start_time"`
	EndTime    *time.Time `json:"end_time,omitempty"`
	Duration   int64      `json:"duration,omitempty"`
	ExitCode   int        `json:"exit_code,omitempty"`
	LogURL     string     `json:"log_url,omitempty"`
	ArtifactURL string    `json:"artifact_url,omitempty"`
	ErrorMessage string   `json:"error_message,omitempty"`
	StepResults []StepResult `json:"step_results"`
}

// StepResult represents the result of a single step execution
type StepResult struct {
	StepName     string     `json:"step_name"`
	Status       string     `json:"status"`
	StartTime    time.Time  `json:"start_time"`
	EndTime      *time.Time `json:"end_time,omitempty"`
	Duration     int64      `json:"duration,omitempty"`
	ExitCode     int        `json:"exit_code,omitempty"`
	Output       string     `json:"output,omitempty"`
	ErrorMessage string     `json:"error_message,omitempty"`
}

// LayoutOptions defines options for automatic layout generation
type LayoutOptions struct {
	Direction   string  `json:"direction"`   // "LR", "TB", "RL", "BT"
	NodeSpacing float64 `json:"node_spacing"`
	RankSpacing float64 `json:"rank_spacing"`
	EdgeSpacing float64 `json:"edge_spacing"`
	Algorithm   string  `json:"algorithm"`   // "dagre", "elk", "manual"
}

// ViewportOptions defines viewport settings for the visualization
type ViewportOptions struct {
	Zoom      float64  `json:"zoom"`
	Center    Position `json:"center"`
	FitView   bool     `json:"fit_view"`
	MinZoom   float64  `json:"min_zoom"`
	MaxZoom   float64  `json:"max_zoom"`
}

// VisualizationConfig contains configuration for the pipeline visualization
type VisualizationConfig struct {
	Layout         LayoutOptions   `json:"layout"`
	Viewport       ViewportOptions `json:"viewport"`
	Theme          string          `json:"theme"`          // "light", "dark"
	ShowMinimap    bool            `json:"show_minimap"`
	ShowControls   bool            `json:"show_controls"`
	ShowBackground bool            `json:"show_background"`
	RealTimeUpdate bool            `json:"real_time_update"`
	UpdateInterval int             `json:"update_interval"` // seconds
}

// NodeFilter defines filters for node visibility
type NodeFilter struct {
	Status      []string `json:"status,omitempty"`
	Type        []string `json:"type,omitempty"`
	Environment []string `json:"environment,omitempty"`
	Tags        []string `json:"tags,omitempty"`
}

// PipelineQuery defines query parameters for pipeline data
type PipelineQuery struct {
	PipelineID string     `json:"pipeline_id"`
	RunID      string     `json:"run_id,omitempty"`
	Filter     NodeFilter `json:"filter,omitempty"`
	Layout     string     `json:"layout,omitempty"`
	Format     string     `json:"format,omitempty"` // "json", "yaml", "dot"
}

// WebSocketMessage represents real-time updates
type WebSocketMessage struct {
	Type      string      `json:"type"`      // "node_update", "edge_update", "pipeline_status"
	Timestamp time.Time   `json:"timestamp"`
	Data      interface{} `json:"data"`
}

// NodeUpdate represents a real-time node status update
type NodeUpdate struct {
	NodeID   string     `json:"node_id"`
	Status   string     `json:"status"`
	Progress float64    `json:"progress,omitempty"` // 0-100
	Message  string     `json:"message,omitempty"`
	EndTime  *time.Time `json:"end_time,omitempty"`
}

// PipelineTemplate represents a reusable pipeline template
type PipelineTemplate struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Category    string            `json:"category"`
	Tags        []string          `json:"tags"`
	Template    PipelineDAG       `json:"template"`
	Variables   map[string]string `json:"variables"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// ValidationResult represents pipeline validation results
type ValidationResult struct {
	Valid    bool              `json:"valid"`
	Errors   []ValidationError `json:"errors,omitempty"`
	Warnings []ValidationError `json:"warnings,omitempty"`
}

// ValidationError represents a validation error or warning
type ValidationError struct {
	Type     string `json:"type"`     // "error", "warning"
	Code     string `json:"code"`
	Message  string `json:"message"`
	NodeID   string `json:"node_id,omitempty"`
	Line     int    `json:"line,omitempty"`
	Column   int    `json:"column,omitempty"`
}

// ExportFormat defines supported export formats
type ExportFormat struct {
	Format      string `json:"format"`      // "png", "svg", "pdf", "json", "yaml"
	Quality     int    `json:"quality,omitempty"`
	Width       int    `json:"width,omitempty"`
	Height      int    `json:"height,omitempty"`
	Transparent bool   `json:"transparent,omitempty"`
}
