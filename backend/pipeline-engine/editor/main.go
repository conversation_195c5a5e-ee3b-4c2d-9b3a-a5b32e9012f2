package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"chainops-backend/editor"
)

func main() {
	// Configuration
	port := getEnv("PORT", "8081")
	
	log.Printf("Starting ChainOps Pipeline Editor API on port %s", port)
	
	// Initialize DAG API handler
	dagHandler := editor.NewDAGAPIHandler()
	
	// Set up HTTP routes
	mux := http.NewServeMux()
	
	// API routes
	dagHandler.SetupRoutes(mux)
	
	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.<PERSON>er().Set("Access-Control-Allow-Origin", "*")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status":"healthy","timestamp":"%s","service":"pipeline-editor"}`, time.Now().Format(time.RFC3339))
	})
	
	// Static file serving for frontend (if needed)
	mux.Handle("/", http.FileServer(http.Dir("./frontend/dist/")))
	
	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}
	
	// Start server in a goroutine
	go func() {
		log.Printf("Pipeline Editor API listening on :%s", port)
		log.Println("Endpoints:")
		log.Println("  GET  /api/editor/pipeline/dag?id=<pipeline_id> - Get pipeline DAG")
		log.Println("  GET  /api/editor/pipeline/status?run_id=<run_id> - Get pipeline status")
		log.Println("  POST /api/editor/pipeline/validate - Validate pipeline")
		log.Println("  GET  /health - Health check")
		
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()
	
	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	log.Println("Shutting down pipeline editor API...")
	
	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}
	
	log.Println("Pipeline editor API stopped")
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
