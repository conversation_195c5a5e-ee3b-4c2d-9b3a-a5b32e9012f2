name: ChainOps CI/CD Pipeline
version: "1.0"

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options: ['staging', 'production']

env:
  NODE_VERSION: "18"
  GO_VERSION: "1.21"

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Install dependencies
        script:
          - npm ci
      - name: Run ESLint
        script:
          - npm run lint
      - name: Run Prettier
        script:
          - npm run format:check

  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: [lint]
    strategy:
      matrix:
        browser: [chrome, firefox, safari]
        node-version: [16, 18, 20]
      fail-fast: false
      max-parallel: 3
    env:
      BROWSER: ${{ matrix.browser }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        script:
          - npm ci
      - name: Run tests
        script:
          - npm run test:unit
          - npm run test:e2e
        env:
          CI: true

  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: [lint]
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: chainops_test
        ports:
          - 5432:5432
      redis:
        image: redis:7
        ports:
          - 6379:6379
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Install dependencies
        script:
          - go mod download
      - name: Run tests
        script:
          - go test ./...
          - go test -race ./...
        env:
          DATABASE_URL: postgres://postgres:postgres@localhost:5432/chainops_test
          REDIS_URL: redis://localhost:6379

  build-frontend:
    name: Build Frontend
    runs-on: ubuntu-latest
    needs: [test-frontend]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Install dependencies
        script:
          - npm ci
      - name: Build application
        script:
          - npm run build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: frontend-build
          path: dist/

  build-backend:
    name: Build Backend
    runs-on: ubuntu-latest
    needs: [test-backend]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Build binary
        script:
          - go build -o chainops-backend ./cmd/server
      - name: Build Docker image
        script:
          - docker build -t chainops-backend:${{ github.sha }} .
      - name: Upload binary
        uses: actions/upload-artifact@v3
        with:
          name: backend-binary
          path: chainops-backend

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: [build-frontend, build-backend]
    strategy:
      matrix:
        scan-type: [sast, dependency, container]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run security scan
        script:
          - echo "Running ${{ matrix.scan-type }} security scan"
          - |
            case "${{ matrix.scan-type }}" in
              sast)
                echo "Running SAST scan with CodeQL"
                ;;
              dependency)
                echo "Running dependency scan with Snyk"
                ;;
              container)
                echo "Running container scan with Trivy"
                ;;
            esac

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [security-scan]
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.chainops.dev
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: frontend-build
          path: ./dist
      - name: Download backend binary
        uses: actions/download-artifact@v3
        with:
          name: backend-binary
          path: ./bin
      - name: Deploy to staging
        script:
          - echo "Deploying to staging environment"
          - kubectl apply -f k8s/staging/
          - kubectl rollout status deployment/chainops-frontend -n staging
          - kubectl rollout status deployment/chainops-backend -n staging
        env:
          KUBECONFIG: ${{ secrets.STAGING_KUBECONFIG }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [security-scan]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://chainops.dev
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: frontend-build
          path: ./dist
      - name: Download backend binary
        uses: actions/download-artifact@v3
        with:
          name: backend-binary
          path: ./bin
      - name: Deploy to production
        script:
          - echo "Deploying to production environment"
          - kubectl apply -f k8s/production/
          - kubectl rollout status deployment/chainops-frontend -n production
          - kubectl rollout status deployment/chainops-backend -n production
        env:
          KUBECONFIG: ${{ secrets.PRODUCTION_KUBECONFIG }}

  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Send Slack notification
        script:
          - |
            if [ "${{ needs.deploy-staging.result }}" == "success" ] || [ "${{ needs.deploy-production.result }}" == "success" ]; then
              echo "Sending success notification to Slack"
            else
              echo "Sending failure notification to Slack"
            fi
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
