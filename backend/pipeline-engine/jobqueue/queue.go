package jobqueue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// Job represents a job in the queue
type Job struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Tags     []string          `json:"tags"`
	Payload  map[string]string `json:"payload"`
	Status   string            `json:"status"`
	Priority int               `json:"priority"`
	Retry    int               `json:"retry"`
	MaxRetry int               `json:"max_retry"`
	Created  time.Time         `json:"created"`
	Started  *time.Time        `json:"started,omitempty"`
	Finished *time.Time        `json:"finished,omitempty"`
}

// JobQueue manages job queuing with Redis
type JobQueue struct {
	client    *redis.Client
	queueName string
}

// NewJobQueue creates a new job queue instance
func NewJobQueue(redisAddr, queueName string) *JobQueue {
	rdb := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: "", // no password
		DB:       0,  // default DB
	})

	return &JobQueue{
		client:    rdb,
		queueName: queueName,
	}
}

// Enqueue adds a job to the queue
func (jq *JobQueue) Enqueue(ctx context.Context, job *Job) error {
	job.Created = time.Now()
	job.Status = "pending"
	
	if job.MaxRetry == 0 {
		job.MaxRetry = 3
	}

	jobData, err := json.Marshal(job)
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}

	// Add to priority queue (higher priority = lower score)
	score := float64(time.Now().Unix()) - float64(job.Priority*1000)
	
	err = jq.client.ZAdd(ctx, jq.queueName, &redis.Z{
		Score:  score,
		Member: string(jobData),
	}).Err()
	
	if err != nil {
		return fmt.Errorf("failed to enqueue job: %w", err)
	}

	// Store job details in hash for quick lookup
	err = jq.client.HSet(ctx, fmt.Sprintf("%s:jobs", jq.queueName), job.ID, string(jobData)).Err()
	if err != nil {
		return fmt.Errorf("failed to store job details: %w", err)
	}

	return nil
}

// Dequeue removes and returns the next job from the queue
func (jq *JobQueue) Dequeue(ctx context.Context) (*Job, error) {
	// Get the job with the lowest score (highest priority, oldest first)
	result, err := jq.client.ZPopMin(ctx, jq.queueName, 1).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // No jobs available
		}
		return nil, fmt.Errorf("failed to dequeue job: %w", err)
	}

	if len(result) == 0 {
		return nil, nil // No jobs available
	}

	var job Job
	err = json.Unmarshal([]byte(result[0].Member.(string)), &job)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal job: %w", err)
	}

	// Update job status
	now := time.Now()
	job.Status = "running"
	job.Started = &now

	// Update job in hash
	jobData, _ := json.Marshal(job)
	jq.client.HSet(ctx, fmt.Sprintf("%s:jobs", jq.queueName), job.ID, string(jobData))

	return &job, nil
}

// UpdateJobStatus updates the status of a job
func (jq *JobQueue) UpdateJobStatus(ctx context.Context, jobID, status string) error {
	jobKey := fmt.Sprintf("%s:jobs", jq.queueName)
	
	// Get current job data
	jobData, err := jq.client.HGet(ctx, jobKey, jobID).Result()
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	var job Job
	err = json.Unmarshal([]byte(jobData), &job)
	if err != nil {
		return fmt.Errorf("failed to unmarshal job: %w", err)
	}

	// Update status
	job.Status = status
	if status == "completed" || status == "failed" {
		now := time.Now()
		job.Finished = &now
	}

	// Save updated job
	updatedData, err := json.Marshal(job)
	if err != nil {
		return fmt.Errorf("failed to marshal updated job: %w", err)
	}

	err = jq.client.HSet(ctx, jobKey, jobID, string(updatedData)).Err()
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}

	return nil
}

// GetJob retrieves a job by ID
func (jq *JobQueue) GetJob(ctx context.Context, jobID string) (*Job, error) {
	jobData, err := jq.client.HGet(ctx, fmt.Sprintf("%s:jobs", jq.queueName), jobID).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get job: %w", err)
	}

	var job Job
	err = json.Unmarshal([]byte(jobData), &job)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal job: %w", err)
	}

	return &job, nil
}

// GetQueueStats returns queue statistics
func (jq *JobQueue) GetQueueStats(ctx context.Context) (map[string]int64, error) {
	stats := make(map[string]int64)

	// Count pending jobs
	pending, err := jq.client.ZCard(ctx, jq.queueName).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to count pending jobs: %w", err)
	}
	stats["pending"] = pending

	// Count total jobs
	total, err := jq.client.HLen(ctx, fmt.Sprintf("%s:jobs", jq.queueName)).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to count total jobs: %w", err)
	}
	stats["total"] = total

	// Calculate running jobs (total - pending - completed/failed)
	stats["running"] = total - pending

	return stats, nil
}

// Close closes the Redis connection
func (jq *JobQueue) Close() error {
	return jq.client.Close()
}

// Worker represents a job worker
type Worker struct {
	ID       string
	Queue    *JobQueue
	Handler  func(context.Context, *Job) error
	StopChan chan bool
}

// NewWorker creates a new worker
func NewWorker(id string, queue *JobQueue, handler func(context.Context, *Job) error) *Worker {
	return &Worker{
		ID:       id,
		Queue:    queue,
		Handler:  handler,
		StopChan: make(chan bool),
	}
}

// Start starts the worker
func (w *Worker) Start(ctx context.Context) {
	fmt.Printf("Worker %s started\n", w.ID)
	
	for {
		select {
		case <-w.StopChan:
			fmt.Printf("Worker %s stopped\n", w.ID)
			return
		case <-ctx.Done():
			fmt.Printf("Worker %s stopped due to context cancellation\n", w.ID)
			return
		default:
			job, err := w.Queue.Dequeue(ctx)
			if err != nil {
				fmt.Printf("Worker %s: Error dequeuing job: %v\n", w.ID, err)
				time.Sleep(time.Second)
				continue
			}

			if job == nil {
				// No jobs available, wait a bit
				time.Sleep(time.Second)
				continue
			}

			fmt.Printf("Worker %s: Processing job %s\n", w.ID, job.ID)

			// Process the job
			err = w.Handler(ctx, job)
			if err != nil {
				fmt.Printf("Worker %s: Job %s failed: %v\n", w.ID, job.ID, err)
				w.Queue.UpdateJobStatus(ctx, job.ID, "failed")
			} else {
				fmt.Printf("Worker %s: Job %s completed\n", w.ID, job.ID)
				w.Queue.UpdateJobStatus(ctx, job.ID, "completed")
			}
		}
	}
}

// Stop stops the worker
func (w *Worker) Stop() {
	w.StopChan <- true
}
