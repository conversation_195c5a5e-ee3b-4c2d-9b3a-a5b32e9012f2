package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"chainops-backend/jobqueue"
	"chainops-backend/pipeline"
)

func main() {
	// Check if pipeline file is provided
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run main.go <pipeline.yml>")
	}

	pipelineFile := os.Args[1]
	
	// Parse pipeline YAML
	fmt.Printf("Parsing pipeline file: %s\n", pipelineFile)
	p, err := pipeline.ParsePipelineYAML(pipelineFile)
	if err != nil {
		log.Fatalf("Failed to parse pipeline: %v", err)
	}
	
	fmt.Printf("Pipeline '%s' parsed successfully!\n", p.Name)
	fmt.Printf("Jobs found: %d\n", len(p.Jobs))
	
	// Build DAG
	fmt.Println("\nBuilding DAG...")
	dag, err := pipeline.BuildDAG(p)
	if err != nil {
		log.Fatalf("Failed to build DAG: %v", err)
	}
	
	fmt.Println("DAG built successfully!")
	
	// Print DAG statistics
	stats := dag.GetDAGStats()
	fmt.Printf("\nDAG Statistics:\n")
	fmt.Printf("  Total Jobs: %v\n", stats["total_jobs"])
	fmt.Printf("  Total Levels: %v\n", stats["total_levels"])
	fmt.Printf("  Max Parallelism: %v\n", stats["max_parallelism"])
	fmt.Printf("  Critical Path Length: %v\n", stats["critical_path_length"])
	fmt.Printf("  Matrix Jobs: %v\n", stats["matrix_jobs"])
	
	// Print execution plan
	fmt.Printf("\nExecution Plan:\n")
	executionPlan := dag.GetExecutionPlan()
	for level, jobs := range executionPlan {
		fmt.Printf("  Level %d: %v\n", level, jobs)
	}
	
	// Print critical path
	criticalPath := dag.GetCriticalPath()
	fmt.Printf("\nCritical Path: %v\n", criticalPath)
	
	// Initialize job queue
	fmt.Println("\nInitializing job queue...")
	queue := jobqueue.NewJobQueue("localhost:6379", "chainops-jobs")
	defer queue.Close()
	
	// Test queue connection
	ctx := context.Background()
	stats_queue, err := queue.GetQueueStats(ctx)
	if err != nil {
		log.Printf("Warning: Could not connect to Redis queue: %v", err)
		fmt.Println("Simulating job scheduling without Redis...")
		simulateJobScheduling(dag)
		return
	}
	
	fmt.Printf("Queue connected. Current stats: %+v\n", stats_queue)
	
	// Schedule jobs using the DAG
	fmt.Println("\nScheduling jobs...")
	err = scheduleJobs(ctx, dag, queue)
	if err != nil {
		log.Fatalf("Failed to schedule jobs: %v", err)
	}
	
	fmt.Println("\nAll jobs scheduled successfully!")
	
	// Start a worker to process jobs (for demonstration)
	fmt.Println("\nStarting worker to process jobs...")
	worker := jobqueue.NewWorker("worker-1", queue, jobHandler)
	
	// Start worker in a goroutine
	go worker.Start(ctx)
	
	// Wait for jobs to complete or timeout
	fmt.Println("Waiting for jobs to complete (30 seconds timeout)...")
	time.Sleep(30 * time.Second)
	
	// Stop worker
	worker.Stop()
	
	// Final queue stats
	finalStats, _ := queue.GetQueueStats(ctx)
	fmt.Printf("\nFinal queue stats: %+v\n", finalStats)
}

// scheduleJobs schedules all jobs in the DAG to the queue
func scheduleJobs(ctx context.Context, dag *pipeline.DAG, queue *jobqueue.JobQueue) error {
	completed := make(map[string]bool)
	scheduled := make(map[string]bool)
	
	for {
		// Get ready jobs
		readyJobs := dag.ReadyJobs(completed)
		
		// Filter out already scheduled jobs
		var toSchedule []string
		for _, jobName := range readyJobs {
			if !scheduled[jobName] {
				toSchedule = append(toSchedule, jobName)
			}
		}
		
		if len(toSchedule) == 0 {
			// Check if all jobs are completed
			if len(completed) == len(dag.Nodes) {
				break // All jobs completed
			}
			
			// Check if any jobs are scheduled but not completed
			hasScheduled := false
			for jobName := range scheduled {
				if !completed[jobName] {
					hasScheduled = true
					break
				}
			}
			
			if !hasScheduled {
				return fmt.Errorf("no jobs ready to schedule and none in progress")
			}
			
			// Wait a bit for jobs to complete
			time.Sleep(time.Second)
			continue
		}
		
		// Schedule ready jobs
		for _, jobName := range toSchedule {
			node := dag.Nodes[jobName]
			
			// Handle matrix jobs
			if len(node.MatrixJobs) > 0 {
				for _, matrixJob := range node.MatrixJobs {
					job := &jobqueue.Job{
						ID:       matrixJob.ID,
						Name:     matrixJob.Job.Name,
						Tags:     []string{"matrix", jobName},
						Payload:  createJobPayload(&matrixJob.Job, matrixJob.MatrixValues),
						Status:   "pending",
						Priority: node.Level, // Higher level = lower priority
					}
					
					err := queue.Enqueue(ctx, job)
					if err != nil {
						return fmt.Errorf("failed to enqueue matrix job %s: %w", matrixJob.ID, err)
					}
					
					fmt.Printf("Scheduled matrix job: %s (level %d)\n", matrixJob.ID, node.Level)
				}
			} else {
				// Regular job
				job := &jobqueue.Job{
					ID:       jobName,
					Name:     node.Job.Name,
					Tags:     []string{jobName},
					Payload:  createJobPayload(node.Job, nil),
					Status:   "pending",
					Priority: node.Level,
				}
				
				err := queue.Enqueue(ctx, job)
				if err != nil {
					return fmt.Errorf("failed to enqueue job %s: %w", jobName, err)
				}
				
				fmt.Printf("Scheduled job: %s (level %d)\n", jobName, node.Level)
			}
			
			scheduled[jobName] = true
		}
		
		// Simulate job completion for demo
		// In a real implementation, this would be handled by job status updates
		for _, jobName := range toSchedule {
			completed[jobName] = true
		}
	}
	
	return nil
}

// createJobPayload creates a payload for a job
func createJobPayload(job *pipeline.Job, matrixValues map[string]interface{}) map[string]string {
	payload := make(map[string]string)
	
	// Add job configuration
	payload["job_name"] = job.Name
	payload["runs_on"] = fmt.Sprintf("%v", job.GetRunsOn())
	
	// Add steps
	for i, step := range job.Steps {
		stepKey := fmt.Sprintf("step_%d", i)
		if len(step.Script) > 0 {
			payload[stepKey] = fmt.Sprintf("script: %v", step.Script)
		} else if step.Uses != "" {
			payload[stepKey] = fmt.Sprintf("uses: %s", step.Uses)
		}
	}
	
	// Add matrix values if present
	if matrixValues != nil {
		for key, value := range matrixValues {
			payload[fmt.Sprintf("matrix_%s", key)] = fmt.Sprintf("%v", value)
		}
	}
	
	// Add environment variables
	if job.Env != nil {
		for key, value := range job.Env {
			payload[fmt.Sprintf("env_%s", key)] = value
		}
	}
	
	return payload
}

// jobHandler processes a job (for demonstration)
func jobHandler(ctx context.Context, job *jobqueue.Job) error {
	fmt.Printf("Processing job: %s\n", job.ID)
	
	// Simulate job execution time
	executionTime := time.Duration(1+job.Priority) * time.Second
	time.Sleep(executionTime)
	
	// Simulate random failures (10% chance)
	if time.Now().UnixNano()%10 == 0 {
		return fmt.Errorf("simulated job failure")
	}
	
	fmt.Printf("Job %s completed successfully\n", job.ID)
	return nil
}

// simulateJobScheduling simulates job scheduling without Redis
func simulateJobScheduling(dag *pipeline.DAG) {
	fmt.Println("Simulating job scheduling...")
	
	completed := make(map[string]bool)
	
	for {
		readyJobs := dag.ReadyJobs(completed)
		if len(readyJobs) == 0 {
			break
		}
		
		for _, jobName := range readyJobs {
			node := dag.Nodes[jobName]
			
			if len(node.MatrixJobs) > 0 {
				for _, matrixJob := range node.MatrixJobs {
					fmt.Printf("Would schedule matrix job: %s (level %d)\n", matrixJob.ID, node.Level)
				}
			} else {
				fmt.Printf("Would schedule job: %s (level %d)\n", jobName, node.Level)
			}
			
			completed[jobName] = true
		}
	}
	
	fmt.Println("All jobs would be scheduled!")
}
