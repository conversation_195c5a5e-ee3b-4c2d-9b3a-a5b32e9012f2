package orchestrator

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"chainops-backend/jobqueue"
	"chainops-backend/pipeline"
)

// PipelineOrchestrator manages the execution of pipeline jobs
type PipelineOrchestrator struct {
	queue         *jobqueue.JobQueue
	dag           *pipeline.DAG
	pipeline      *pipeline.Pipeline
	executionID   string
	status        ExecutionStatus
	startTime     time.Time
	endTime       *time.Time
	jobResults    map[string]*JobResult
	mutex         sync.RWMutex
	eventHandlers []EventHandler
	config        *OrchestratorConfig
}

// ExecutionStatus represents the overall pipeline execution status
type ExecutionStatus string

const (
	StatusPending   ExecutionStatus = "pending"
	StatusRunning   ExecutionStatus = "running"
	StatusSuccess   ExecutionStatus = "success"
	StatusFailed    ExecutionStatus = "failed"
	StatusCancelled ExecutionStatus = "cancelled"
	StatusSkipped   ExecutionStatus = "skipped"
)

// JobResult represents the result of a job execution
type JobResult struct {
	JobID       string          `json:"job_id"`
	Status      ExecutionStatus `json:"status"`
	StartTime   time.Time       `json:"start_time"`
	EndTime     *time.Time      `json:"end_time,omitempty"`
	Duration    time.Duration   `json:"duration"`
	ExitCode    int             `json:"exit_code"`
	Output      string          `json:"output,omitempty"`
	Error       string          `json:"error,omitempty"`
	Artifacts   []string        `json:"artifacts,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// EventHandler defines the interface for handling pipeline events
type EventHandler interface {
	OnPipelineStart(executionID string, pipeline *pipeline.Pipeline)
	OnPipelineComplete(executionID string, status ExecutionStatus, duration time.Duration)
	OnJobStart(executionID string, jobID string)
	OnJobComplete(executionID string, jobID string, result *JobResult)
	OnJobFailed(executionID string, jobID string, error error)
}

// OrchestratorConfig contains configuration for the orchestrator
type OrchestratorConfig struct {
	MaxConcurrentJobs int           `json:"max_concurrent_jobs"`
	JobTimeout        time.Duration `json:"job_timeout"`
	RetryAttempts     int           `json:"retry_attempts"`
	RetryDelay        time.Duration `json:"retry_delay"`
	FailFast          bool          `json:"fail_fast"`
	CleanupOnFailure  bool          `json:"cleanup_on_failure"`
}

// DefaultOrchestratorConfig returns default configuration
func DefaultOrchestratorConfig() *OrchestratorConfig {
	return &OrchestratorConfig{
		MaxConcurrentJobs: 10,
		JobTimeout:        30 * time.Minute,
		RetryAttempts:     3,
		RetryDelay:        5 * time.Second,
		FailFast:          true,
		CleanupOnFailure:  true,
	}
}

// NewPipelineOrchestrator creates a new pipeline orchestrator
func NewPipelineOrchestrator(
	queue *jobqueue.JobQueue,
	dag *pipeline.DAG,
	pipelineConfig *pipeline.Pipeline,
	executionID string,
	config *OrchestratorConfig,
) *PipelineOrchestrator {
	if config == nil {
		config = DefaultOrchestratorConfig()
	}

	return &PipelineOrchestrator{
		queue:       queue,
		dag:         dag,
		pipeline:    pipelineConfig,
		executionID: executionID,
		status:      StatusPending,
		jobResults:  make(map[string]*JobResult),
		config:      config,
	}
}

// AddEventHandler adds an event handler to the orchestrator
func (o *PipelineOrchestrator) AddEventHandler(handler EventHandler) {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.eventHandlers = append(o.eventHandlers, handler)
}

// Execute runs the pipeline orchestration
func (o *PipelineOrchestrator) Execute(ctx context.Context) error {
	o.mutex.Lock()
	o.status = StatusRunning
	o.startTime = time.Now()
	o.mutex.Unlock()

	// Notify handlers of pipeline start
	for _, handler := range o.eventHandlers {
		handler.OnPipelineStart(o.executionID, o.pipeline)
	}

	log.Printf("Starting pipeline execution: %s", o.executionID)

	// Execute jobs in dependency order
	err := o.executeJobs(ctx)

	// Update final status
	o.mutex.Lock()
	endTime := time.Now()
	o.endTime = &endTime
	
	if err != nil {
		o.status = StatusFailed
	} else if o.allJobsSuccessful() {
		o.status = StatusSuccess
	} else {
		o.status = StatusFailed
	}
	o.mutex.Unlock()

	// Notify handlers of pipeline completion
	duration := endTime.Sub(o.startTime)
	for _, handler := range o.eventHandlers {
		handler.OnPipelineComplete(o.executionID, o.status, duration)
	}

	log.Printf("Pipeline execution completed: %s (status: %s, duration: %v)", 
		o.executionID, o.status, duration)

	return err
}

// executeJobs executes jobs according to their dependencies
func (o *PipelineOrchestrator) executeJobs(ctx context.Context) error {
	completed := make(map[string]bool)
	running := make(map[string]bool)
	semaphore := make(chan struct{}, o.config.MaxConcurrentJobs)

	for {
		// Check if we're done
		if len(completed) == len(o.dag.Nodes) {
			break
		}

		// Get ready jobs
		readyJobs := o.dag.ReadyJobs(completed)
		
		// Filter out already running jobs
		var toStart []string
		for _, jobID := range readyJobs {
			if !running[jobID] && !completed[jobID] {
				toStart = append(toStart, jobID)
			}
		}

		// Start ready jobs
		for _, jobID := range toStart {
			select {
			case semaphore <- struct{}{}: // Acquire semaphore
				running[jobID] = true
				go func(jobID string) {
					defer func() { <-semaphore }() // Release semaphore
					
					result := o.executeJob(ctx, jobID)
					
					o.mutex.Lock()
					o.jobResults[jobID] = result
					completed[jobID] = true
					delete(running, jobID)
					o.mutex.Unlock()

					// Notify handlers
					if result.Status == StatusSuccess {
						for _, handler := range o.eventHandlers {
							handler.OnJobComplete(o.executionID, jobID, result)
						}
					} else {
						err := fmt.Errorf("job failed: %s", result.Error)
						for _, handler := range o.eventHandlers {
							handler.OnJobFailed(o.executionID, jobID, err)
						}
					}
				}(jobID)
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		// Check for failures and fail-fast
		if o.config.FailFast && o.hasFailedJobs() {
			return fmt.Errorf("pipeline failed due to job failures (fail-fast enabled)")
		}

		// Wait a bit before checking again
		if len(toStart) == 0 && len(running) > 0 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	return nil
}

// executeJob executes a single job
func (o *PipelineOrchestrator) executeJob(ctx context.Context, jobID string) *JobResult {
	log.Printf("Starting job: %s", jobID)
	
	result := &JobResult{
		JobID:     jobID,
		Status:    StatusRunning,
		StartTime: time.Now(),
		Metadata:  make(map[string]string),
	}

	// Notify handlers of job start
	for _, handler := range o.eventHandlers {
		handler.OnJobStart(o.executionID, jobID)
	}

	// Get job configuration
	node := o.dag.Nodes[jobID]
	if node == nil {
		result.Status = StatusFailed
		result.Error = "job not found in DAG"
		endTime := time.Now()
		result.EndTime = &endTime
		result.Duration = endTime.Sub(result.StartTime)
		return result
	}

	// Handle matrix jobs
	if len(node.MatrixJobs) > 0 {
		return o.executeMatrixJob(ctx, jobID, node.MatrixJobs)
	}

	// Execute regular job
	return o.executeRegularJob(ctx, jobID, node.Job)
}

// executeRegularJob executes a regular (non-matrix) job
func (o *PipelineOrchestrator) executeRegularJob(ctx context.Context, jobID string, job *pipeline.Job) *JobResult {
	result := &JobResult{
		JobID:     jobID,
		Status:    StatusRunning,
		StartTime: time.Now(),
		Metadata:  make(map[string]string),
	}

	// Create job for queue
	queueJob := &jobqueue.Job{
		ID:       fmt.Sprintf("%s_%s", o.executionID, jobID),
		Name:     job.Name,
		Tags:     []string{jobID, o.executionID},
		Payload:  o.createJobPayload(job, nil),
		Status:   "pending",
		Priority: 1,
	}

	// Enqueue job
	if err := o.queue.Enqueue(ctx, queueJob); err != nil {
		result.Status = StatusFailed
		result.Error = fmt.Sprintf("failed to enqueue job: %v", err)
		endTime := time.Now()
		result.EndTime = &endTime
		result.Duration = endTime.Sub(result.StartTime)
		return result
	}

	// Wait for job completion (simplified - in real implementation, use job status polling)
	time.Sleep(time.Duration(len(job.Steps)) * time.Second) // Simulate execution time

	// Simulate job result
	result.Status = StatusSuccess
	result.ExitCode = 0
	endTime := time.Now()
	result.EndTime = &endTime
	result.Duration = endTime.Sub(result.StartTime)

	log.Printf("Job completed: %s (duration: %v)", jobID, result.Duration)
	return result
}

// executeMatrixJob executes a matrix job (multiple parallel executions)
func (o *PipelineOrchestrator) executeMatrixJob(ctx context.Context, jobID string, matrixJobs []pipeline.MatrixJob) *JobResult {
	result := &JobResult{
		JobID:     jobID,
		Status:    StatusRunning,
		StartTime: time.Now(),
		Metadata:  make(map[string]string),
	}

	// Execute all matrix combinations
	var wg sync.WaitGroup
	results := make(chan *JobResult, len(matrixJobs))

	for _, matrixJob := range matrixJobs {
		wg.Add(1)
		go func(mj pipeline.MatrixJob) {
			defer wg.Done()
			
			subResult := o.executeRegularJob(ctx, mj.ID, &mj.Job)
			results <- subResult
		}(matrixJob)
	}

	// Wait for all matrix jobs to complete
	go func() {
		wg.Wait()
		close(results)
	}()

	// Collect results
	allSuccessful := true
	for subResult := range results {
		if subResult.Status != StatusSuccess {
			allSuccessful = false
		}
	}

	// Set final status
	if allSuccessful {
		result.Status = StatusSuccess
	} else {
		result.Status = StatusFailed
	}

	endTime := time.Now()
	result.EndTime = &endTime
	result.Duration = endTime.Sub(result.StartTime)

	return result
}

// createJobPayload creates payload for job execution
func (o *PipelineOrchestrator) createJobPayload(job *pipeline.Job, matrixValues map[string]interface{}) map[string]string {
	payload := make(map[string]string)
	
	payload["job_name"] = job.Name
	payload["execution_id"] = o.executionID
	payload["runs_on"] = fmt.Sprintf("%v", job.GetRunsOn())
	
	// Add steps
	for i, step := range job.Steps {
		stepKey := fmt.Sprintf("step_%d", i)
		if len(step.Script) > 0 {
			payload[stepKey] = fmt.Sprintf("script: %v", step.Script)
		} else if step.Uses != "" {
			payload[stepKey] = fmt.Sprintf("uses: %s", step.Uses)
		}
	}
	
	// Add matrix values
	if matrixValues != nil {
		for key, value := range matrixValues {
			payload[fmt.Sprintf("matrix_%s", key)] = fmt.Sprintf("%v", value)
		}
	}
	
	// Add environment variables
	if job.Env != nil {
		for key, value := range job.Env {
			payload[fmt.Sprintf("env_%s", key)] = value
		}
	}
	
	return payload
}

// Helper methods
func (o *PipelineOrchestrator) allJobsSuccessful() bool {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	
	for _, result := range o.jobResults {
		if result.Status != StatusSuccess {
			return false
		}
	}
	return true
}

func (o *PipelineOrchestrator) hasFailedJobs() bool {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	
	for _, result := range o.jobResults {
		if result.Status == StatusFailed {
			return true
		}
	}
	return false
}

// GetStatus returns the current execution status
func (o *PipelineOrchestrator) GetStatus() ExecutionStatus {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	return o.status
}

// GetJobResults returns all job results
func (o *PipelineOrchestrator) GetJobResults() map[string]*JobResult {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	
	results := make(map[string]*JobResult)
	for k, v := range o.jobResults {
		results[k] = v
	}
	return results
}

// Cancel cancels the pipeline execution
func (o *PipelineOrchestrator) Cancel() {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	
	if o.status == StatusRunning {
		o.status = StatusCancelled
		endTime := time.Now()
		o.endTime = &endTime
	}
}
