package pipeline

import (
	"fmt"
	"sort"
	"strings"
)

// DAG represents a Directed Acyclic Graph of pipeline jobs
type DAG struct {
	Nodes map[string]*DAGNode // job name -> node
	Edges map[string][]string // job name -> dependencies
	Levels [][]string         // jobs grouped by execution level
}

// DAGNode represents a node in the DAG
type DAGNode struct {
	JobName      string
	Job          *Job
	Dependencies []string
	Dependents   []string
	Level        int
	MatrixJobs   []MatrixJob // Expanded matrix jobs
}

// <PERSON>Job represents an expanded matrix job
type MatrixJob struct {
	ID           string
	JobName      string
	Job          Job
	MatrixValues map[string]interface{}
}

// BuildDAG builds a DAG from a pipeline
func BuildDAG(p *Pipeline) (*DAG, error) {
	dag := &DAG{
		Nodes: make(map[string]*DAGNode),
		Edges: make(map[string][]string),
	}
	
	// First pass: create nodes and collect dependencies
	for jobName, job := range p.Jobs {
		jobCopy := job // Create a copy to avoid pointer issues
		
		node := &DAGNode{
			JobName:      jobName,
			Job:          &jobCopy,
			Dependencies: job.GetJobNeeds(),
			Dependents:   []string{},
			Level:        -1,
		}
		
		// Expand matrix jobs if needed
		if job.Strategy != nil && job.Strategy.Matrix != nil {
			expandedJobs, err := job.ExpandMatrix()
			if err != nil {
				return nil, fmt.Errorf("failed to expand matrix for job '%s': %w", jobName, err)
			}
			
			for i, expandedJob := range expandedJobs {
				matrixJob := MatrixJob{
					ID:      fmt.Sprintf("%s-%d", jobName, i),
					JobName: jobName,
					Job:     expandedJob,
				}
				
				// Extract matrix values from environment variables
				matrixValues := make(map[string]interface{})
				for key, value := range expandedJob.Env {
					if strings.HasPrefix(key, "MATRIX_") {
						matrixKey := strings.ToLower(strings.TrimPrefix(key, "MATRIX_"))
						matrixValues[matrixKey] = value
					}
				}
				matrixJob.MatrixValues = matrixValues
				
				node.MatrixJobs = append(node.MatrixJobs, matrixJob)
			}
		}
		
		dag.Nodes[jobName] = node
		dag.Edges[jobName] = node.Dependencies
	}
	
	// Second pass: build dependents relationships
	for jobName, dependencies := range dag.Edges {
		for _, dep := range dependencies {
			if depNode, exists := dag.Nodes[dep]; exists {
				depNode.Dependents = append(depNode.Dependents, jobName)
			}
		}
	}
	
	// Check for cycles
	if hasCycle(dag) {
		return nil, fmt.Errorf("pipeline DAG has a cycle")
	}
	
	// Calculate levels
	if err := calculateLevels(dag); err != nil {
		return nil, fmt.Errorf("failed to calculate DAG levels: %w", err)
	}
	
	return dag, nil
}

// hasCycle detects cycles in the DAG using DFS
func hasCycle(dag *DAG) bool {
	visited := make(map[string]bool)
	recStack := make(map[string]bool)
	
	var visit func(string) bool
	visit = func(node string) bool {
		if recStack[node] {
			return true // Back edge found, cycle detected
		}
		if visited[node] {
			return false // Already processed
		}
		
		visited[node] = true
		recStack[node] = true
		
		for _, dep := range dag.Edges[node] {
			if visit(dep) {
				return true
			}
		}
		
		recStack[node] = false
		return false
	}
	
	for node := range dag.Nodes {
		if visit(node) {
			return true
		}
	}
	
	return false
}

// calculateLevels assigns execution levels to jobs
func calculateLevels(dag *DAG) error {
	// Calculate the level of each node (topological sort)
	inDegree := make(map[string]int)
	
	// Initialize in-degrees
	for jobName := range dag.Nodes {
		inDegree[jobName] = len(dag.Edges[jobName])
	}
	
	// Find nodes with no dependencies (level 0)
	var queue []string
	for jobName, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, jobName)
			dag.Nodes[jobName].Level = 0
		}
	}
	
	level := 0
	dag.Levels = [][]string{}
	
	for len(queue) > 0 {
		// Process all nodes at current level
		currentLevel := make([]string, len(queue))
		copy(currentLevel, queue)
		dag.Levels = append(dag.Levels, currentLevel)
		
		// Prepare next level
		nextQueue := []string{}
		
		for _, jobName := range queue {
			// Process dependents
			for _, dependent := range dag.Nodes[jobName].Dependents {
				inDegree[dependent]--
				if inDegree[dependent] == 0 {
					dag.Nodes[dependent].Level = level + 1
					nextQueue = append(nextQueue, dependent)
				}
			}
		}
		
		queue = nextQueue
		level++
	}
	
	// Check if all nodes were processed
	for jobName, node := range dag.Nodes {
		if node.Level == -1 {
			return fmt.Errorf("job '%s' could not be assigned a level (possible cycle)", jobName)
		}
	}
	
	return nil
}

// ReadyJobs returns jobs that are ready to run (all dependencies completed)
func (dag *DAG) ReadyJobs(completed map[string]bool) []string {
	var ready []string
	
	for jobName, node := range dag.Nodes {
		if completed[jobName] {
			continue // Already completed
		}
		
		// Check if all dependencies are completed
		allDepsCompleted := true
		for _, dep := range node.Dependencies {
			if !completed[dep] {
				allDepsCompleted = false
				break
			}
		}
		
		if allDepsCompleted {
			ready = append(ready, jobName)
		}
	}
	
	// Sort by level for consistent ordering
	sort.Slice(ready, func(i, j int) bool {
		levelI := dag.Nodes[ready[i]].Level
		levelJ := dag.Nodes[ready[j]].Level
		if levelI != levelJ {
			return levelI < levelJ
		}
		return ready[i] < ready[j] // Alphabetical for same level
	})
	
	return ready
}

// GetJobsAtLevel returns all jobs at a specific level
func (dag *DAG) GetJobsAtLevel(level int) []string {
	if level < 0 || level >= len(dag.Levels) {
		return []string{}
	}
	return dag.Levels[level]
}

// GetMaxLevel returns the maximum level in the DAG
func (dag *DAG) GetMaxLevel() int {
	return len(dag.Levels) - 1
}

// GetJobDependencies returns the dependencies of a job
func (dag *DAG) GetJobDependencies(jobName string) []string {
	if node, exists := dag.Nodes[jobName]; exists {
		return node.Dependencies
	}
	return []string{}
}

// GetJobDependents returns the dependents of a job
func (dag *DAG) GetJobDependents(jobName string) []string {
	if node, exists := dag.Nodes[jobName]; exists {
		return node.Dependents
	}
	return []string{}
}

// IsJobReady checks if a job is ready to run
func (dag *DAG) IsJobReady(jobName string, completed map[string]bool) bool {
	if completed[jobName] {
		return false // Already completed
	}
	
	node, exists := dag.Nodes[jobName]
	if !exists {
		return false // Job doesn't exist
	}
	
	// Check if all dependencies are completed
	for _, dep := range node.Dependencies {
		if !completed[dep] {
			return false
		}
	}
	
	return true
}

// GetExecutionPlan returns an execution plan for the pipeline
func (dag *DAG) GetExecutionPlan() [][]string {
	return dag.Levels
}

// GetCriticalPath returns the critical path (longest path) through the DAG
func (dag *DAG) GetCriticalPath() []string {
	// Find the longest path using dynamic programming
	memo := make(map[string]int)
	parent := make(map[string]string)
	
	var longestPath func(string) int
	longestPath = func(jobName string) int {
		if length, exists := memo[jobName]; exists {
			return length
		}
		
		maxLength := 0
		maxParent := ""
		
		for _, dep := range dag.Nodes[jobName].Dependencies {
			length := longestPath(dep) + 1
			if length > maxLength {
				maxLength = length
				maxParent = dep
			}
		}
		
		memo[jobName] = maxLength
		if maxParent != "" {
			parent[jobName] = maxParent
		}
		
		return maxLength
	}
	
	// Find the job with the longest path
	maxLength := 0
	endJob := ""
	
	for jobName := range dag.Nodes {
		length := longestPath(jobName)
		if length > maxLength {
			maxLength = length
			endJob = jobName
		}
	}
	
	// Reconstruct the path
	var path []string
	current := endJob
	
	for current != "" {
		path = append([]string{current}, path...)
		current = parent[current]
	}
	
	return path
}

// GetParallelizationOpportunities returns jobs that can run in parallel
func (dag *DAG) GetParallelizationOpportunities() map[int][]string {
	opportunities := make(map[int][]string)
	
	for level, jobs := range dag.Levels {
		if len(jobs) > 1 {
			opportunities[level] = jobs
		}
	}
	
	return opportunities
}

// EstimateExecutionTime estimates the total execution time
func (dag *DAG) EstimateExecutionTime(jobDurations map[string]int) int {
	if len(jobDurations) == 0 {
		return 0
	}
	
	totalTime := 0
	
	for _, levelJobs := range dag.Levels {
		maxTimeInLevel := 0
		
		for _, jobName := range levelJobs {
			if duration, exists := jobDurations[jobName]; exists {
				if duration > maxTimeInLevel {
					maxTimeInLevel = duration
				}
			}
		}
		
		totalTime += maxTimeInLevel
	}
	
	return totalTime
}

// GetDAGStats returns statistics about the DAG
func (dag *DAG) GetDAGStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	stats["total_jobs"] = len(dag.Nodes)
	stats["total_levels"] = len(dag.Levels)
	stats["max_parallelism"] = dag.getMaxParallelism()
	stats["critical_path_length"] = len(dag.GetCriticalPath())
	
	// Count matrix jobs
	matrixJobCount := 0
	for _, node := range dag.Nodes {
		matrixJobCount += len(node.MatrixJobs)
	}
	stats["matrix_jobs"] = matrixJobCount
	
	return stats
}

func (dag *DAG) getMaxParallelism() int {
	maxParallelism := 0
	
	for _, levelJobs := range dag.Levels {
		if len(levelJobs) > maxParallelism {
			maxParallelism = len(levelJobs)
		}
	}
	
	return maxParallelism
}
