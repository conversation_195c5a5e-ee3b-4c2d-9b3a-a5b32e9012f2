package pipeline

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"gopkg.in/yaml.v3"
)

// ParsePipelineYAML parses a pipeline YAML file
func ParsePipelineYAML(path string) (*Pipeline, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read pipeline file: %w", err)
	}

	return ParsePipelineYAMLFromBytes(data)
}

// ParsePipelineYAMLFromBytes parses pipeline YAML from byte data
func ParsePipelineYAMLFromBytes(data []byte) (*Pipeline, error) {
	var p Pipeline
	err := yaml.Unmarshal(data, &p)
	if err != nil {
		return nil, fmt.Errorf("failed to parse pipeline YAML: %w", err)
	}

	// Validate the pipeline
	if err := ValidatePipeline(&p); err != nil {
		return nil, fmt.Errorf("pipeline validation failed: %w", err)
	}

	return &p, nil
}

// ParsePipelineYAMLBytes is an alias for ParsePipelineYAMLFromBytes for backward compatibility
func ParsePipelineYAMLBytes(data []byte) (*Pipeline, error) {
	return ParsePipelineYAMLFromBytes(data)
}

// ParsePipelineYAMLFromString parses pipeline YAML from string
func ParsePipelineYAMLFromString(yamlContent string) (*Pipeline, error) {
	return ParsePipelineYAMLFromBytes([]byte(yamlContent))
}

// ValidatePipeline validates a pipeline configuration
func ValidatePipeline(p *Pipeline) error {
	if p.Jobs == nil || len(p.Jobs) == 0 {
		return fmt.Errorf("pipeline must have at least one job")
	}

	// Validate each job
	for jobName, job := range p.Jobs {
		if err := ValidateJob(jobName, &job, p); err != nil {
			return fmt.Errorf("job '%s': %w", jobName, err)
		}
	}

	// Validate job dependencies
	if err := ValidateJobDependencies(p); err != nil {
		return fmt.Errorf("job dependencies: %w", err)
	}

	return nil
}

// ValidateJob validates a single job
func ValidateJob(jobName string, job *Job, pipeline *Pipeline) error {
	// Validate job name
	if !isValidJobName(jobName) {
		return fmt.Errorf("invalid job name '%s': must contain only alphanumeric characters, hyphens, and underscores", jobName)
	}

	// Validate steps
	if len(job.Steps) == 0 {
		return fmt.Errorf("job must have at least one step")
	}

	for i, step := range job.Steps {
		if err := ValidateStep(i, &step); err != nil {
			return fmt.Errorf("step %d: %w", i+1, err)
		}
	}

	// Validate needs dependencies
	needs := job.GetJobNeeds()
	for _, need := range needs {
		if _, exists := pipeline.Jobs[need]; !exists {
			return fmt.Errorf("dependency '%s' does not exist", need)
		}
	}

	// Validate matrix strategy
	if job.Strategy != nil && job.Strategy.Matrix != nil {
		if err := ValidateMatrix(job.Strategy.Matrix); err != nil {
			return fmt.Errorf("matrix strategy: %w", err)
		}
	}

	// Validate timeout
	if job.Timeout < 0 || job.Timeout > 360 {
		return fmt.Errorf("timeout must be between 0 and 360 minutes")
	}

	return nil
}

// ValidateStep validates a single step
func ValidateStep(index int, step *Step) error {
	// Step must have either 'run', 'uses', or 'script'
	hasAction := step.Uses != "" || len(step.Script) > 0
	if !hasAction {
		return fmt.Errorf("step must have either 'uses', 'script', or 'run' field")
	}

	// Cannot have both 'uses' and 'script'
	if step.Uses != "" && len(step.Script) > 0 {
		return fmt.Errorf("step cannot have both 'uses' and 'script' fields")
	}

	// Validate step name if provided
	if step.Name != "" && !isValidStepName(step.Name) {
		return fmt.Errorf("invalid step name '%s'", step.Name)
	}

	// Validate timeout
	if step.Timeout < 0 || step.Timeout > 360 {
		return fmt.Errorf("step timeout must be between 0 and 360 minutes")
	}

	// Validate 'uses' format if provided
	if step.Uses != "" {
		if err := ValidateUsesAction(step.Uses); err != nil {
			return fmt.Errorf("invalid 'uses' action: %w", err)
		}
	}

	return nil
}

// ValidateMatrix validates matrix strategy configuration
func ValidateMatrix(matrix map[string]interface{}) error {
	if len(matrix) == 0 {
		return fmt.Errorf("matrix cannot be empty")
	}

	variableCount := 0
	for key, value := range matrix {
		if key == "include" || key == "exclude" {
			continue
		}

		variableCount++

		// Validate matrix variable name
		if !isValidMatrixVariable(key) {
			return fmt.Errorf("invalid matrix variable name '%s'", key)
		}

		// Validate matrix values
		switch v := value.(type) {
		case []interface{}:
			if len(v) == 0 {
				return fmt.Errorf("matrix variable '%s' cannot have empty values", key)
			}
		case []string:
			if len(v) == 0 {
				return fmt.Errorf("matrix variable '%s' cannot have empty values", key)
			}
		default:
			return fmt.Errorf("matrix variable '%s' must be an array", key)
		}
	}

	if variableCount == 0 && matrix["include"] == nil {
		return fmt.Errorf("matrix must have at least one variable or include section")
	}

	// Validate include/exclude sections
	if include, ok := matrix["include"]; ok {
		if err := ValidateMatrixIncludeExclude("include", include); err != nil {
			return err
		}
	}

	if exclude, ok := matrix["exclude"]; ok {
		if err := ValidateMatrixIncludeExclude("exclude", exclude); err != nil {
			return err
		}
	}

	return nil
}

// ValidateMatrixIncludeExclude validates include/exclude sections
func ValidateMatrixIncludeExclude(section string, value interface{}) error {
	array, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("matrix %s must be an array", section)
	}

	for i, item := range array {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			return fmt.Errorf("matrix %s item %d must be an object", section, i)
		}

		if len(itemMap) == 0 {
			return fmt.Errorf("matrix %s item %d cannot be empty", section, i)
		}
	}

	return nil
}

// ValidateJobDependencies validates that job dependencies form a valid DAG
func ValidateJobDependencies(pipeline *Pipeline) error {
	// Build dependency graph
	graph := make(map[string][]string)
	for jobName, job := range pipeline.Jobs {
		graph[jobName] = job.GetJobNeeds()
	}

	// Check for cycles using DFS
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	var hasCycle func(string) bool
	hasCycle = func(node string) bool {
		if recStack[node] {
			return true
		}
		if visited[node] {
			return false
		}

		visited[node] = true
		recStack[node] = true

		for _, dep := range graph[node] {
			if hasCycle(dep) {
				return true
			}
		}

		recStack[node] = false
		return false
	}

	for jobName := range pipeline.Jobs {
		if hasCycle(jobName) {
			return fmt.Errorf("circular dependency detected involving job '%s'", jobName)
		}
	}

	return nil
}

// ValidateUsesAction validates the format of a 'uses' action
func ValidateUsesAction(uses string) error {
	// Format: owner/repo@ref or ./path/to/action
	if strings.HasPrefix(uses, "./") {
		// Local action
		return nil
	}

	// Remote action: owner/repo@ref
	parts := strings.Split(uses, "@")
	if len(parts) != 2 {
		return fmt.Errorf("remote action must be in format 'owner/repo@ref'")
	}

	repo := parts[0]
	ref := parts[1]

	if !isValidRepoName(repo) {
		return fmt.Errorf("invalid repository name '%s'", repo)
	}

	if ref == "" {
		return fmt.Errorf("action reference cannot be empty")
	}

	return nil
}

// Helper validation functions
func isValidJobName(name string) bool {
	// Job names can contain alphanumeric characters, hyphens, and underscores
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, name)
	return matched && len(name) <= 100
}

func isValidStepName(name string) bool {
	// Step names are more flexible but should not be empty or too long
	return len(name) > 0 && len(name) <= 255
}

func isValidMatrixVariable(name string) bool {
	// Matrix variable names should be valid identifiers (allow hyphens)
	matched, _ := regexp.MatchString(`^[a-zA-Z_][a-zA-Z0-9_-]*$`, name)
	return matched && len(name) <= 50
}

func isValidRepoName(repo string) bool {
	// Repository names should be in format owner/repo
	parts := strings.Split(repo, "/")
	if len(parts) != 2 {
		return false
	}

	owner, repoName := parts[0], parts[1]

	// Basic validation for owner and repo names
	ownerValid, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, owner)
	repoValid, _ := regexp.MatchString(`^[a-zA-Z0-9_.-]+$`, repoName)

	return ownerValid && repoValid && len(owner) > 0 && len(repoName) > 0
}

// DiscoverPipelineFiles discovers pipeline files in a directory
func DiscoverPipelineFiles(dir string) ([]string, error) {
	var pipelineFiles []string

	// Common pipeline file patterns
	patterns := []string{
		"*.chainops.yml",
		"*.chainops.yaml",
		".chainops/*.yml",
		".chainops/*.yaml",
		".github/workflows/*.yml",
		".github/workflows/*.yaml",
	}

	for _, pattern := range patterns {
		matches, err := filepath.Glob(filepath.Join(dir, pattern))
		if err != nil {
			continue
		}
		pipelineFiles = append(pipelineFiles, matches...)
	}

	return pipelineFiles, nil
}

// LoadPipelineFromDirectory loads a pipeline from a directory
func LoadPipelineFromDirectory(dir string) (*Pipeline, error) {
	files, err := DiscoverPipelineFiles(dir)
	if err != nil {
		return nil, fmt.Errorf("failed to discover pipeline files: %w", err)
	}

	if len(files) == 0 {
		return nil, fmt.Errorf("no pipeline files found in directory")
	}

	// Use the first found pipeline file
	return ParsePipelineYAML(files[0])
}

// SavePipelineYAML saves a pipeline to a YAML file
func SavePipelineYAML(pipeline *Pipeline, path string) error {
	data, err := yaml.Marshal(pipeline)
	if err != nil {
		return fmt.Errorf("failed to marshal pipeline: %w", err)
	}

	// Ensure directory exists
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	err = ioutil.WriteFile(path, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write pipeline file: %w", err)
	}

	return nil
}
