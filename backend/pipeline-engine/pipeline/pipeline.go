package pipeline

import (
	"fmt"
	"strings"
)

// Step represents a single step in a pipeline job
type Step struct {
	Name     string            `yaml:"name"`
	<PERSON>ript   []string          `yaml:"script"`
	Uses     string            `yaml:"uses,omitempty"`
	With     map[string]string `yaml:"with,omitempty"`
	Env      map[string]string `yaml:"env,omitempty"`
	If       string            `yaml:"if,omitempty"`
	Needs    []string          `yaml:"needs"`
	Tags     []string          `yaml:"tags"`
	Timeout  int               `yaml:"timeout,omitempty"`
	Continue bool              `yaml:"continue-on-error,omitempty"`
}

// Job represents a pipeline job with multiple steps
type Job struct {
	Name        string             `yaml:"name"`
	RunsOn      interface{}        `yaml:"runs-on"` // Can be string or []string
	Needs       interface{}        `yaml:"needs"`   // Can be string or []string
	If          string             `yaml:"if,omitempty"`
	Env         map[string]string  `yaml:"env,omitempty"`
	Timeout     int                `yaml:"timeout-minutes,omitempty"`
	Continue    bool               `yaml:"continue-on-error,omitempty"`
	Strategy    *Strategy          `yaml:"strategy,omitempty"`
	Environment interface{}        `yaml:"environment,omitempty"` // Can be string or Environment object
	Steps       []Step             `yaml:"steps"`
	Services    map[string]Service `yaml:"services,omitempty"`
	Container   *Container         `yaml:"container,omitempty"`
}

// Strategy defines job execution strategy (matrix, fail-fast, etc.)
type Strategy struct {
	Matrix      map[string]interface{} `yaml:"matrix,omitempty"`
	FailFast    *bool                  `yaml:"fail-fast,omitempty"`
	MaxParallel *int                   `yaml:"max-parallel,omitempty"`
}

// Environment represents an environment configuration
type Environment struct {
	Name string `yaml:"name"`
	URL  string `yaml:"url,omitempty"`
}

// Service represents a service container
type Service struct {
	Image   string            `yaml:"image"`
	Env     map[string]string `yaml:"env,omitempty"`
	Ports   []string          `yaml:"ports,omitempty"`
	Volumes []string          `yaml:"volumes,omitempty"`
	Options string            `yaml:"options,omitempty"`
}

// Container represents a job container
type Container struct {
	Image       string            `yaml:"image"`
	Env         map[string]string `yaml:"env,omitempty"`
	Ports       []string          `yaml:"ports,omitempty"`
	Volumes     []string          `yaml:"volumes,omitempty"`
	Options     string            `yaml:"options,omitempty"`
	Credentials *Credentials      `yaml:"credentials,omitempty"`
}

// Credentials for container registry
type Credentials struct {
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

// Trigger defines when the pipeline should run
type Trigger struct {
	Push             *PushTrigger             `yaml:"push,omitempty"`
	PullRequest      *PullRequestTrigger      `yaml:"pull_request,omitempty"`
	Schedule         []ScheduleTrigger        `yaml:"schedule,omitempty"`
	WorkflowDispatch *WorkflowDispatchTrigger `yaml:"workflow_dispatch,omitempty"`
	Webhook          *WebhookTrigger          `yaml:"webhook,omitempty"`
}

// PushTrigger for push events
type PushTrigger struct {
	Branches    []string `yaml:"branches,omitempty"`
	Tags        []string `yaml:"tags,omitempty"`
	Paths       []string `yaml:"paths,omitempty"`
	PathsIgnore []string `yaml:"paths-ignore,omitempty"`
}

// PullRequestTrigger for pull request events
type PullRequestTrigger struct {
	Branches    []string `yaml:"branches,omitempty"`
	Types       []string `yaml:"types,omitempty"`
	Paths       []string `yaml:"paths,omitempty"`
	PathsIgnore []string `yaml:"paths-ignore,omitempty"`
}

// ScheduleTrigger for scheduled runs
type ScheduleTrigger struct {
	Cron string `yaml:"cron"`
}

// WorkflowDispatchTrigger for manual triggers
type WorkflowDispatchTrigger struct {
	Inputs map[string]WorkflowInput `yaml:"inputs,omitempty"`
}

// WorkflowInput defines manual trigger inputs
type WorkflowInput struct {
	Description string   `yaml:"description,omitempty"`
	Required    bool     `yaml:"required,omitempty"`
	Default     string   `yaml:"default,omitempty"`
	Type        string   `yaml:"type,omitempty"`
	Options     []string `yaml:"options,omitempty"`
}

// WebhookTrigger for webhook events
type WebhookTrigger struct {
	Types []string `yaml:"types,omitempty"`
}

// Pipeline represents the complete pipeline configuration
type Pipeline struct {
	Name     string            `yaml:"name,omitempty"`
	Version  string            `yaml:"version,omitempty"`
	On       *Trigger          `yaml:"on,omitempty"`
	Env      map[string]string `yaml:"env,omitempty"`
	Defaults *Defaults         `yaml:"defaults,omitempty"`
	Jobs     map[string]Job    `yaml:"jobs"`
}

// Defaults for pipeline execution
type Defaults struct {
	Run *RunDefaults `yaml:"run,omitempty"`
}

// RunDefaults for run commands
type RunDefaults struct {
	Shell            string `yaml:"shell,omitempty"`
	WorkingDirectory string `yaml:"working-directory,omitempty"`
}

// GetJobNeeds returns the dependencies for a job as a slice of strings
func (j *Job) GetJobNeeds() []string {
	switch needs := j.Needs.(type) {
	case string:
		if needs == "" {
			return []string{}
		}
		return []string{needs}
	case []string:
		return needs
	case []interface{}:
		result := make([]string, len(needs))
		for i, need := range needs {
			result[i] = fmt.Sprintf("%v", need)
		}
		return result
	default:
		return []string{}
	}
}

// GetRunsOn returns the runner requirements as a slice of strings
func (j *Job) GetRunsOn() []string {
	switch runsOn := j.RunsOn.(type) {
	case string:
		return []string{runsOn}
	case []string:
		return runsOn
	case []interface{}:
		result := make([]string, len(runsOn))
		for i, runner := range runsOn {
			result[i] = fmt.Sprintf("%v", runner)
		}
		return result
	default:
		return []string{"ubuntu-latest"} // Default runner
	}
}

// GetEnvironmentName returns the environment name
func (j *Job) GetEnvironmentName() string {
	switch env := j.Environment.(type) {
	case string:
		return env
	case Environment:
		return env.Name
	case map[string]interface{}:
		if name, ok := env["name"].(string); ok {
			return name
		}
	}
	return ""
}

// ExpandMatrix expands a job with matrix strategy into multiple jobs
func (j *Job) ExpandMatrix() ([]Job, error) {
	if j.Strategy == nil || j.Strategy.Matrix == nil {
		return []Job{*j}, nil
	}

	matrix := j.Strategy.Matrix

	// Extract matrix variables and their values
	var keys []string
	var values [][]interface{}

	for key, val := range matrix {
		if key == "include" || key == "exclude" {
			continue // Handle these separately
		}

		keys = append(keys, key)
		switch v := val.(type) {
		case []interface{}:
			values = append(values, v)
		case []string:
			interfaceSlice := make([]interface{}, len(v))
			for i, s := range v {
				interfaceSlice[i] = s
			}
			values = append(values, interfaceSlice)
		default:
			return nil, fmt.Errorf("invalid matrix value for key %s", key)
		}
	}

	// Generate all combinations
	combinations := generateCombinations(keys, values)

	// Handle include/exclude
	if include, ok := matrix["include"].([]interface{}); ok {
		combinations = append(combinations, include...)
	}

	if exclude, ok := matrix["exclude"].([]interface{}); ok {
		combinations = filterExcluded(combinations, exclude)
	}

	// Create jobs for each combination
	var jobs []Job
	for _, combo := range combinations {
		newJob := *j
		newJob.Name = fmt.Sprintf("%s (%s)", j.Name, formatMatrixCombo(combo))

		// Add matrix values to job environment
		if newJob.Env == nil {
			newJob.Env = make(map[string]string)
		}

		if comboMap, ok := combo.(map[string]interface{}); ok {
			for key, value := range comboMap {
				newJob.Env[fmt.Sprintf("MATRIX_%s", strings.ToUpper(key))] = fmt.Sprintf("%v", value)
			}
		}

		jobs = append(jobs, newJob)
	}

	return jobs, nil
}

// Helper functions for matrix expansion
func generateCombinations(keys []string, values [][]interface{}) []interface{} {
	if len(keys) == 0 {
		return []interface{}{}
	}

	var result []interface{}
	var generate func(int, map[string]interface{})

	generate = func(index int, current map[string]interface{}) {
		if index == len(keys) {
			combo := make(map[string]interface{})
			for k, v := range current {
				combo[k] = v
			}
			result = append(result, combo)
			return
		}

		key := keys[index]
		for _, value := range values[index] {
			current[key] = value
			generate(index+1, current)
		}
	}

	generate(0, make(map[string]interface{}))
	return result
}

func filterExcluded(combinations []interface{}, exclude []interface{}) []interface{} {
	var filtered []interface{}

	for _, combo := range combinations {
		excluded := false
		comboMap, ok := combo.(map[string]interface{})
		if !ok {
			continue
		}

		for _, exc := range exclude {
			excMap, ok := exc.(map[string]interface{})
			if !ok {
				continue
			}

			matches := true
			for key, value := range excMap {
				if comboMap[key] != value {
					matches = false
					break
				}
			}

			if matches {
				excluded = true
				break
			}
		}

		if !excluded {
			filtered = append(filtered, combo)
		}
	}

	return filtered
}

func formatMatrixCombo(combo interface{}) string {
	comboMap, ok := combo.(map[string]interface{})
	if !ok {
		return fmt.Sprintf("%v", combo)
	}

	var parts []string
	for key, value := range comboMap {
		parts = append(parts, fmt.Sprintf("%s: %v", key, value))
	}

	return strings.Join(parts, ", ")
}
