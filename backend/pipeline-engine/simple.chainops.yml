name: Simple ChainOps Pipeline
version: "1.0"

jobs:
  build:
    name: Build Application
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Build
        script:
          - make build
    tags: [docker, build]

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Test
        script:
          - make test
    tags: [docker, test]

  deploy:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [test]
    steps:
      - name: Deploy
        script:
          - ./deploy.sh
    tags: [docker, deploy]
