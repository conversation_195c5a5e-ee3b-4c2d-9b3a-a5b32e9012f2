package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"chainops-backend/editor"
)

func main() {
	fmt.Println("🎨 Testing ChainOps Pipeline Visualization")
	fmt.Println("==========================================")

	// Test 1: Start the editor API server
	fmt.Println("\n1. Starting Pipeline Editor API server...")
	
	// Initialize DAG API handler
	dagHandler := editor.NewDAGAPIHandler()
	
	// Set up HTTP routes
	mux := http.NewServeMux()
	dagHandler.SetupRoutes(mux)
	
	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON><PERSON>ead<PERSON>(http.StatusOK)
		fmt.Fprintf(w, `{"status":"healthy","timestamp":"%s","service":"pipeline-editor"}`, time.Now().Format(time.RFC3339))
	})
	
	// Start server in background
	server := &http.Server{
		Addr:    ":8081",
		Handler: mux,
	}
	
	go func() {
		fmt.Println("✅ Pipeline Editor API started on :8081")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("❌ Server error: %v\n", err)
		}
	}()
	
	// Wait for server to start
	time.Sleep(2 * time.Second)

	// Test 2: Test health endpoint
	fmt.Println("\n2. Testing health endpoint...")
	resp, err := http.Get("http://localhost:8081/health")
	if err != nil {
		fmt.Printf("❌ Health check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Printf("✅ Health check: %s\n", string(body))

	// Test 3: Test pipeline DAG endpoint
	fmt.Println("\n3. Testing pipeline DAG endpoint...")
	resp, err = http.Get("http://localhost:8081/api/editor/pipeline/dag?id=pipeline-1")
	if err != nil {
		fmt.Printf("❌ DAG endpoint failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ = ioutil.ReadAll(resp.Body)
	
	var dag editor.PipelineDAG
	if err := json.Unmarshal(body, &dag); err != nil {
		fmt.Printf("❌ Failed to parse DAG response: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Pipeline DAG retrieved successfully!\n")
	fmt.Printf("   Pipeline: %s (version: %s)\n", dag.Name, dag.Version)
	fmt.Printf("   Status: %s\n", dag.Status)
	fmt.Printf("   Nodes: %d\n", len(dag.Nodes))
	fmt.Printf("   Edges: %d\n", len(dag.Edges))
	fmt.Printf("   Total Jobs: %d\n", dag.Stats.TotalJobs)
	fmt.Printf("   Success Jobs: %d\n", dag.Stats.SuccessJobs)
	fmt.Printf("   Running Jobs: %d\n", dag.Stats.RunningJobs)
	fmt.Printf("   Pending Jobs: %d\n", dag.Stats.PendingJobs)

	// Test 4: Test different layout options
	fmt.Println("\n4. Testing layout options...")
	
	layouts := []string{"horizontal", "vertical", "circular"}
	for _, layout := range layouts {
		resp, err := http.Get(fmt.Sprintf("http://localhost:8081/api/editor/pipeline/dag?id=pipeline-1&layout=%s", layout))
		if err != nil {
			fmt.Printf("❌ Layout %s failed: %v\n", layout, err)
			continue
		}
		defer resp.Body.Close()
		
		body, _ := ioutil.ReadAll(resp.Body)
		var layoutDAG editor.PipelineDAG
		if err := json.Unmarshal(body, &layoutDAG); err != nil {
			fmt.Printf("❌ Failed to parse %s layout: %v\n", layout, err)
			continue
		}
		
		fmt.Printf("✅ Layout %s: nodes positioned\n", layout)
		for i, node := range layoutDAG.Nodes[:3] { // Show first 3 nodes
			fmt.Printf("   Node %d (%s): x=%.1f, y=%.1f\n", i+1, node.Label, node.Position.X, node.Position.Y)
		}
	}

	// Test 5: Test pipeline validation
	fmt.Println("\n5. Testing pipeline validation...")
	
	validPipeline := map[string]interface{}{
		"name":    "Test Pipeline",
		"version": "1.0",
		"jobs": map[string]interface{}{
			"build": map[string]interface{}{
				"name":     "Build Job",
				"runs-on":  "ubuntu-latest",
				"steps": []map[string]interface{}{
					{
						"name":   "Checkout",
						"uses":   "actions/checkout@v4",
					},
					{
						"name": "Build",
						"script": []string{"make build"},
					},
				},
			},
			"test": map[string]interface{}{
				"name":     "Test Job",
				"runs-on":  "ubuntu-latest",
				"needs":    []string{"build"},
				"steps": []map[string]interface{}{
					{
						"name": "Test",
						"script": []string{"make test"},
					},
				},
			},
		},
	}
	
	jsonData, _ := json.Marshal(validPipeline)
	resp, err = http.Post("http://localhost:8081/api/editor/pipeline/validate", "application/json", 
		ioutil.NopCloser(fmt.Sprintf("%s", jsonData)))
	if err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		
		var result editor.ValidationResult
		if err := json.Unmarshal(body, &result); err != nil {
			fmt.Printf("❌ Failed to parse validation result: %v\n", err)
		} else {
			if result.Valid {
				fmt.Printf("✅ Pipeline validation: VALID\n")
			} else {
				fmt.Printf("❌ Pipeline validation: INVALID\n")
				for _, err := range result.Errors {
					fmt.Printf("   Error: %s - %s\n", err.Code, err.Message)
				}
			}
		}
	}

	// Test 6: Display sample node data for frontend
	fmt.Println("\n6. Sample node data for frontend visualization:")
	
	if len(dag.Nodes) > 0 {
		sampleNode := dag.Nodes[0]
		nodeJSON, _ := json.MarshalIndent(sampleNode, "   ", "  ")
		fmt.Printf("   Sample Node JSON:\n   %s\n", string(nodeJSON))
	}

	// Test 7: Display sample edge data
	fmt.Println("\n7. Sample edge data for frontend visualization:")
	
	if len(dag.Edges) > 0 {
		sampleEdge := dag.Edges[0]
		edgeJSON, _ := json.MarshalIndent(sampleEdge, "   ", "  ")
		fmt.Printf("   Sample Edge JSON:\n   %s\n", string(edgeJSON))
	}

	fmt.Println("\n🎉 All visualization tests completed!")
	fmt.Println("\n💡 Next steps:")
	fmt.Println("   1. Start the frontend: cd frontend && npm run dev")
	fmt.Println("   2. Open http://localhost:5173 in your browser")
	fmt.Println("   3. The frontend will connect to this API server on :8081")
	fmt.Println("   4. Try different view modes: 2D, 3D, and Stats")
	fmt.Println("   5. Click on nodes to see detailed information")
	
	fmt.Println("\n🔧 API Endpoints available:")
	fmt.Println("   GET  /api/editor/pipeline/dag?id=<pipeline_id>&layout=<layout>")
	fmt.Println("   GET  /api/editor/pipeline/status?run_id=<run_id>")
	fmt.Println("   POST /api/editor/pipeline/validate")
	fmt.Println("   GET  /health")
	
	// Keep server running
	fmt.Println("\n⏳ Server running... Press Ctrl+C to stop")
	select {} // Block forever
}
