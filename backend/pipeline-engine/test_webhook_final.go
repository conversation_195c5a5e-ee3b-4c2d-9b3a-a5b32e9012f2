package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🚀 Testing ChainOps Webhook Server")
	fmt.Println("==================================")

	// Test health endpoint
	fmt.Println("\n1. Testing health endpoint...")
	resp, err := http.Get("http://localhost:8080/health")
	if err != nil {
		fmt.Printf("❌ Health check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Printf("✅ Health check: %s\n", string(body))

	// Test status endpoint
	fmt.Println("\n2. Testing status endpoint...")
	resp, err = http.Get("http://localhost:8080/status")
	if err != nil {
		fmt.Printf("❌ Status check failed: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		fmt.Printf("✅ Status check: %s\n", string(body))
	}

	// Test GitHub webhook
	fmt.Println("\n3. Testing GitHub webhook...")
	githubPayload := map[string]interface{}{
		"ref":    "refs/heads/main",
		"before": "0000000000000000000000000000000000000000",
		"after":  "1234567890abcdef1234567890abcdef12345678",
		"repository": map[string]interface{}{
			"id":        123456,
			"name":      "chainops-demo",
			"full_name": "example/chainops-demo",
			"clone_url": "https://github.com/example/chainops-demo.git",
			"ssh_url":   "**************:example/chainops-demo.git",
			"html_url":  "https://github.com/example/chainops-demo",
			"private":   false,
			"owner": map[string]interface{}{
				"id":         12345,
				"login":      "example",
				"name":       "Example User",
				"email":      "<EMAIL>",
				"avatar_url": "https://github.com/images/error/example_happy.gif",
			},
		},
		"pusher": map[string]interface{}{
			"name":  "example",
			"email": "<EMAIL>",
		},
		"head_commit": map[string]interface{}{
			"id":        "1234567890abcdef1234567890abcdef12345678",
			"tree_id":   "abcdef1234567890abcdef1234567890abcdef12",
			"message":   "Add ChainOps pipeline configuration",
			"timestamp": "2024-01-15T10:30:00Z",
			"url":       "https://github.com/example/chainops-demo/commit/1234567890abcdef1234567890abcdef12345678",
			"author": map[string]interface{}{
				"name":  "Example User",
				"email": "<EMAIL>",
			},
			"committer": map[string]interface{}{
				"name":  "Example User",
				"email": "<EMAIL>",
			},
			"added":    []string{".chainops.yml"},
			"removed":  []string{},
			"modified": []string{"README.md"},
		},
		"commits": []map[string]interface{}{
			{
				"id":        "1234567890abcdef1234567890abcdef12345678",
				"tree_id":   "abcdef1234567890abcdef1234567890abcdef12",
				"message":   "Add ChainOps pipeline configuration",
				"timestamp": "2024-01-15T10:30:00Z",
				"url":       "https://github.com/example/chainops-demo/commit/1234567890abcdef1234567890abcdef12345678",
				"author": map[string]interface{}{
					"name":  "Example User",
					"email": "<EMAIL>",
				},
				"committer": map[string]interface{}{
					"name":  "Example User",
					"email": "<EMAIL>",
				},
				"added":    []string{".chainops.yml"},
				"removed":  []string{},
				"modified": []string{"README.md"},
			},
		},
	}

	jsonData, _ := json.Marshal(githubPayload)
	req, _ := http.NewRequest("POST", "http://localhost:8080/webhook", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-GitHub-Event", "push")
	req.Header.Set("X-Hub-Signature-256", "sha256=test")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err = client.Do(req)
	if err != nil {
		fmt.Printf("❌ GitHub webhook failed: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		fmt.Printf("✅ GitHub webhook response: %s\n", string(body))
	}

	// Test generic webhook
	fmt.Println("\n4. Testing generic webhook...")
	genericPayload := map[string]interface{}{
		"repo":       "https://github.com/example/chainops-demo.git",
		"commit":     "abc123def456",
		"branch":     "main",
		"author":     "Example User",
		"message":    "Add ChainOps pipeline configuration",
		"timestamp":  "2024-01-15T10:30:00Z",
		"event_type": "push",
		"ref":        "refs/heads/main",
		"repository": map[string]interface{}{
			"id":        123456,
			"name":      "chainops-demo",
			"full_name": "example/chainops-demo",
			"clone_url": "https://github.com/example/chainops-demo.git",
			"owner": map[string]interface{}{
				"login": "example",
				"name":  "Example User",
			},
		},
	}

	jsonData, _ = json.Marshal(genericPayload)
	req, _ = http.NewRequest("POST", "http://localhost:8080/webhook", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Event-Type", "push")

	resp, err = client.Do(req)
	if err != nil {
		fmt.Printf("❌ Generic webhook failed: %v\n", err)
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		fmt.Printf("✅ Generic webhook response: %s\n", string(body))
	}

	fmt.Println("\n🎉 Webhook tests completed!")
	fmt.Println("\n💡 Check the webhook server logs for detailed processing information")
}
