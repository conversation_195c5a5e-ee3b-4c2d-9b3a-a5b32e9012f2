package main

import (
	"fmt"
	"log"

	"chainops-backend/pipeline"
	"chainops-backend/webhook"
)

func main() {
	fmt.Println("🚀 Testing ChainOps Webhook Components")
	fmt.Println("=====================================")

	// Test 1: Parse a simple webhook event
	fmt.Println("\n1. Testing webhook event parsing...")
	
	event := webhook.WebhookEvent{
		Repo:      "https://github.com/example/test-repo.git",
		Commit:    "abc123def456",
		Branch:    "main",
		Author:    "Test User",
		Message:   "Add pipeline configuration",
		EventType: "push",
		Repository: webhook.Repository{
			Name:     "test-repo",
			FullName: "example/test-repo",
			CloneURL: "https://github.com/example/test-repo.git",
			Owner: webhook.User{
				Login: "example",
				Name:  "Example User",
			},
		},
	}
	
	fmt.Printf("✅ Created webhook event: %s from %s (commit: %s)\n", 
		event.EventType, event.Repository.FullName, event.Commit[:8])

	// Test 2: Check if event should be processed
	fmt.Println("\n2. Testing event filtering...")
	
	shouldProcess := webhook.ShouldProcessEvent(event)
	fmt.Printf("✅ Should process event: %v\n", shouldProcess)

	// Test 3: Test pipeline parsing
	fmt.Println("\n3. Testing pipeline parsing...")
	
	samplePipeline := `
name: Test Pipeline
version: "1.0"

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Build
        script:
          - make build

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Test
        script:
          - make test

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy
        script:
          - make deploy
`

	p, err := pipeline.ParsePipelineYAMLBytes([]byte(samplePipeline))
	if err != nil {
		log.Fatalf("Failed to parse pipeline: %v", err)
	}
	
	fmt.Printf("✅ Parsed pipeline: %s (version: %s)\n", p.Name, p.Version)
	fmt.Printf("   Jobs found: %d\n", len(p.Jobs))

	// Test 4: Build DAG
	fmt.Println("\n4. Testing DAG building...")
	
	dag, err := pipeline.BuildDAG(p)
	if err != nil {
		log.Fatalf("Failed to build DAG: %v", err)
	}
	
	stats := dag.GetDAGStats()
	fmt.Printf("✅ DAG built successfully!\n")
	fmt.Printf("   Total Jobs: %v\n", stats["total_jobs"])
	fmt.Printf("   Total Levels: %v\n", stats["total_levels"])
	fmt.Printf("   Max Parallelism: %v\n", stats["max_parallelism"])

	// Test 5: Get execution plan
	fmt.Println("\n5. Testing execution planning...")
	
	executionPlan := dag.GetExecutionPlan()
	fmt.Printf("✅ Execution plan:\n")
	for level, jobs := range executionPlan {
		fmt.Printf("   Level %d: %v\n", level, jobs)
	}

	// Test 6: Test matrix expansion
	fmt.Println("\n6. Testing matrix expansion...")
	
	matrixPipeline := `
name: Matrix Test Pipeline
version: "1.0"

jobs:
  test:
    name: Test Matrix
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16, 18, 20]
        os: [ubuntu-latest, windows-latest]
    steps:
      - name: Test
        script:
          - npm test
`

	mp, err := pipeline.ParsePipelineYAMLBytes([]byte(matrixPipeline))
	if err != nil {
		log.Fatalf("Failed to parse matrix pipeline: %v", err)
	}

	mdag, err := pipeline.BuildDAG(mp)
	if err != nil {
		log.Fatalf("Failed to build matrix DAG: %v", err)
	}

	mstats := mdag.GetDAGStats()
	fmt.Printf("✅ Matrix pipeline processed!\n")
	fmt.Printf("   Total Jobs: %v\n", mstats["total_jobs"])
	fmt.Printf("   Matrix Jobs: %v\n", mstats["matrix_jobs"])

	// Test 7: Test GitFetcher (without actual Git operations)
	fmt.Println("\n7. Testing GitFetcher initialization...")
	
	gitFetcher := webhook.NewGitFetcher("/tmp/chainops-test")
	fmt.Printf("✅ GitFetcher created with work dir: %s\n", gitFetcher.WorkDir)

	// Test 8: Test webhook handler creation
	fmt.Println("\n8. Testing webhook handler...")
	
	handler := webhook.NewHandler("test-secret", func(event webhook.WebhookEvent) {
		fmt.Printf("   📨 Received event: %s from %s\n", event.EventType, event.Repository.FullName)
	})
	
	fmt.Printf("✅ Webhook handler created with %d providers\n", len(handler.Providers))

	fmt.Println("\n🎉 All webhook component tests passed!")
	fmt.Println("\n💡 To test the full webhook server:")
	fmt.Println("   1. Run: go run webhook/main.go webhook/webhook.go webhook/gitfetch.go")
	fmt.Println("   2. Test: curl -X POST http://localhost:8080/health")
	fmt.Println("   3. Send webhook: ./webhook/test_webhook.sh")
}
