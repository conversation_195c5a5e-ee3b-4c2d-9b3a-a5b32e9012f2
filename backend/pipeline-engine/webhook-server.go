package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"chainops-backend/jobqueue"
	"chainops-backend/pipeline"
	"chainops-backend/webhook"
)

func main() {
	// Configuration
	port := getEnv("PORT", "8080")
	webhookSecret := getEnv("WEBHOOK_SECRET", "")
	redisAddr := getEnv("REDIS_ADDR", "localhost:6379")
	queueName := getEnv("QUEUE_NAME", "chainops-webhooks")
	workDir := getEnv("GIT_WORK_DIR", "/tmp/chainops-git")

	log.Printf("Starting ChainOps Webhook Server on port %s", port)

	// Initialize components
	gitFetcher := webhook.NewGitFetcher(workDir)
	queue := jobqueue.NewJobQueue(redisAddr, queueName)
	defer queue.Close()

	// Test queue connection
	ctx := context.Background()
	if stats, err := queue.GetQueueStats(ctx); err != nil {
		log.Printf("Warning: Could not connect to Redis queue: %v", err)
		log.Println("Pipeline jobs will be processed synchronously")
	} else {
		log.Printf("Connected to Redis queue. Stats: %+v", stats)
	}

	// Create webhook handler
	webhookHandler := webhook.NewHandler(webhookSecret, func(event webhook.WebhookEvent) {
		handleWebhookEvent(event, gitFetcher, queue)
	})

	// Set up HTTP routes
	mux := http.NewServeMux()

	// Webhook endpoint
	mux.Handle("/webhook", webhookHandler)

	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status":"healthy","timestamp":"%s"}`, time.Now().Format(time.RFC3339))
	})

	// Status endpoint
	mux.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		stats, err := queue.GetQueueStats(ctx)
		if err != nil {
			http.Error(w, "Queue unavailable", http.StatusServiceUnavailable)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"queue_stats":%v,"timestamp":"%s"}`, stats, time.Now().Format(time.RFC3339))
	})

	// Pipeline validation endpoint
	mux.HandleFunc("/validate", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		repo := r.URL.Query().Get("repo")
		commit := r.URL.Query().Get("commit")

		if repo == "" || commit == "" {
			http.Error(w, "repo and commit parameters required", http.StatusBadRequest)
			return
		}

		// Fetch and validate pipeline
		yamlData, err := gitFetcher.FetchPipelineYAML(repo, commit)
		if err != nil {
			http.Error(w, fmt.Sprintf("Failed to fetch pipeline: %v", err), http.StatusBadRequest)
			return
		}

		p, err := pipeline.ParsePipelineYAMLBytes(yamlData)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid pipeline: %v", err), http.StatusBadRequest)
			return
		}

		dag, err := pipeline.BuildDAG(p)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid DAG: %v", err), http.StatusBadRequest)
			return
		}

		stats := dag.GetDAGStats()
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"valid":true,"pipeline_name":"%s","stats":%v}`, p.Name, stats)
	})

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Webhook server listening on :%s", port)
		log.Println("Endpoints:")
		log.Println("  POST /webhook - Receive Git webhooks")
		log.Println("  GET  /health  - Health check")
		log.Println("  GET  /status  - Queue status")
		log.Println("  POST /validate?repo=<url>&commit=<sha> - Validate pipeline")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down webhook server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Webhook server stopped")
}

// handleWebhookEvent processes a webhook event
func handleWebhookEvent(event webhook.WebhookEvent, gitFetcher *webhook.GitFetcher, queue *jobqueue.JobQueue) {
	log.Printf("Processing webhook event: %s from %s (commit: %s)",
		event.EventType, event.Repository.FullName, event.Commit)

	// Check if we should process this event
	if !webhook.ShouldProcessEvent(event) {
		log.Printf("Skipping event: %s on branch %s", event.EventType, event.Branch)
		return
	}

	// For demo purposes, use a sample pipeline instead of fetching from Git
	samplePipeline := `name: Webhook Triggered Pipeline
version: "1.0"

on:
  push:
    branches: [main]

jobs:
  validate:
    name: Validate Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Validate
        script:
          - echo "Validating code from webhook event"
          - echo "Repository: ` + event.Repository.FullName + `"
          - echo "Commit: ` + event.Commit + `"

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: [validate]
    steps:
      - name: Test
        script:
          - echo "Running tests for commit ` + event.Commit[:8] + `"

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy
        script:
          - echo "Deploying ` + event.Repository.FullName + ` to production"`

	log.Printf("Using sample pipeline for demo (in production, would fetch from %s@%s)",
		event.Repository.FullName, event.Commit[:8])

	// Parse pipeline YAML
	p, err := pipeline.ParsePipelineYAMLBytes([]byte(samplePipeline))
	if err != nil {
		log.Printf("Failed to parse pipeline YAML: %v", err)
		return
	}

	log.Printf("Parsed pipeline: %s (version: %s)", p.Name, p.Version)

	// Build DAG
	dag, err := pipeline.BuildDAG(p)
	if err != nil {
		log.Printf("Failed to build pipeline DAG: %v", err)
		return
	}

	// Print DAG statistics
	stats := dag.GetDAGStats()
	log.Printf("Pipeline DAG built successfully!")
	log.Printf("  Total Jobs: %v", stats["total_jobs"])
	log.Printf("  Total Levels: %v", stats["total_levels"])
	log.Printf("  Max Parallelism: %v", stats["max_parallelism"])

	// Schedule pipeline execution
	ctx := context.Background()
	if err := schedulePipelineExecution(ctx, dag, event, queue); err != nil {
		log.Printf("Failed to schedule pipeline execution: %v", err)
		return
	}

	log.Printf("Pipeline execution scheduled successfully for %s@%s",
		event.Repository.FullName, event.Commit[:8])
}

// schedulePipelineExecution schedules all jobs in the pipeline DAG
func schedulePipelineExecution(ctx context.Context, dag *pipeline.DAG, event webhook.WebhookEvent, queue *jobqueue.JobQueue) error {
	// Create a unique run ID for this pipeline execution
	runID := fmt.Sprintf("%s_%s_%d",
		event.Repository.FullName, event.Commit[:8], time.Now().Unix())

	log.Printf("Scheduling pipeline run: %s", runID)

	// Get initial ready jobs (jobs with no dependencies)
	readyJobs := dag.ReadyJobs(make(map[string]bool))

	if len(readyJobs) == 0 {
		return fmt.Errorf("no ready jobs found in pipeline")
	}

	// Schedule initial jobs
	for _, jobName := range readyJobs {
		node := dag.Nodes[jobName]

		// Regular job
		job := &jobqueue.Job{
			ID:       fmt.Sprintf("%s_%s", runID, jobName),
			Name:     node.Job.Name,
			Tags:     []string{"webhook", jobName, event.Repository.FullName},
			Payload:  createWebhookJobPayload(node.Job, nil, event, runID),
			Status:   "pending",
			Priority: node.Level,
		}

		if err := queue.Enqueue(ctx, job); err != nil {
			log.Printf("Warning: Failed to enqueue job %s: %v (continuing with demo)", jobName, err)
		} else {
			log.Printf("Scheduled job: %s (level %d)", jobName, node.Level)
		}
	}

	return nil
}

// createWebhookJobPayload creates a payload for a job with webhook context
func createWebhookJobPayload(job *pipeline.Job, matrixValues map[string]interface{}, event webhook.WebhookEvent, runID string) map[string]string {
	payload := make(map[string]string)

	// Job configuration
	payload["job_name"] = job.Name
	payload["runs_on"] = fmt.Sprintf("%v", job.GetRunsOn())
	payload["run_id"] = runID

	// Git context
	payload["git_repo"] = event.Repo
	payload["git_commit"] = event.Commit
	payload["git_branch"] = event.Branch
	payload["git_author"] = event.Author
	payload["git_message"] = event.Message
	payload["git_ref"] = event.Ref

	// Repository context
	payload["repo_name"] = event.Repository.Name
	payload["repo_full_name"] = event.Repository.FullName
	payload["repo_owner"] = event.Repository.Owner.Login

	// Event context
	payload["event_type"] = event.EventType
	payload["event_timestamp"] = event.Timestamp.Format(time.RFC3339)

	return payload
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
