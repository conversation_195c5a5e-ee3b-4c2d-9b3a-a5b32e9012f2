name: Demo Webhook Pipeline
version: "1.0"

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: "18"
  ENVIRONMENT: "demo"

jobs:
  validate:
    name: Validate Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Validate syntax
        script:
          - echo "Validating code syntax..."
          - find . -name "*.js" -exec node -c {} \;

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: [validate]
    strategy:
      matrix:
        test-type: [unit, integration, e2e]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Install dependencies
        script:
          - npm ci
      - name: Run ${{ matrix.test-type }} tests
        script:
          - npm run test:${{ matrix.test-type }}

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Install dependencies
        script:
          - npm ci
      - name: Build application
        script:
          - npm run build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: dist/

  deploy:
    name: Deploy to Demo
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    environment:
      name: demo
      url: https://demo.chainops.dev
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
          path: ./dist
      - name: Deploy to demo environment
        script:
          - echo "Deploying to demo environment..."
          - kubectl apply -f k8s/demo/
        env:
          KUBECONFIG: ${{ secrets.DEMO_KUBECONFIG }}
