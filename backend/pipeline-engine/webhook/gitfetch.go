package webhook

import (
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// GitFetcher handles fetching pipeline configurations from Git repositories
type GitFetcher struct {
	WorkDir    string // Working directory for git operations
	Timeout    time.Duration
	CleanupAge time.Duration // How long to keep cloned repos
}

// NewGitFetcher creates a new GitFetcher instance
func NewGitFetcher(workDir string) *GitFetcher {
	if workDir == "" {
		workDir = "/tmp/chainops-git"
	}

	return &GitFetcher{
		WorkDir:    workDir,
		Timeout:    5 * time.Minute,
		CleanupAge: 1 * time.Hour,
	}
}

// FetchPipelineYAML fetches the pipeline YAML file from a repository at a specific commit
func (gf *GitFetcher) FetchPipelineYAML(repo, commit string) ([]byte, error) {
	// Create unique directory for this fetch
	repoName := extractRepoName(repo)
	fetchDir := filepath.Join(gf.WorkDir, fmt.Sprintf("%s_%s_%d", repoName, commit[:8], time.Now().Unix()))

	// Ensure work directory exists
	if err := os.MkdirAll(gf.WorkDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create work directory: %w", err)
	}

	// Clean up old directories
	go gf.cleanup()

	// Clone repository
	if err := gf.cloneRepo(repo, commit, fetchDir); err != nil {
		return nil, fmt.Errorf("failed to clone repository: %w", err)
	}

	// Defer cleanup of this specific directory
	defer func() {
		os.RemoveAll(fetchDir)
	}()

	// Look for pipeline files in order of preference
	pipelineFiles := []string{
		".chainops.yml",
		".chainops.yaml",
		".chainops/pipeline.yml",
		".chainops/pipeline.yaml",
		".github/workflows/chainops.yml",
		".github/workflows/chainops.yaml",
		"chainops.yml",
		"chainops.yaml",
	}

	for _, filename := range pipelineFiles {
		filePath := filepath.Join(fetchDir, filename)
		if data, err := ioutil.ReadFile(filePath); err == nil {
			return data, nil
		}
	}

	return nil, fmt.Errorf("no pipeline configuration file found in repository")
}

// FetchFileAtCommit fetches any file from a repository at a specific commit
func (gf *GitFetcher) FetchFileAtCommit(repo, commit, filePath string) ([]byte, error) {
	repoName := extractRepoName(repo)
	fetchDir := filepath.Join(gf.WorkDir, fmt.Sprintf("%s_%s_%d", repoName, commit[:8], time.Now().Unix()))

	if err := os.MkdirAll(gf.WorkDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create work directory: %w", err)
	}

	if err := gf.cloneRepo(repo, commit, fetchDir); err != nil {
		return nil, fmt.Errorf("failed to clone repository: %w", err)
	}

	defer os.RemoveAll(fetchDir)

	fullPath := filepath.Join(fetchDir, filePath)
	data, err := ioutil.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	return data, nil
}

// ListPipelineFiles lists all potential pipeline files in a repository
func (gf *GitFetcher) ListPipelineFiles(repo, commit string) ([]string, error) {
	repoName := extractRepoName(repo)
	fetchDir := filepath.Join(gf.WorkDir, fmt.Sprintf("%s_%s_%d", repoName, commit[:8], time.Now().Unix()))

	if err := os.MkdirAll(gf.WorkDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create work directory: %w", err)
	}

	if err := gf.cloneRepo(repo, commit, fetchDir); err != nil {
		return nil, fmt.Errorf("failed to clone repository: %w", err)
	}

	defer os.RemoveAll(fetchDir)

	var pipelineFiles []string
	patterns := []string{
		"*.chainops.yml",
		"*.chainops.yaml",
		".chainops/*.yml",
		".chainops/*.yaml",
		".github/workflows/*.yml",
		".github/workflows/*.yaml",
	}

	for _, pattern := range patterns {
		matches, err := filepath.Glob(filepath.Join(fetchDir, pattern))
		if err != nil {
			continue
		}

		for _, match := range matches {
			relPath, err := filepath.Rel(fetchDir, match)
			if err != nil {
				continue
			}
			pipelineFiles = append(pipelineFiles, relPath)
		}
	}

	return pipelineFiles, nil
}

// cloneRepo clones a repository to a specific directory and checks out a commit
func (gf *GitFetcher) cloneRepo(repo, commit, dir string) error {
	// Clone with depth 1 for efficiency
	cloneCmd := exec.Command("git", "clone", "--depth=50", repo, dir)
	cloneCmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0") // Disable interactive prompts

	if output, err := cloneCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("git clone failed: %s, output: %s", err, string(output))
	}

	// Checkout specific commit
	checkoutCmd := exec.Command("git", "-C", dir, "checkout", commit)
	if output, err := checkoutCmd.CombinedOutput(); err != nil {
		// If checkout fails, try to fetch more history
		fetchCmd := exec.Command("git", "-C", dir, "fetch", "--depth=200")
		fetchCmd.Run()

		// Try checkout again
		checkoutCmd = exec.Command("git", "-C", dir, "checkout", commit)
		if output2, err := checkoutCmd.CombinedOutput(); err != nil {
			return fmt.Errorf("git checkout failed: %s, output: %s", err, string(output2))
		}
	} else if len(output) > 0 {
		// Checkout succeeded on first try
	}

	return nil
}

// cleanup removes old clone directories
func (gf *GitFetcher) cleanup() {
	entries, err := ioutil.ReadDir(gf.WorkDir)
	if err != nil {
		return
	}

	cutoff := time.Now().Add(-gf.CleanupAge)

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		dirPath := filepath.Join(gf.WorkDir, entry.Name())
		if entry.ModTime().Before(cutoff) {
			os.RemoveAll(dirPath)
		}
	}
}

// extractRepoName extracts a clean repository name from a URL
func extractRepoName(repo string) string {
	// Remove .git suffix
	repo = strings.TrimSuffix(repo, ".git")

	// Extract name from URL
	parts := strings.Split(repo, "/")
	if len(parts) > 0 {
		name := parts[len(parts)-1]
		// Clean up the name for use as directory
		name = strings.ReplaceAll(name, ":", "_")
		name = strings.ReplaceAll(name, "@", "_")
		return name
	}

	return "unknown"
}

// GitInfo represents information about a Git repository
type GitInfo struct {
	URL       string
	Branch    string
	Commit    string
	Author    string
	Message   string
	Timestamp time.Time
	Files     []string
}

// GetRepoInfo gets information about a repository at a specific commit
func (gf *GitFetcher) GetRepoInfo(repo, commit string) (*GitInfo, error) {
	repoName := extractRepoName(repo)
	fetchDir := filepath.Join(gf.WorkDir, fmt.Sprintf("%s_%s_%d", repoName, commit[:8], time.Now().Unix()))

	if err := os.MkdirAll(gf.WorkDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create work directory: %w", err)
	}

	if err := gf.cloneRepo(repo, commit, fetchDir); err != nil {
		return nil, fmt.Errorf("failed to clone repository: %w", err)
	}

	defer os.RemoveAll(fetchDir)

	info := &GitInfo{
		URL:    repo,
		Commit: commit,
	}

	// Get commit information
	showCmd := exec.Command("git", "-C", fetchDir, "show", "--format=%an|%s|%ct", "--name-only", commit)
	output, err := showCmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to get commit info: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	if len(lines) > 0 {
		parts := strings.Split(lines[0], "|")
		if len(parts) >= 3 {
			info.Author = parts[0]
			info.Message = parts[1]
			// Parse timestamp if needed
		}

		// Files changed in this commit
		for i := 1; i < len(lines); i++ {
			if strings.TrimSpace(lines[i]) != "" {
				info.Files = append(info.Files, strings.TrimSpace(lines[i]))
			}
		}
	}

	// Get current branch
	branchCmd := exec.Command("git", "-C", fetchDir, "rev-parse", "--abbrev-ref", "HEAD")
	if branchOutput, err := branchCmd.Output(); err == nil {
		info.Branch = strings.TrimSpace(string(branchOutput))
	}

	return info, nil
}

// ValidateRepository checks if a repository URL is accessible
func (gf *GitFetcher) ValidateRepository(repo string) error {
	// Try to list remote references
	lsRemoteCmd := exec.Command("git", "ls-remote", "--heads", repo)
	lsRemoteCmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0")

	if output, err := lsRemoteCmd.CombinedOutput(); err != nil {
		return fmt.Errorf("repository not accessible: %s, output: %s", err, string(output))
	}

	return nil
}

// GetDefaultBranch gets the default branch of a repository
func (gf *GitFetcher) GetDefaultBranch(repo string) (string, error) {
	lsRemoteCmd := exec.Command("git", "ls-remote", "--symref", repo, "HEAD")
	lsRemoteCmd.Env = append(os.Environ(), "GIT_TERMINAL_PROMPT=0")

	output, err := lsRemoteCmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get default branch: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "ref: refs/heads/") {
			return strings.TrimPrefix(line, "ref: refs/heads/"), nil
		}
	}

	// Fallback to common default branches
	return "main", nil
}
