#!/bin/bash

# Test script for ChainOps Webhook Server
# This script demonstrates how to send webhook events to the server

WEBHOOK_URL="http://localhost:8080/webhook"
HEALTH_URL="http://localhost:8080/health"
STATUS_URL="http://localhost:8080/status"
VALIDATE_URL="http://localhost:8080/validate"

echo "🚀 Testing ChainOps Webhook Server"
echo "=================================="

# Test health endpoint
echo "📊 Testing health endpoint..."
curl -s "$HEALTH_URL" | jq '.' || echo "Health check failed"
echo ""

# Test status endpoint
echo "📊 Testing status endpoint..."
curl -s "$STATUS_URL" | jq '.' || echo "Status check failed (Redis may not be running)"
echo ""

# Test GitHub webhook
echo "🐙 Testing GitHub webhook..."
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-GitHub-Event: push" \
  -H "X-Hub-Signature-256: sha256=test" \
  -d '{
    "ref": "refs/heads/main",
    "before": "0000000000000000000000000000000000000000",
    "after": "1234567890abcdef1234567890abcdef12345678",
    "repository": {
      "id": 123456,
      "name": "chainops-demo",
      "full_name": "example/chainops-demo",
      "clone_url": "https://github.com/example/chainops-demo.git",
      "ssh_url": "**************:example/chainops-demo.git",
      "html_url": "https://github.com/example/chainops-demo",
      "private": false,
      "owner": {
        "id": 12345,
        "login": "example",
        "name": "Example User",
        "email": "<EMAIL>",
        "avatar_url": "https://github.com/images/error/example_happy.gif"
      }
    },
    "pusher": {
      "name": "example",
      "email": "<EMAIL>"
    },
    "head_commit": {
      "id": "1234567890abcdef1234567890abcdef12345678",
      "tree_id": "abcdef1234567890abcdef1234567890abcdef12",
      "message": "Add ChainOps pipeline configuration",
      "timestamp": "2024-01-15T10:30:00Z",
      "url": "https://github.com/example/chainops-demo/commit/1234567890abcdef1234567890abcdef12345678",
      "author": {
        "name": "Example User",
        "email": "<EMAIL>"
      },
      "committer": {
        "name": "Example User",
        "email": "<EMAIL>"
      },
      "added": [".chainops.yml"],
      "removed": [],
      "modified": ["README.md"]
    },
    "commits": [
      {
        "id": "1234567890abcdef1234567890abcdef12345678",
        "tree_id": "abcdef1234567890abcdef1234567890abcdef12",
        "message": "Add ChainOps pipeline configuration",
        "timestamp": "2024-01-15T10:30:00Z",
        "url": "https://github.com/example/chainops-demo/commit/1234567890abcdef1234567890abcdef12345678",
        "author": {
          "name": "Example User",
          "email": "<EMAIL>"
        },
        "committer": {
          "name": "Example User",
          "email": "<EMAIL>"
        },
        "added": [".chainops.yml"],
        "removed": [],
        "modified": ["README.md"]
      }
    ]
  }' | jq '.' || echo "GitHub webhook test failed"
echo ""

# Test GitLab webhook
echo "🦊 Testing GitLab webhook..."
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-Gitlab-Event: Push Hook" \
  -H "X-Gitlab-Token: test-token" \
  -d '{
    "object_kind": "push",
    "event_name": "push",
    "before": "0000000000000000000000000000000000000000",
    "after": "1234567890abcdef1234567890abcdef12345678",
    "ref": "refs/heads/main",
    "checkout_sha": "1234567890abcdef1234567890abcdef12345678",
    "message": "Add ChainOps pipeline configuration",
    "user_id": 123,
    "user_name": "Example User",
    "user_username": "example",
    "user_email": "<EMAIL>",
    "user_avatar": "https://gitlab.com/uploads/-/system/user/avatar/123/avatar.png",
    "project_id": 456,
    "project": {
      "id": 456,
      "name": "chainops-demo",
      "full_name": "example/chainops-demo",
      "clone_url": "https://gitlab.com/example/chainops-demo.git",
      "ssh_url": "**************:example/chainops-demo.git",
      "html_url": "https://gitlab.com/example/chainops-demo",
      "private": false,
      "owner": {
        "id": 123,
        "login": "example",
        "name": "Example User",
        "email": "<EMAIL>"
      }
    },
    "commits": [
      {
        "id": "1234567890abcdef1234567890abcdef12345678",
        "message": "Add ChainOps pipeline configuration",
        "timestamp": "2024-01-15T10:30:00Z",
        "url": "https://gitlab.com/example/chainops-demo/-/commit/1234567890abcdef1234567890abcdef12345678",
        "author": {
          "name": "Example User",
          "email": "<EMAIL>"
        },
        "added": [".chainops.yml"],
        "modified": ["README.md"],
        "removed": []
      }
    ],
    "repository": {
      "name": "chainops-demo",
      "url": "**************:example/chainops-demo.git",
      "description": "ChainOps Demo Repository",
      "homepage": "https://gitlab.com/example/chainops-demo",
      "git_http_url": "https://gitlab.com/example/chainops-demo.git",
      "git_ssh_url": "**************:example/chainops-demo.git",
      "visibility_level": 20
    }
  }' | jq '.' || echo "GitLab webhook test failed"
echo ""

# Test generic webhook
echo "🔧 Testing generic webhook..."
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-Event-Type: push" \
  -d '{
    "repo": "https://github.com/example/chainops-demo.git",
    "commit": "1234567890abcdef1234567890abcdef12345678",
    "branch": "main",
    "author": "Example User",
    "message": "Add ChainOps pipeline configuration",
    "timestamp": "2024-01-15T10:30:00Z",
    "event_type": "push",
    "ref": "refs/heads/main",
    "repository": {
      "id": 123456,
      "name": "chainops-demo",
      "full_name": "example/chainops-demo",
      "clone_url": "https://github.com/example/chainops-demo.git",
      "owner": {
        "login": "example",
        "name": "Example User"
      }
    }
  }' | jq '.' || echo "Generic webhook test failed"
echo ""

# Test pipeline validation (this will fail since the repo doesn't exist)
echo "✅ Testing pipeline validation..."
curl -X POST "$VALIDATE_URL?repo=https://github.com/example/chainops-demo.git&commit=main" \
  -H "Content-Type: application/json" | jq '.' || echo "Pipeline validation failed (expected - demo repo doesn't exist)"
echo ""

echo "🎉 Webhook tests completed!"
echo ""
echo "💡 Tips:"
echo "  - Start the webhook server with: go run webhook/main.go"
echo "  - Check server logs for detailed processing information"
echo "  - Use a real repository URL for actual testing"
echo "  - Configure webhook secrets for production use"
