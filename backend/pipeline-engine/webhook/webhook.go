package webhook

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
	"time"
)

// WebhookEvent represents a generic webhook event
type WebhookEvent struct {
	Repo       string            `json:"repo"`
	Commit     string            `json:"commit"`
	Branch     string            `json:"branch"`
	Author     string            `json:"author"`
	Message    string            `json:"message"`
	Timestamp  time.Time         `json:"timestamp"`
	EventType  string            `json:"event_type"`
	Ref        string            `json:"ref"`
	Repository Repository        `json:"repository"`
	Pusher     User              `json:"pusher"`
	HeadCommit Commit            `json:"head_commit"`
	Commits    []Commit          `json:"commits"`
	Headers    map[string]string `json:"headers"`
}

// Repository represents repository information
type Repository struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	CloneURL string `json:"clone_url"`
	SSHURL   string `json:"ssh_url"`
	HTMLURL  string `json:"html_url"`
	Private  bool   `json:"private"`
	Owner    User   `json:"owner"`
}

// User represents a user or organization
type User struct {
	ID       int    `json:"id"`
	Login    string `json:"login"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	AvatarURL string `json:"avatar_url"`
}

// Commit represents a git commit
type Commit struct {
	ID        string    `json:"id"`
	TreeID    string    `json:"tree_id"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	URL       string    `json:"url"`
	Author    User      `json:"author"`
	Committer User      `json:"committer"`
	Added     []string  `json:"added"`
	Removed   []string  `json:"removed"`
	Modified  []string  `json:"modified"`
}

// GitHubWebhookEvent represents GitHub-specific webhook payload
type GitHubWebhookEvent struct {
	Action     string     `json:"action"`
	Ref        string     `json:"ref"`
	Before     string     `json:"before"`
	After      string     `json:"after"`
	Repository Repository `json:"repository"`
	Pusher     User       `json:"pusher"`
	Sender     User       `json:"sender"`
	HeadCommit Commit     `json:"head_commit"`
	Commits    []Commit   `json:"commits"`
	Compare    string     `json:"compare"`
	Created    bool       `json:"created"`
	Deleted    bool       `json:"deleted"`
	Forced     bool       `json:"forced"`
}

// GitLabWebhookEvent represents GitLab-specific webhook payload
type GitLabWebhookEvent struct {
	ObjectKind   string     `json:"object_kind"`
	EventName    string     `json:"event_name"`
	Before       string     `json:"before"`
	After        string     `json:"after"`
	Ref          string     `json:"ref"`
	CheckoutSHA  string     `json:"checkout_sha"`
	Message      string     `json:"message"`
	UserID       int        `json:"user_id"`
	UserName     string     `json:"user_name"`
	UserUsername string     `json:"user_username"`
	UserEmail    string     `json:"user_email"`
	UserAvatar   string     `json:"user_avatar"`
	ProjectID    int        `json:"project_id"`
	Project      Repository `json:"project"`
	Commits      []Commit   `json:"commits"`
	Repository   Repository `json:"repository"`
}

// Handler handles webhook HTTP requests
type Handler struct {
	Secret    string                        // Webhook secret for signature validation
	OnEvent   func(event WebhookEvent)      // Callback for processed events
	OnError   func(error)                   // Error callback
	Providers map[string]WebhookProvider    // Supported webhook providers
}

// WebhookProvider defines how to parse provider-specific webhooks
type WebhookProvider interface {
	ParseEvent(body []byte, headers map[string]string) (*WebhookEvent, error)
	ValidateSignature(body []byte, signature string, secret string) bool
	GetEventType(headers map[string]string) string
}

// NewHandler creates a new webhook handler
func NewHandler(secret string, onEvent func(WebhookEvent)) *Handler {
	handler := &Handler{
		Secret:    secret,
		OnEvent:   onEvent,
		OnError:   func(err error) { log.Printf("Webhook error: %v", err) },
		Providers: make(map[string]WebhookProvider),
	}
	
	// Register default providers
	handler.Providers["github"] = &GitHubProvider{}
	handler.Providers["gitlab"] = &GitLabProvider{}
	handler.Providers["generic"] = &GenericProvider{}
	
	return handler
}

// ServeHTTP implements http.Handler interface
func (h *Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	
	// Read request body
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		h.OnError(fmt.Errorf("failed to read request body: %w", err))
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	
	// Extract headers
	headers := make(map[string]string)
	for key, values := range r.Header {
		if len(values) > 0 {
			headers[strings.ToLower(key)] = values[0]
		}
	}
	
	// Determine provider
	provider := h.detectProvider(headers)
	if provider == nil {
		h.OnError(fmt.Errorf("unsupported webhook provider"))
		http.Error(w, "Unsupported provider", http.StatusBadRequest)
		return
	}
	
	// Validate signature if secret is provided
	if h.Secret != "" {
		signature := headers["x-hub-signature-256"] // GitHub format
		if signature == "" {
			signature = headers["x-gitlab-token"] // GitLab format
		}
		
		if signature != "" && !provider.ValidateSignature(body, signature, h.Secret) {
			h.OnError(fmt.Errorf("invalid webhook signature"))
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
	}
	
	// Parse event
	event, err := provider.ParseEvent(body, headers)
	if err != nil {
		h.OnError(fmt.Errorf("failed to parse webhook event: %w", err))
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	
	// Log received event
	log.Printf("Received webhook event: %s from %s (commit: %s)", 
		event.EventType, event.Repository.FullName, event.Commit)
	
	// Process event asynchronously
	go func() {
		defer func() {
			if r := recover(); r != nil {
				h.OnError(fmt.Errorf("panic in webhook handler: %v", r))
			}
		}()
		h.OnEvent(*event)
	}()
	
	// Send response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "success",
		"message": "Webhook received and processed",
	})
}

// detectProvider determines the webhook provider based on headers
func (h *Handler) detectProvider(headers map[string]string) WebhookProvider {
	// GitHub
	if headers["x-github-event"] != "" {
		return h.Providers["github"]
	}
	
	// GitLab
	if headers["x-gitlab-event"] != "" {
		return h.Providers["gitlab"]
	}
	
	// Generic/fallback
	return h.Providers["generic"]
}

// GitHubProvider implements GitHub webhook parsing
type GitHubProvider struct{}

func (p *GitHubProvider) ParseEvent(body []byte, headers map[string]string) (*WebhookEvent, error) {
	var payload GitHubWebhookEvent
	if err := json.Unmarshal(body, &payload); err != nil {
		return nil, fmt.Errorf("failed to parse GitHub webhook: %w", err)
	}
	
	event := &WebhookEvent{
		Repo:       payload.Repository.CloneURL,
		Commit:     payload.After,
		Branch:     extractBranchFromRef(payload.Ref),
		Author:     payload.HeadCommit.Author.Name,
		Message:    payload.HeadCommit.Message,
		Timestamp:  payload.HeadCommit.Timestamp,
		EventType:  headers["x-github-event"],
		Ref:        payload.Ref,
		Repository: payload.Repository,
		Pusher:     payload.Pusher,
		HeadCommit: payload.HeadCommit,
		Commits:    payload.Commits,
		Headers:    headers,
	}
	
	return event, nil
}

func (p *GitHubProvider) ValidateSignature(body []byte, signature string, secret string) bool {
	if !strings.HasPrefix(signature, "sha256=") {
		return false
	}
	
	expectedSignature := signature[7:] // Remove "sha256=" prefix
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(body)
	actualSignature := hex.EncodeToString(mac.Sum(nil))
	
	return hmac.Equal([]byte(expectedSignature), []byte(actualSignature))
}

func (p *GitHubProvider) GetEventType(headers map[string]string) string {
	return headers["x-github-event"]
}

// GitLabProvider implements GitLab webhook parsing
type GitLabProvider struct{}

func (p *GitLabProvider) ParseEvent(body []byte, headers map[string]string) (*WebhookEvent, error) {
	var payload GitLabWebhookEvent
	if err := json.Unmarshal(body, &payload); err != nil {
		return nil, fmt.Errorf("failed to parse GitLab webhook: %w", err)
	}
	
	event := &WebhookEvent{
		Repo:       payload.Repository.CloneURL,
		Commit:     payload.After,
		Branch:     extractBranchFromRef(payload.Ref),
		Author:     payload.UserName,
		Message:    payload.Message,
		Timestamp:  time.Now(), // GitLab doesn't always provide timestamp
		EventType:  payload.ObjectKind,
		Ref:        payload.Ref,
		Repository: payload.Repository,
		HeadCommit: Commit{ID: payload.After, Message: payload.Message},
		Commits:    payload.Commits,
		Headers:    headers,
	}
	
	return event, nil
}

func (p *GitLabProvider) ValidateSignature(body []byte, signature string, secret string) bool {
	return signature == secret // GitLab uses simple token comparison
}

func (p *GitLabProvider) GetEventType(headers map[string]string) string {
	return headers["x-gitlab-event"]
}

// GenericProvider implements generic webhook parsing
type GenericProvider struct{}

func (p *GenericProvider) ParseEvent(body []byte, headers map[string]string) (*WebhookEvent, error) {
	var event WebhookEvent
	if err := json.Unmarshal(body, &event); err != nil {
		return nil, fmt.Errorf("failed to parse generic webhook: %w", err)
	}
	
	event.Headers = headers
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}
	
	return &event, nil
}

func (p *GenericProvider) ValidateSignature(body []byte, signature string, secret string) bool {
	// No signature validation for generic provider
	return true
}

func (p *GenericProvider) GetEventType(headers map[string]string) string {
	return headers["x-event-type"]
}

// Helper functions
func extractBranchFromRef(ref string) string {
	if strings.HasPrefix(ref, "refs/heads/") {
		return strings.TrimPrefix(ref, "refs/heads/")
	}
	return ref
}

// ShouldProcessEvent determines if an event should trigger pipeline execution
func ShouldProcessEvent(event WebhookEvent) bool {
	// Only process push events to main branches
	if event.EventType != "push" {
		return false
	}
	
	// Skip deleted branches
	if event.Commit == "0000000000000000000000000000000000000000" {
		return false
	}
	
	// Process main/master/develop branches
	mainBranches := []string{"main", "master", "develop", "staging", "production"}
	for _, branch := range mainBranches {
		if event.Branch == branch {
			return true
		}
	}
	
	return false
}
