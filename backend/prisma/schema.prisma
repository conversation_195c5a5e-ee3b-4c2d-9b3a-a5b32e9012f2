// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  firstName String?
  lastName  String?
  avatar    String?
  password  String
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  projects     ProjectMember[]
  pipelines    Pipeline[]
  jobs         Job[]
  tokens       ApiToken[]
  notifications Notification[]

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  avatar      String?
  settings    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  projects Project[]
  members  OrganizationMember[]

  @@map("organizations")
}

model OrganizationMember {
  id             String       @id @default(cuid())
  organizationId String
  userId         String
  role           OrgRole      @default(MEMBER)
  joinedAt       DateTime     @default(now())

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@map("organization_members")
}

model Project {
  id             String   @id @default(cuid())
  name           String
  slug           String
  description    String?
  repositoryUrl  String?
  defaultBranch  String   @default("main")
  isActive       Boolean  @default(true)
  settings       Json?
  organizationId String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization?   @relation(fields: [organizationId], references: [id])
  members      ProjectMember[]
  pipelines    Pipeline[]
  environments Environment[]
  secrets      Secret[]

  @@unique([organizationId, slug])
  @@map("projects")
}

model ProjectMember {
  id        String      @id @default(cuid())
  projectId String
  userId    String
  role      ProjectRole @default(DEVELOPER)
  joinedAt  DateTime    @default(now())

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model Environment {
  id          String   @id @default(cuid())
  name        String
  slug        String
  description String?
  projectId   String
  variables   Json?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  jobs    Job[]

  @@unique([projectId, slug])
  @@map("environments")
}

model Pipeline {
  id          String         @id @default(cuid())
  name        String
  slug        String
  description String?
  projectId   String
  userId      String
  config      Json
  isActive    Boolean        @default(true)
  status      PipelineStatus @default(IDLE)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])
  runs    PipelineRun[]
  triggers PipelineTrigger[]

  @@unique([projectId, slug])
  @@map("pipelines")
}

model PipelineTrigger {
  id         String      @id @default(cuid())
  pipelineId String
  type       TriggerType
  config     Json
  isActive   Boolean     @default(true)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  pipeline Pipeline @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  @@map("pipeline_triggers")
}

model PipelineRun {
  id         String           @id @default(cuid())
  number     Int
  pipelineId String
  status     PipelineRunStatus @default(PENDING)
  trigger    Json?
  variables  Json?
  startedAt  DateTime?
  finishedAt DateTime?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  // Relations
  pipeline Pipeline @relation(fields: [pipelineId], references: [id], onDelete: Cascade)
  jobs     Job[]

  @@unique([pipelineId, number])
  @@map("pipeline_runs")
}

model Job {
  id            String        @id @default(cuid())
  name          String
  pipelineRunId String
  userId        String
  environmentId String?
  status        JobStatus     @default(PENDING)
  config        Json
  logs          String?
  artifacts     Json?
  startedAt     DateTime?
  finishedAt    DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  pipelineRun PipelineRun  @relation(fields: [pipelineRunId], references: [id], onDelete: Cascade)
  user        User         @relation(fields: [userId], references: [id])
  environment Environment? @relation(fields: [environmentId], references: [id])

  @@map("jobs")
}

model Secret {
  id          String   @id @default(cuid())
  key         String
  value       String
  description String?
  projectId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, key])
  @@map("secrets")
}

model ApiToken {
  id          String   @id @default(cuid())
  name        String
  token       String   @unique
  userId      String
  permissions Json?
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_tokens")
}

model Notification {
  id        String             @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean            @default(false)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Enums
enum Role {
  ADMIN
  USER
}

enum OrgRole {
  OWNER
  ADMIN
  MEMBER
}

enum ProjectRole {
  OWNER
  MAINTAINER
  DEVELOPER
  VIEWER
}

enum PipelineStatus {
  IDLE
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
}

enum PipelineRunStatus {
  PENDING
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
}

enum JobStatus {
  PENDING
  QUEUED
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
  SKIPPED
}

enum TriggerType {
  WEBHOOK
  SCHEDULE
  MANUAL
}

enum NotificationType {
  PIPELINE_SUCCESS
  PIPELINE_FAILED
  JOB_SUCCESS
  JOB_FAILED
  SYSTEM
}
