"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const adminPassword = await bcryptjs_1.default.hash('admin123', 12);
    const admin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'admin',
            firstName: 'Admin',
            lastName: 'User',
            password: adminPassword,
            role: 'ADMIN',
        },
    });
    console.log('✅ Created admin user:', admin.email);
    const demoPassword = await bcryptjs_1.default.hash('demo123', 12);
    const demoUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            username: 'demo',
            firstName: 'Demo',
            lastName: 'User',
            password: demoPassword,
            role: 'USER',
        },
    });
    console.log('✅ Created demo user:', demoUser.email);
    const demoOrg = await prisma.organization.upsert({
        where: { slug: 'demo-org' },
        update: {},
        create: {
            name: 'Demo Organization',
            slug: 'demo-org',
            description: 'A demo organization for testing ChainOps',
            members: {
                create: [
                    {
                        userId: admin.id,
                        role: 'OWNER',
                    },
                    {
                        userId: demoUser.id,
                        role: 'MEMBER',
                    },
                ],
            },
        },
    });
    console.log('✅ Created demo organization:', demoOrg.name);
    const webProject = await prisma.project.upsert({
        where: {
            organizationId_slug: {
                organizationId: demoOrg.id,
                slug: 'web-app'
            }
        },
        update: {},
        create: {
            name: 'Web Application',
            slug: 'web-app',
            description: 'Frontend web application built with React',
            repositoryUrl: 'https://github.com/demo/web-app.git',
            defaultBranch: 'main',
            organizationId: demoOrg.id,
            members: {
                create: [
                    {
                        userId: admin.id,
                        role: 'OWNER',
                    },
                    {
                        userId: demoUser.id,
                        role: 'DEVELOPER',
                    },
                ],
            },
            environments: {
                create: [
                    {
                        name: 'Development',
                        slug: 'development',
                        description: 'Development environment',
                        variables: {
                            NODE_ENV: 'development',
                            API_URL: 'http://localhost:3001',
                        },
                    },
                    {
                        name: 'Staging',
                        slug: 'staging',
                        description: 'Staging environment',
                        variables: {
                            NODE_ENV: 'staging',
                            API_URL: 'https://api-staging.chainops.dev',
                        },
                    },
                    {
                        name: 'Production',
                        slug: 'production',
                        description: 'Production environment',
                        variables: {
                            NODE_ENV: 'production',
                            API_URL: 'https://api.chainops.dev',
                        },
                    },
                ],
            },
        },
    });
    console.log('✅ Created web project:', webProject.name);
    const apiProject = await prisma.project.upsert({
        where: {
            organizationId_slug: {
                organizationId: demoOrg.id,
                slug: 'api-server'
            }
        },
        update: {},
        create: {
            name: 'API Server',
            slug: 'api-server',
            description: 'Backend API server built with Node.js',
            repositoryUrl: 'https://github.com/demo/api-server.git',
            defaultBranch: 'main',
            organizationId: demoOrg.id,
            members: {
                create: [
                    {
                        userId: admin.id,
                        role: 'OWNER',
                    },
                    {
                        userId: demoUser.id,
                        role: 'DEVELOPER',
                    },
                ],
            },
            environments: {
                create: [
                    {
                        name: 'Development',
                        slug: 'development',
                        description: 'Development environment',
                        variables: {
                            NODE_ENV: 'development',
                            DATABASE_URL: 'postgresql://localhost:5432/chainops_dev',
                        },
                    },
                    {
                        name: 'Staging',
                        slug: 'staging',
                        description: 'Staging environment',
                        variables: {
                            NODE_ENV: 'staging',
                            DATABASE_URL: 'postgresql://staging-db:5432/chainops',
                        },
                    },
                    {
                        name: 'Production',
                        slug: 'production',
                        description: 'Production environment',
                        variables: {
                            NODE_ENV: 'production',
                            DATABASE_URL: 'postgresql://prod-db:5432/chainops',
                        },
                    },
                ],
            },
        },
    });
    console.log('✅ Created API project:', apiProject.name);
    const buildPipeline = await prisma.pipeline.create({
        data: {
            name: 'Build and Test',
            slug: 'build-test',
            description: 'Build application and run tests',
            projectId: webProject.id,
            userId: admin.id,
            config: {
                steps: [
                    {
                        name: 'Checkout',
                        uses: 'actions/checkout@v3',
                    },
                    {
                        name: 'Setup Node.js',
                        uses: 'actions/setup-node@v3',
                        with: {
                            'node-version': '18',
                        },
                    },
                    {
                        name: 'Install dependencies',
                        run: 'npm ci',
                    },
                    {
                        name: 'Run tests',
                        run: 'npm test',
                    },
                    {
                        name: 'Build application',
                        run: 'npm run build',
                    },
                ],
            },
            triggers: {
                create: [
                    {
                        type: 'WEBHOOK',
                        config: {
                            events: ['push', 'pull_request'],
                            branches: ['main', 'develop'],
                        },
                    },
                ],
            },
        },
    });
    console.log('✅ Created build pipeline:', buildPipeline.name);
    const deployPipeline = await prisma.pipeline.create({
        data: {
            name: 'Deploy to Production',
            slug: 'deploy-prod',
            description: 'Deploy application to production environment',
            projectId: webProject.id,
            userId: admin.id,
            config: {
                steps: [
                    {
                        name: 'Checkout',
                        uses: 'actions/checkout@v3',
                    },
                    {
                        name: 'Build Docker image',
                        run: 'docker build -t web-app:${{ github.sha }} .',
                    },
                    {
                        name: 'Push to registry',
                        run: 'docker push web-app:${{ github.sha }}',
                    },
                    {
                        name: 'Deploy to Kubernetes',
                        run: 'kubectl set image deployment/web-app web-app=web-app:${{ github.sha }}',
                    },
                ],
            },
            triggers: {
                create: [
                    {
                        type: 'WEBHOOK',
                        config: {
                            events: ['push'],
                            branches: ['main'],
                        },
                    },
                ],
            },
        },
    });
    console.log('✅ Created deploy pipeline:', deployPipeline.name);
    await prisma.secret.createMany({
        data: [
            {
                key: 'DATABASE_PASSWORD',
                value: 'encrypted-password-value',
                description: 'Database password for production',
                projectId: webProject.id,
            },
            {
                key: 'API_KEY',
                value: 'encrypted-api-key-value',
                description: 'Third-party API key',
                projectId: webProject.id,
            },
            {
                key: 'DOCKER_REGISTRY_TOKEN',
                value: 'encrypted-registry-token',
                description: 'Docker registry access token',
                projectId: apiProject.id,
            },
        ],
    });
    console.log('✅ Created demo secrets');
    const pipelineRun1 = await prisma.pipelineRun.create({
        data: {
            number: 1,
            pipelineId: buildPipeline.id,
            status: 'SUCCESS',
            trigger: {
                type: 'WEBHOOK',
                branch: 'main',
                commit: 'abc123',
            },
            startedAt: new Date(Date.now() - 10 * 60 * 1000),
            finishedAt: new Date(Date.now() - 5 * 60 * 1000),
            jobs: {
                create: [
                    {
                        name: 'build',
                        userId: admin.id,
                        status: 'SUCCESS',
                        config: { step: 'build' },
                        startedAt: new Date(Date.now() - 10 * 60 * 1000),
                        finishedAt: new Date(Date.now() - 8 * 60 * 1000),
                        logs: 'Building application...\nBuild completed successfully!',
                    },
                    {
                        name: 'test',
                        userId: admin.id,
                        status: 'SUCCESS',
                        config: { step: 'test' },
                        startedAt: new Date(Date.now() - 8 * 60 * 1000),
                        finishedAt: new Date(Date.now() - 5 * 60 * 1000),
                        logs: 'Running tests...\nAll tests passed!',
                    },
                ],
            },
        },
    });
    console.log('✅ Created demo pipeline run:', pipelineRun1.id);
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Demo Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Demo User: <EMAIL> / demo123');
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map