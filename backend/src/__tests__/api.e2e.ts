import request from 'supertest';
import { MetricsManager } from '../observability/metrics';
import { LogManager } from '../observability/logs';

describe('API E2E Tests', () => {
    const baseUrl = 'http://localhost:8081/api';
    const metricsUrl = 'http://localhost:9090';

    it('should handle a complete pipeline workflow', async () => {
        // 1. Create a pipeline
        const pipelineResponse = await request(baseUrl)
            .post('/pipelines')
            .send({
                name: 'Test Pipeline',
                jobs: {
                    build: {
                        name: 'Build',
                        script: ['make build'],
                        needs: [],
                        tags: ['docker']
                    }
                }
            })
            .expect(201);

        const pipelineId = pipelineResponse.body.id;
        expect(pipelineId).toBeDefined();

        // 2. Get pipeline details
        const getPipelineResponse = await request(baseUrl)
            .get(`/pipelines/${pipelineId}`)
            .expect(200);

        expect(getPipelineResponse.body.name).toBe('Test Pipeline');

        // 3. List jobs
        const jobsResponse = await request(baseUrl)
            .get('/jobs')
            .expect(200);

        expect(Array.isArray(jobsResponse.body)).toBe(true);

        // 4. Get job logs
        const jobId = 'test-job-1';
        await LogManager.writeJobLog(jobId, 'Test log message');

        const logsResponse = await request(baseUrl)
            .get(`/jobs/${jobId}/logs`)
            .expect(200);

        expect(logsResponse.text).toContain('Test log message');

        // 5. Check metrics
        const metricsResponse = await request(metricsUrl)
            .get('/metrics')
            .expect(200);

        expect(metricsResponse.text).toContain('chainops_job_total');
        expect(metricsResponse.text).toContain('chainops_pipeline_total');
    });

    it('should handle secrets management', async () => {
        const response = await request(baseUrl)
            .get('/secrets/project1/build')
            .expect(200);

        expect(response.body).toHaveProperty('project', 'project1');
        expect(response.body).toHaveProperty('job', 'build');
        expect(response.body).toHaveProperty('secrets');
        expect(response.body.secrets).toHaveProperty('API_KEY');
    });
}); 