import { beforeAll, afterAll } from '@jest/globals';
import { mkdir } from 'fs/promises';
import { join } from 'path';

// Create test directories
beforeAll(async () => {
    const testDirs = [
        'logs',
        'logs/jobs',
        'artifacts',
        'coverage'
    ];

    for (const dir of testDirs) {
        await mkdir(join(process.cwd(), dir), { recursive: true });
    }
});

// Cleanup after all tests
afterAll(async () => {
    // Add cleanup logic here if needed
}); 