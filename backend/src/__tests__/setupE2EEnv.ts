// This file will be renamed to setupE2EEnv.ts and only used as a setup file, not as a test file.
import { beforeAll, afterAll } from '@jest/globals';
import { APIServer } from '../api/server';
import { MetricsManager } from '../observability/metrics';
import { LogManager } from '../observability/logs';
import type { Server } from 'http';

let metricsHttpServer: Server | undefined;

// Start API server and metrics server before tests
beforeAll(async () => {
    // Start API server
    const apiServer = new APIServer();
    await apiServer.start();

    // Start metrics server
    const metricsServer = MetricsManager.getMetricsServer();
    metricsHttpServer = metricsServer.listen(9090, () => {
        console.log('Metrics server started on port 9090');
    });

    // Wait for servers to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
});

// Cleanup after all tests
afterAll(async () => {
    // Close metrics server
    if (metricsHttpServer) {
        await new Promise<void>((resolve) => {
            metricsHttpServer!.close(() => {
                console.log('Metrics server closed');
                resolve();
            });
        });
    }
}); 