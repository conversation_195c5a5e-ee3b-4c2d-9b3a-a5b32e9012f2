import { Request, Response } from 'express';
import { JobController } from '../job.controller';

describe('JobController', () => {
  let controller: JobController;
  let req: Partial<Request>;
  let res: Partial<Response>;

  beforeEach(() => {
    controller = new JobController();
    req = {};
    res = {
      json: jest.fn(),
      type: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
  });

  describe('listJobs', () => {
    it('should return a list of jobs', async () => {
      await controller.listJobs(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith([
        { id: 1, name: 'Test Job', status: 'success' }
      ]);
    });
  });

  describe('getJob', () => {
    it('should return a specific job', async () => {
      req.params = { id: '123' };

      await controller.getJob(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Test Job',
        status: 'success'
      });
    });
  });

  describe('createJob', () => {
    it('should create a new job', async () => {
      req.body = {
        name: 'New Job',
        script: ['echo "Hello World"']
      };

      await controller.createJob(req as Request, res as Response);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        id: 1,
        name: 'New Job',
        script: ['echo "Hello World"'],
        status: 'pending'
      });
    });
  });

  describe('updateJob', () => {
    it('should update an existing job', async () => {
      req.params = { id: '123' };
      req.body = {
        name: 'Updated Job',
        status: 'running'
      };

      await controller.updateJob(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Updated Job',
        status: 'success'
      });
    });
  });

  describe('deleteJob', () => {
    it('should delete a job', async () => {
      req.params = { id: '123' };

      await controller.deleteJob(req as Request, res as Response);

      expect(res.status).toHaveBeenCalledWith(204);
      expect(res.send).toHaveBeenCalled();
    });
  });

  describe('getJobLogs', () => {
    it('should return job logs', async () => {
      req.params = { id: '123' };

      await controller.getJobLogs(req as Request, res as Response);

      expect(res.type).toHaveBeenCalledWith('text/plain');
      expect(res.send).toHaveBeenCalledWith(
        '[2024-01-01T00:00:00Z] Job 123 started\n[2024-01-01T00:01:00Z] Job 123 completed successfully'
      );
    });
  });

  describe('getJobArtifacts', () => {
    it('should return job artifacts', async () => {
      req.params = { id: '123' };

      await controller.getJobArtifacts(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith([
        { id: 1, name: 'build.zip', size: 1024, url: '/artifacts/123/build.zip' },
        { id: 2, name: 'test-results.xml', size: 512, url: '/artifacts/123/test-results.xml' }
      ]);
    });
  });

  describe('retryJob', () => {
    it('should retry a job', async () => {
      req.params = { id: '123' };

      await controller.retryJob(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Test Job',
        status: 'pending',
        retried: true
      });
    });
  });

  describe('cancelJob', () => {
    it('should cancel a job', async () => {
      req.params = { id: '123' };

      await controller.cancelJob(req as Request, res as Response);

      expect(res.json).toHaveBeenCalledWith({
        id: 123,
        name: 'Test Job',
        status: 'cancelled'
      });
    });
  });
});
