import { Request, Response } from 'express';

export class PipelineController {
    public listPipelines = async (req: Request, res: Response) => {
        // Mock data for testing
        res.json([
            { id: 1, name: 'Test Pipeline', status: 'success' }
        ]);
    };

    public getPipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.json({ id: parseInt(id), name: 'Test Pipeline', status: 'success' });
    };

    public createPipeline = async (req: Request, res: Response) => {
        const pipeline = req.body;
        res.status(201).json({ id: 1, ...pipeline, status: 'pending' });
    };

    public updatePipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        const updates = req.body;
        res.json({ id: parseInt(id), ...updates, status: 'success' });
    };

    public deletePipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.status(204).send();
    };

    public runPipeline = async (req: Request, res: Response) => {
        const { id } = req.params;
        const { branch, variables } = req.body;
        res.status(201).json({
            id: Math.floor(Math.random() * 1000),
            pipelineId: parseInt(id),
            status: 'pending',
            branch: branch || 'main',
            variables: variables || {}
        });
    };

    public getPipelineRuns = async (req: Request, res: Response) => {
        const { id } = req.params;
        res.json([
            {
                id: 1,
                pipelineId: parseInt(id),
                status: 'success',
                branch: 'main',
                createdAt: new Date().toISOString()
            }
        ]);
    };

    public getPipelineRun = async (req: Request, res: Response) => {
        const { id, runId } = req.params;
        res.json({
            id: parseInt(runId),
            pipelineId: parseInt(id),
            status: 'success',
            branch: 'main',
            jobs: [
                { id: 1, name: 'build', status: 'success' },
                { id: 2, name: 'test', status: 'success' }
            ]
        });
    };
}