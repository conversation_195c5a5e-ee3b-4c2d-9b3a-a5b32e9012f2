import { Request, Response } from 'express';

export class SecretsController {
    public getSecrets = async (req: Request, res: Response) => {
        const { project, job } = req.params;
        res.json({
            project,
            job,
            secrets: {
                API_KEY: 'test-api-key'
            }
        });
    };

    public setSecrets = async (req: Request, res: Response) => {
        const { project, job } = req.params;
        const secrets = req.body;
        res.status(201).json({
            project,
            job,
            secrets
        });
    };

    public deleteSecrets = async (req: Request, res: Response) => {
        const { project, job } = req.params;
        res.status(204).send();
    };
} 