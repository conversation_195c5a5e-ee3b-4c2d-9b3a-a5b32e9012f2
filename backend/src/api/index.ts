import { Router } from 'express';
import { PipelineController } from './controllers/pipeline.controller';
import { JobController } from './controllers/job.controller';
import { SecretsController } from './controllers/secrets.controller';

export const router = Router();

// Initialize controllers
const pipelineController = new PipelineController();
const jobController = new JobController();
const secretsController = new SecretsController();

// Pipeline routes
router.get('/pipelines', pipelineController.listPipelines);
router.get('/pipelines/:id', pipelineController.getPipeline);
router.post('/pipelines', pipelineController.createPipeline);
router.put('/pipelines/:id', pipelineController.updatePipeline);
router.delete('/pipelines/:id', pipelineController.deletePipeline);

// Job routes
router.get('/jobs', jobController.listJobs);
router.get('/jobs/:id', jobController.getJob);
router.post('/jobs', jobController.createJob);
router.put('/jobs/:id', jobController.updateJob);
router.delete('/jobs/:id', jobController.deleteJob);

// Secrets routes
router.get('/secrets/:project/:job', secretsController.getSecrets);
router.post('/secrets/:project/:job', secretsController.setSecrets);
router.delete('/secrets/:project/:job', secretsController.deleteSecrets); 