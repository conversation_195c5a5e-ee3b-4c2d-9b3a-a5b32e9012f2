import express, { Router } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { rateLimit } from 'express-rate-limit';
import { router } from './index';
import { MetricsManager } from '../observability/metrics';
import { LogManager } from '../observability/logs';
import { join } from 'path';

export class APIServer {
    private app: express.Application;
    private server: any;
    public router: express.Router;

    constructor() {
        this.app = express();
        this.router = router;
        this.setupMiddleware();
        this.setupRoutes();
    }

    private setupMiddleware() {
        // Security middleware
        this.app.use(helmet());
        this.app.use(cors());

        // Performance middleware
        this.app.use(compression());
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));

        // Logging
        this.app.use(morgan('combined'));

        // Rate limiting
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100 // limit each IP to 100 requests per windowMs
        });
        this.app.use(limiter);
    }

    private setupRoutes() {
        // Pipeline routes
        this.router.get('/pipelines', this.listPipelines);
        this.router.get('/pipelines/:id', this.getPipeline);
        this.router.post('/pipelines', this.createPipeline);

        // Job routes
        this.router.get('/jobs', this.listJobs);
        this.router.get('/jobs/:id', this.getJob);
        this.router.get('/jobs/:id/logs', this.getJobLogs);
        this.router.get('/jobs/:id/artifacts', this.getJobArtifacts);

        // Secret routes
        this.router.get('/secrets/:project/:job', this.getSecrets);

        // Mount API routes
        this.app.use('/api', this.router);
    }

    // Route handlers
    private listPipelines = async (req: express.Request, res: express.Response) => {
        // For demo: return a static list
        const pipelines = [
            { id: 'pipeline-1', name: 'Build and Test', status: 'success' },
            { id: 'pipeline-2', name: 'Deploy', status: 'running' }
        ];
        res.json(pipelines);
    };

    private getPipeline = async (req: express.Request, res: express.Response) => {
        const { id } = req.params;
        // TODO: Implement pipeline retrieval
        res.json({ id, name: 'Sample Pipeline', status: 'running' });
    };

    private createPipeline = async (req: express.Request, res: express.Response) => {
        const pipeline = req.body;
        // TODO: Implement pipeline creation
        res.status(201).json({ id: 'new-pipeline', ...pipeline });
    };

    private listJobs = async (req: express.Request, res: express.Response) => {
        const jobs = [
            { id: 'job-1', name: 'build', status: 'success' },
            { id: 'job-2', name: 'test', status: 'running' }
        ];
        res.json(jobs);
    };

    private getJob = async (req: express.Request, res: express.Response) => {
        const { id } = req.params;
        // TODO: Implement job retrieval
        res.json({ id, name: 'Sample Job', status: 'running' });
    };

    private getJobLogs = async (req: express.Request, res: express.Response) => {
        const { id } = req.params;
        // Use absolute path for log file
        const logPath = join(process.cwd(), 'logs', 'jobs', `${id}.log`);
        res.sendFile(logPath);
    };

    private getJobArtifacts = async (req: express.Request, res: express.Response) => {
        const { id } = req.params;
        // TODO: Implement artifact listing
        const artifacts = [`artifacts/${id}_output.txt`];
        res.json(artifacts);
    };

    private getSecrets = async (req: express.Request, res: express.Response) => {
        const { project, job } = req.params;
        // For demo: return static secret
        const secrets = {
            API_KEY: 'demo-key'
        };
        res.json({
            project,
            job,
            secrets
        });
    };

    async start(port: number = 8081): Promise<void> {
        return new Promise((resolve) => {
            this.server = this.app.listen(port, () => {
                console.log(`API server started on port ${port}`);
                resolve();
            });
        });
    }

    async stop(): Promise<void> {
        if (this.server) {
            return new Promise((resolve) => {
                this.server.close(() => {
                    console.log('API server stopped');
                    resolve();
                });
            });
        }
    }
} 