import { Client } from 'minio';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';

export class ArtifactStore {
    private client: Client;
    private bucketName: string;

    constructor(
        endpoint: string,
        accessKey: string,
        secretKey: string,
        bucket: string,
        useSSL: boolean = false
    ) {
        this.client = new Client({
            endPoint: endpoint,
            port: useSSL ? 443 : 80,
            useSSL,
            accessKey,
            secretKey
        });
        this.bucketName = bucket;
    }

    /**
     * Initialize the artifact store
     * Creates the bucket if it doesn't exist
     */
    async initialize(): Promise<void> {
        try {
            const exists = await this.client.bucketExists(this.bucketName);
            if (!exists) {
                await this.client.makeBucket(this.bucketName);
            }
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to initialize artifact store: ${errorMessage}`);
        }
    }

    /**
     * Upload an artifact to the store
     * @param objectName Name of the object in the store
     * @param filePath Path to the file to upload
     */
    async uploadArtifact(objectName: string, filePath: string): Promise<void> {
        try {
            await this.client.fPutObject(
                this.bucketName,
                objectName,
                filePath,
                {}
            );
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to upload artifact ${objectName}: ${errorMessage}`);
        }
    }

    /**
     * Download an artifact from the store
     * @param objectName Name of the object in the store
     * @param destPath Path where to save the downloaded file
     */
    async downloadArtifact(objectName: string, destPath: string): Promise<void> {
        try {
            const stream = await this.client.getObject(this.bucketName, objectName);
            const writeStream = createWriteStream(destPath);
            await pipeline(stream, writeStream);
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to download artifact ${objectName}: ${errorMessage}`);
        }
    }

    /**
     * List artifacts in a directory
     * @param prefix Directory prefix to list
     * @returns Array of object names
     */
    async listArtifacts(prefix: string): Promise<string[]> {
        try {
            const objects: string[] = [];
            const stream = this.client.listObjects(this.bucketName, prefix, true);
            
            for await (const obj of stream) {
                objects.push(obj.name);
            }
            
            return objects;
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to list artifacts: ${errorMessage}`);
        }
    }

    /**
     * Delete an artifact from the store
     * @param objectName Name of the object to delete
     */
    async deleteArtifact(objectName: string): Promise<void> {
        try {
            await this.client.removeObject(this.bucketName, objectName);
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to delete artifact ${objectName}: ${errorMessage}`);
        }
    }
} 