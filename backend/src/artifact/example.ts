import { ArtifactStore } from './artifact';
import { writeFile } from 'fs/promises';

async function main() {
    // MinIO configuration
    const endpoint = 'localhost:9000';
    const accessKey = 'minioadmin';
    const secretKey = 'minioadmin';
    const bucket = 'chainops-artifacts';
    const useSSL = false;

    try {
        // Initialize artifact store
        const store = new ArtifactStore(endpoint, accessKey, secretKey, bucket, useSSL);
        await store.initialize();
        console.log('Artifact store initialized!');

        // Create a test file
        const testContent = 'Hello, this is a test artifact!';
        const testFile = 'test-artifact.txt';
        await writeFile(testFile, testContent);
        console.log('Test file created');

        // Upload the artifact
        const objectName = 'job-1/output.txt';
        await store.uploadArtifact(objectName, testFile);
        console.log('Artifact uploaded!');

        // List artifacts
        const artifacts = await store.listArtifacts('job-1/');
        console.log('Available artifacts:', artifacts);

        // Download the artifact
        const downloadedFile = 'downloaded-artifact.txt';
        await store.downloadArtifact(objectName, downloadedFile);
        console.log('Artifact downloaded!');

        // Clean up
        await store.deleteArtifact(objectName);
        console.log('Artifact deleted!');
    } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : 'Unknown error');
        process.exit(1);
    }
}

// Run the example
main().catch(console.error); 