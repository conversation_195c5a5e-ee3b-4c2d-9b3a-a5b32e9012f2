import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { authMiddleware, requireRole, requireAdmin, optionalAuth, AuthenticatedRequest } from '../auth';
import { CustomError } from '../errorHandler';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    user: {
      findUnique: jest.fn(),
    },
  })),
}));

const mockJwt = jwt as jest.Mocked<typeof jwt>;

describe('Auth Middleware', () => {
  let req: Partial<AuthenticatedRequest>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    req = {
      headers: {},
    };
    res = {};
    next = jest.fn();
    jest.clearAllMocks();
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('authMiddleware', () => {
    it('should throw error when no authorization header is provided', async () => {
      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when authorization header does not start with Bearer', async () => {
      req.headers!.authorization = 'Basic token123';

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when no token is provided', async () => {
      req.headers!.authorization = 'Bearer ';

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token required',
          statusCode: 401,
        })
      );
    });

    it('should throw error when token is invalid', async () => {
      req.headers!.authorization = 'Bearer invalid-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid token',
          statusCode: 401,
        })
      );
    });

    it('should throw error when token is expired', async () => {
      req.headers!.authorization = 'Bearer expired-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      await authMiddleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Token expired',
          statusCode: 401,
        })
      );
    });
  });

  describe('requireRole', () => {
    it('should throw error when user is not authenticated', () => {
      const middleware = requireRole(['ADMIN']);

      expect(() => {
        middleware(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Authentication required');
    });

    it('should throw error when user does not have required role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'USER' };
      const middleware = requireRole(['ADMIN']);

      expect(() => {
        middleware(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Insufficient permissions');
    });

    it('should call next when user has required role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'ADMIN' };
      const middleware = requireRole(['ADMIN']);

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should allow multiple roles', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'MODERATOR' };
      const middleware = requireRole(['ADMIN', 'MODERATOR']);

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('requireAdmin', () => {
    it('should require ADMIN role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'USER' };

      expect(() => {
        requireAdmin(req as AuthenticatedRequest, res as Response, next);
      }).toThrow('Insufficient permissions');
    });

    it('should allow ADMIN role', () => {
      req.user = { id: '1', email: '<EMAIL>', username: 'test', role: 'ADMIN' };

      requireAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('optionalAuth', () => {
    it('should continue without error when no auth header is provided', async () => {
      await optionalAuth(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.user).toBeUndefined();
    });

    it('should continue without error when invalid token is provided', async () => {
      req.headers!.authorization = 'Bearer invalid-token';
      mockJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.user).toBeUndefined();
    });
  });

  describe('comprehensive error scenarios', () => {
    it('should handle malformed authorization headers', async () => {
      const testCases = [
        'Bearer',
        'Bearer ',
        'Bearer  ',
        'NotBearer token123',
        'bearer token123', // lowercase
        'BEARER token123', // uppercase
        'Bearer token with spaces',
        'Bearer token\nwith\nnewlines',
      ];

      for (const authHeader of testCases) {
        req.headers = { authorization: authHeader };
        await optionalAuth(req as AuthenticatedRequest, res as Response, next);
        expect(next).toHaveBeenCalled();
        jest.clearAllMocks();
      }
    });

    it('should handle various token formats', async () => {
      const testTokens = [
        'invalid.token.format',
        'too.short',
        'way.too.long.token.with.many.parts.that.should.not.be.valid',
        '',
        '   ',
        'null',
        'undefined',
      ];

      for (const token of testTokens) {
        req.headers = { authorization: `Bearer ${token}` };
        await optionalAuth(req as AuthenticatedRequest, res as Response, next);
        expect(next).toHaveBeenCalled();
        jest.clearAllMocks();
      }
    });

    it('should handle database connection issues', async () => {
      const validToken = jwt.sign({ userId: 'user123' }, 'test-secret');
      req.headers = { authorization: `Bearer ${validToken}` };

      // Mock database connection failure
      (DatabaseService.getClient as jest.Mock).mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);
      expect(next).toHaveBeenCalled();
    });

    it('should handle null/undefined user data from database', async () => {
      const validToken = jwt.sign({ userId: 'user123' }, 'test-secret');
      req.headers = { authorization: `Bearer ${validToken}` };

      // Mock null user response
      (DatabaseService.getClient as jest.Mock).mockReturnValue({
        user: {
          findUnique: jest.fn().mockResolvedValue(null)
        }
      });

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);
      expect(next).toHaveBeenCalled();
      expect(req.user).toBeUndefined();
    });

    it('should handle concurrent authentication requests', async () => {
      const validToken = jwt.sign({ userId: 'user123' }, 'test-secret');

      const requests = Array(5).fill(null).map(() => ({
        headers: { authorization: `Bearer ${validToken}` }
      }));

      const responses = Array(5).fill(null).map(() => ({
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      }));

      const nextFunctions = Array(5).fill(null).map(() => jest.fn());

      // Mock successful user lookup
      (DatabaseService.getClient as jest.Mock).mockReturnValue({
        user: {
          findUnique: jest.fn().mockResolvedValue({
            id: 'user123',
            email: '<EMAIL>'
          })
        }
      });

      const promises = requests.map((req, index) =>
        optionalAuth(req as AuthenticatedRequest, responses[index] as Response, nextFunctions[index])
      );

      await Promise.all(promises);

      nextFunctions.forEach(nextFn => {
        expect(nextFn).toHaveBeenCalled();
      });
    });

    it('should handle edge cases in JWT verification', async () => {
      const edgeCaseTokens = [
        jwt.sign({ userId: '' }, 'test-secret'), // Empty userId
        jwt.sign({ userId: null }, 'test-secret'), // Null userId
        jwt.sign({ userId: undefined }, 'test-secret'), // Undefined userId
        jwt.sign({ notUserId: 'user123' }, 'test-secret'), // Wrong field name
        jwt.sign({ userId: 123 }, 'test-secret'), // Numeric userId
        jwt.sign({ userId: {} }, 'test-secret'), // Object userId
        jwt.sign({ userId: [] }, 'test-secret'), // Array userId
      ];

      for (const token of edgeCaseTokens) {
        req.headers = { authorization: `Bearer ${token}` };
        await optionalAuth(req as AuthenticatedRequest, res as Response, next);
        expect(next).toHaveBeenCalled();
        jest.clearAllMocks();
      }
    });

    it('should handle expired tokens gracefully', async () => {
      const expiredToken = jwt.sign(
        {
          userId: 'user123',
          exp: Math.floor(Date.now() / 1000) - 3600 // Expired 1 hour ago
        },
        'test-secret'
      );
      req.headers = { authorization: `Bearer ${expiredToken}` };

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);
      expect(next).toHaveBeenCalled();
      expect(req.user).toBeUndefined();
    });

    it('should handle tokens with invalid signatures', async () => {
      const invalidSignatureToken = jwt.sign({ userId: 'user123' }, 'wrong-secret');
      req.headers = { authorization: `Bearer ${invalidSignatureToken}` };

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);
      expect(next).toHaveBeenCalled();
      expect(req.user).toBeUndefined();
    });

    it('should handle missing JWT secret', async () => {
      const originalSecret = process.env.JWT_SECRET;
      delete process.env.JWT_SECRET;

      const token = jwt.sign({ userId: 'user123' }, 'test-secret');
      req.headers = { authorization: `Bearer ${token}` };

      await optionalAuth(req as AuthenticatedRequest, res as Response, next);
      expect(next).toHaveBeenCalled();

      // Restore original secret
      if (originalSecret) {
        process.env.JWT_SECRET = originalSecret;
      }
    });
  });
});
