import { LogManager } from '../logs';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { mkdir } from 'fs/promises';

describe('LogManager', () => {
    const testJobId = 'test-job-1';
    const testMessage = 'Test log message';

    beforeEach(async () => {
        // Create necessary directories
        await mkdir(join(process.cwd(), 'logs', 'jobs'), { recursive: true });
        LogManager.closeJobLog(testJobId);
    });

    afterEach(() => {
        LogManager.closeJobLog(testJobId);
    });

    it('should write job logs to a file', async () => {
        LogManager.writeJobLog(testJobId, testMessage);

        const logPath = join('logs', 'jobs', `${testJobId}.log`);
        const logContent = await readFile(logPath, 'utf-8');

        expect(logContent).toContain(testMessage);
        expect(logContent).toMatch(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/); // ISO timestamp
    });

    it('should append multiple log messages', async () => {
        const messages = ['First message', 'Second message', 'Third message'];
        
        for (const message of messages) {
            LogManager.writeJobLog(testJobId, message);
        }

        const logPath = join('logs', 'jobs', `${testJobId}.log`);
        const logContent = await readFile(logPath, 'utf-8');

        for (const message of messages) {
            expect(logContent).toContain(message);
        }
    });

    it('should get a logger instance', () => {
        const logger = LogManager.getLogger(testJobId);
        expect(logger).toBeDefined();
        expect(logger.info).toBeDefined();
        expect(logger.error).toBeDefined();
    });
}); 