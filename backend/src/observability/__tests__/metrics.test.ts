import { MetricsManager } from '../metrics';
import request from 'supertest';
import express from 'express';

describe('MetricsManager', () => {
    let app: express.Application;

    beforeAll(() => {
        app = express();
        app.get('/metrics', MetricsManager.getMetricsMiddleware());
    });

    it('should record job duration', () => {
        const jobName = 'test-job';
        const status = 'success';
        const duration = 5.5;

        MetricsManager.recordJobDuration(jobName, status, duration);

        // Note: We can't directly test the metric value as it's internal to prom-client
        // Instead, we verify the method doesn't throw
        expect(() => {
            MetricsManager.recordJobDuration(jobName, status, duration);
        }).not.toThrow();
    });

    it('should increment job count', () => {
        const jobName = 'test-job';
        const status = 'success';

        expect(() => {
            MetricsManager.incrementJobCount(jobName, status);
        }).not.toThrow();
    });

    it('should increment pipeline count', () => {
        const pipelineName = 'test-pipeline';
        const status = 'success';

        expect(() => {
            MetricsManager.incrementPipelineCount(pipelineName, status);
        }).not.toThrow();
    });

    it('should record artifact size', () => {
        const jobName = 'test-job';
        const type = 'log';
        const size = 1024;

        expect(() => {
            MetricsManager.recordArtifactSize(jobName, type, size);
        }).not.toThrow();
    });

    it('should expose metrics endpoint', async () => {
        const response = await request(app)
            .get('/metrics')
            .expect('Content-Type', /text\/plain/)
            .expect(200);

        // Verify metrics content
        expect(response.text).toContain('chainops_job_duration_seconds');
        expect(response.text).toContain('chainops_job_total');
        expect(response.text).toContain('chainops_pipeline_total');
        expect(response.text).toContain('chainops_artifact_size_bytes');
    });

    it('should get metrics middleware', () => {
        const middleware = MetricsManager.getMetricsMiddleware();
        expect(middleware).toBeDefined();
        expect(typeof middleware).toBe('function');
    });

    it('should handle multiple job statuses', () => {
        const jobName = 'multi-status-job';

        expect(() => {
            MetricsManager.incrementJobCount(jobName, 'success');
            MetricsManager.incrementJobCount(jobName, 'failure');
            MetricsManager.incrementJobCount(jobName, 'cancelled');
        }).not.toThrow();
    });

    it('should handle multiple pipeline statuses', () => {
        const pipelineName = 'multi-status-pipeline';

        expect(() => {
            MetricsManager.incrementPipelineCount(pipelineName, 'success');
            MetricsManager.incrementPipelineCount(pipelineName, 'failure');
            MetricsManager.incrementPipelineCount(pipelineName, 'running');
        }).not.toThrow();
    });

    it('should handle different artifact types', () => {
        const jobName = 'artifact-job';

        expect(() => {
            MetricsManager.recordArtifactSize(jobName, 'log', 1024);
            MetricsManager.recordArtifactSize(jobName, 'binary', 2048);
            MetricsManager.recordArtifactSize(jobName, 'report', 512);
        }).not.toThrow();
    });

    describe('metrics server', () => {
        it('should get metrics server instance', () => {
            const server = MetricsManager.getMetricsServer();
            expect(server).toBeDefined();
            expect(typeof server.get).toBe('function');
        });

        it('should provide metrics middleware', () => {
            const middleware = MetricsManager.getMetricsMiddleware();
            expect(middleware).toBeDefined();
            expect(typeof middleware).toBe('function');
        });

        it('should handle metrics endpoint request', async () => {
            const middleware = MetricsManager.getMetricsMiddleware();
            const mockReq = {} as any;
            const mockRes = {
                set: jest.fn(),
                end: jest.fn(),
            } as any;

            await middleware(mockReq, mockRes, jest.fn());

            expect(mockRes.set).toHaveBeenCalledWith('Content-Type', expect.any(String));
            expect(mockRes.end).toHaveBeenCalled();
        });
    });

    describe('instance methods', () => {
        let instance: any;

        beforeEach(() => {
            instance = MetricsManager.getInstance();
        });

        it('should record job duration using instance method', () => {
            expect(() => {
                instance.recordJobDuration('instance-job', 'success', 45);
            }).not.toThrow();
        });

        it('should increment job count using instance method', () => {
            expect(() => {
                instance.incrementJobCount('instance-job', 'success');
            }).not.toThrow();
        });

        it('should increment pipeline count using instance method', () => {
            expect(() => {
                instance.incrementPipelineCount('instance-pipeline', 'success');
            }).not.toThrow();
        });

        it('should record artifact size using instance method', () => {
            expect(() => {
                instance.recordArtifactSize('instance-job', 'log', 2048);
            }).not.toThrow();
        });

        it('should have metrics property', () => {
            expect(instance.metrics).toBeDefined();
            expect(instance.metrics.jobDuration).toBeDefined();
            expect(instance.metrics.jobCount).toBeDefined();
            expect(instance.metrics.pipelineCount).toBeDefined();
            expect(instance.metrics.artifactSize).toBeDefined();
        });

        it('should have metricsServer property', () => {
            expect(instance.metricsServer).toBeDefined();
            expect(typeof instance.metricsServer.get).toBe('function');
        });
    });

    describe('metric types and configurations', () => {
        let instance: any;

        beforeEach(() => {
            instance = MetricsManager.getInstance();
        });

        it('should have properly configured histogram for job duration', () => {
            const histogram = instance.metrics.jobDuration;
            expect(histogram.name).toBe('job_duration_seconds');
            expect(histogram.help).toBe('Duration of jobs in seconds');
        });

        it('should have properly configured counter for job count', () => {
            const counter = instance.metrics.jobCount;
            expect(counter.name).toBe('job_count_total');
            expect(counter.help).toBe('Total number of jobs');
        });

        it('should have properly configured counter for pipeline count', () => {
            const counter = instance.metrics.pipelineCount;
            expect(counter.name).toBe('pipeline_count_total');
            expect(counter.help).toBe('Total number of pipelines');
        });

        it('should have properly configured gauge for artifact size', () => {
            const gauge = instance.metrics.artifactSize;
            expect(gauge.name).toBe('artifact_size_bytes');
            expect(gauge.help).toBe('Size of artifacts in bytes');
        });
    });

    describe('comprehensive metric recording', () => {
        it('should record multiple job durations', () => {
            const jobs = [
                { name: 'build', status: 'success', duration: 30 },
                { name: 'test', status: 'success', duration: 45 },
                { name: 'deploy', status: 'failed', duration: 15 },
            ];

            jobs.forEach(job => {
                expect(() => {
                    MetricsManager.recordJobDuration(job.name, job.status, job.duration);
                }).not.toThrow();
            });
        });

        it('should increment multiple job counts', () => {
            const jobs = [
                { name: 'build', status: 'success' },
                { name: 'test', status: 'success' },
                { name: 'deploy', status: 'failed' },
                { name: 'build', status: 'success' }, // Duplicate to test increment
            ];

            jobs.forEach(job => {
                expect(() => {
                    MetricsManager.incrementJobCount(job.name, job.status);
                }).not.toThrow();
            });
        });

        it('should increment multiple pipeline counts', () => {
            const pipelines = [
                { name: 'ci-pipeline', status: 'success' },
                { name: 'cd-pipeline', status: 'success' },
                { name: 'test-pipeline', status: 'failed' },
            ];

            pipelines.forEach(pipeline => {
                expect(() => {
                    MetricsManager.incrementPipelineCount(pipeline.name, pipeline.status);
                }).not.toThrow();
            });
        });

        it('should record multiple artifact sizes', () => {
            const artifacts = [
                { jobName: 'build', type: 'binary', size: 1024000 },
                { jobName: 'test', type: 'log', size: 2048 },
                { jobName: 'deploy', type: 'config', size: 512 },
            ];

            artifacts.forEach(artifact => {
                expect(() => {
                    MetricsManager.recordArtifactSize(artifact.jobName, artifact.type, artifact.size);
                }).not.toThrow();
            });
        });
    });
});