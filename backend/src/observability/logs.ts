import { createWriteStream, WriteStream } from 'fs';
import { join } from 'path';
import { format } from 'winston';
import winston from 'winston';

// Configure Winston logger
const logger = winston.createLogger({
    level: 'info',
    format: format.combine(
        format.timestamp(),
        format.json()
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' })
    ]
});

// Job-specific log streams
const jobLogStreams = new Map<string, WriteStream>();

export class LogManager {
    private static loggers: Map<string, winston.Logger> = new Map();
    private static streams: Map<string, NodeJS.WritableStream> = new Map();

    /**
     * Get the logger instance for a specific job
     * @param jobId The ID of the job
     */
    public static getLogger(jobId: string): winston.Logger {
        if (!this.loggers.has(jobId)) {
            const logger = winston.createLogger({
                level: 'info',
                format: winston.format.combine(
                    winston.format.timestamp(),
                    winston.format.json()
                ),
                defaultMeta: { jobId },
                transports: [
                    new winston.transports.Console(),
                    new winston.transports.File({
                        filename: join('logs', 'jobs', `${jobId}.log`)
                    })
                ]
            });
            this.loggers.set(jobId, logger);
        }
        return this.loggers.get(jobId)!;
    }

    /**
     * Write a log message for a specific job
     * @param jobId The ID of the job
     * @param message The log message
     */
    public static writeJobLog(jobId: string, message: string): void {
        const logger = this.getLogger(jobId);
        logger.info(`Job log ${message}`);
    }

    /**
     * Close the log stream for a job
     * @param jobId The ID of the job
     */
    public static closeJobLog(jobId: string): void {
        const logger = this.loggers.get(jobId);
        if (logger) {
            logger.end();
            this.loggers.delete(jobId);
        }
    }
} 