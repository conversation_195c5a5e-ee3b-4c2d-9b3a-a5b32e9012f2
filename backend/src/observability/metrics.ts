import { Registry, Counter, Histogram } from 'prom-client';
import express from 'express';
import promClient from 'prom-client';

// Create a registry
const register = new Registry();

// Define metrics
export const jobDuration = new Histogram({
    name: 'chainops_job_duration_seconds',
    help: 'Duration of jobs in seconds',
    labelNames: ['job', 'status'],
    buckets: [1, 5, 15, 30, 60, 120, 300, 600, 1800, 3600] // 1s to 1h
});

export const jobCount = new Counter({
    name: 'chainops_job_total',
    help: 'Total number of jobs run',
    labelNames: ['job', 'status']
});

export const pipelineCount = new Counter({
    name: 'chainops_pipeline_total',
    help: 'Total number of pipelines run',
    labelNames: ['pipeline', 'status']
});

export const artifactSize = new Histogram({
    name: 'chainops_artifact_size_bytes',
    help: 'Size of artifacts in bytes',
    labelNames: ['job', 'type'],
    buckets: [1024, 10240, 102400, 1048576, 10485760] // 1KB to 10MB
});

// Register metrics
register.registerMetric(jobDuration);
register.registerMetric(jobCount);
register.registerMetric(pipelineCount);
register.registerMetric(artifactSize);

export class MetricsManager {
    private static instance: MetricsManager;
    private metricsServer: express.Application;
    private metrics: {
        jobDuration: promClient.Histogram;
        jobCount: promClient.Counter;
        pipelineCount: promClient.Counter;
        artifactSize: promClient.Gauge;
    };

    private constructor() {
        // Initialize metrics
        this.metrics = {
            jobDuration: new promClient.Histogram({
                name: 'job_duration_seconds',
                help: 'Duration of jobs in seconds',
                labelNames: ['job_name', 'status'],
                buckets: [1, 5, 15, 30, 60, 120, 300, 600]
            }),
            jobCount: new promClient.Counter({
                name: 'job_count_total',
                help: 'Total number of jobs',
                labelNames: ['job_name', 'status']
            }),
            pipelineCount: new promClient.Counter({
                name: 'pipeline_count_total',
                help: 'Total number of pipelines',
                labelNames: ['pipeline_name', 'status']
            }),
            artifactSize: new promClient.Gauge({
                name: 'artifact_size_bytes',
                help: 'Size of artifacts in bytes',
                labelNames: ['job_name', 'type']
            })
        };

        // Initialize metrics server
        this.metricsServer = express();
        this.metricsServer.get('/metrics', MetricsManager.getMetricsMiddleware());
    }

    public static getInstance(): MetricsManager {
        if (!MetricsManager.instance) {
            MetricsManager.instance = new MetricsManager();
        }
        return MetricsManager.instance;
    }

    public static getMetricsServer(): express.Application {
        return MetricsManager.getInstance().metricsServer;
    }

    public static getMetricsMiddleware(): express.RequestHandler {
        return async (req: express.Request, res: express.Response) => {
            res.set('Content-Type', promClient.register.contentType);
            res.end(await promClient.register.metrics());
        };
    }

    /**
     * Record job duration
     * @param jobName Name of the job
     * @param status Job status
     * @param durationSeconds Duration in seconds
     */
    static recordJobDuration(jobName: string, status: string, durationSeconds: number): void {
        jobDuration.labels(jobName, status).observe(durationSeconds);
    }

    /**
     * Increment job counter
     * @param jobName Name of the job
     * @param status Job status
     */
    static incrementJobCount(jobName: string, status: string): void {
        jobCount.labels(jobName, status).inc();
    }

    /**
     * Increment pipeline counter
     * @param pipelineName Name of the pipeline
     * @param status Pipeline status
     */
    static incrementPipelineCount(pipelineName: string, status: string): void {
        pipelineCount.labels(pipelineName, status).inc();
    }

    /**
     * Record artifact size
     * @param jobName Name of the job
     * @param type Type of artifact
     * @param sizeBytes Size in bytes
     */
    static recordArtifactSize(jobName: string, type: string, sizeBytes: number): void {
        artifactSize.labels(jobName, type).observe(sizeBytes);
    }

    public recordJobDuration(jobName: string, status: string, duration: number): void {
        this.metrics.jobDuration.labels(jobName, status).observe(duration);
    }

    public incrementJobCount(jobName: string, status: string): void {
        this.metrics.jobCount.labels(jobName, status).inc();
    }

    public incrementPipelineCount(pipelineName: string, status: string): void {
        this.metrics.pipelineCount.labels(pipelineName, status).inc();
    }

    public recordArtifactSize(jobName: string, type: string, size: number): void {
        this.metrics.artifactSize.labels(jobName, type).set(size);
    }
} 