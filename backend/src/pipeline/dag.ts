import { Pipeline, DAG, Step } from './types';

export class PipelineDAG {
    private dag: DAG;

    constructor(pipeline: Pipeline) {
        this.dag = {
            nodes: new Map<string, Step>(),
            edges: new Map<string, string[]>()
        };

        // Build nodes and edges
        for (const [name, step] of Object.entries(pipeline.jobs)) {
            this.dag.nodes.set(name, step);
            this.dag.edges.set(name, step.needs);
        }

        // Validate DAG
        if (this.hasCycle()) {
            throw new Error('Pipeline DAG has a cycle');
        }
    }

    private hasCycle(): boolean {
        const visited = new Set<string>();
        const recStack = new Set<string>();

        const visit = (node: string): boolean => {
            if (recStack.has(node)) {
                return true;
            }
            if (visited.has(node)) {
                return false;
            }

            visited.add(node);
            recStack.add(node);

            const dependencies = this.dag.edges.get(node) || [];
            for (const dep of dependencies) {
                if (visit(dep)) {
                    return true;
                }
            }

            recStack.delete(node);
            return false;
        };

        for (const node of this.dag.nodes.keys()) {
            if (visit(node)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Find jobs that are ready to run (all dependencies are satisfied)
     * @param done Set of completed job names
     * @returns Array of job names that are ready to run
     */
    getReadyJobs(done: Set<string>): string[] {
        const ready: string[] = [];

        for (const [name, needs] of this.dag.edges.entries()) {
            if (done.has(name)) {
                continue;
            }

            const unmetDeps = needs.some(dep => !done.has(dep));
            if (!unmetDeps) {
                ready.push(name);
            }
        }

        return ready;
    }

    /**
     * Get all dependencies for a job
     * @param jobName Name of the job
     * @returns Array of dependency job names
     */
    getDependencies(jobName: string): string[] {
        return this.dag.edges.get(jobName) || [];
    }

    /**
     * Get a job by name
     * @param jobName Name of the job
     * @returns The job step or undefined if not found
     */
    getJob(jobName: string): Step | undefined {
        return this.dag.nodes.get(jobName);
    }
} 