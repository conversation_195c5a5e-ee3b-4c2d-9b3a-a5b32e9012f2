import { parsePipelineYAML } from './parser';
import { PipelineDAG } from './dag';
import { JobQueue } from '../jobqueue/jobqueue';

async function main() {
    try {
        // Parse pipeline YAML
        const pipeline = await parsePipelineYAML('example.chainops.yml');
        const dag = new PipelineDAG(pipeline);
        console.log('DAG built successfully!');

        // Initialize job queue
        const queue = new JobQueue('localhost:6379', 'chainops-jobs');
        const done = new Set<string>();

        // Simulate job scheduling
        while (true) {
            const ready = dag.getReadyJobs(done);
            if (ready.length === 0) {
                break;
            }

            for (const jobName of ready) {
                const step = dag.getJob(jobName);
                if (!step) {
                    console.error(`Job ${jobName} not found`);
                    continue;
                }

                // Create and enqueue job
                const job = {
                    id: jobName,
                    name: step.name,
                    tags: step.tags,
                    payload: { script: step.script.join('\n') },
                    status: 'pending'
                };

                await queue.enqueue(job);
                console.log(`Scheduled job: ${jobName}`);
                done.add(jobName); // Simulate instant completion for demo
            }
        }

        console.log('All jobs scheduled!');
    } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : 'Unknown error');
        process.exit(1);
    }
}

// Run the example
main().catch(console.error); 