import { readFile } from 'fs/promises';
import { parse } from 'yaml';
import { Pipeline } from './types';

export async function parsePipelineYAML(path: string): Promise<Pipeline> {
    try {
        const data = await readFile(path, 'utf8');
        const pipeline = parse(data) as Pipeline;
        return pipeline;
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new Error(`Failed to parse pipeline YAML: ${errorMessage}`);
    }
} 