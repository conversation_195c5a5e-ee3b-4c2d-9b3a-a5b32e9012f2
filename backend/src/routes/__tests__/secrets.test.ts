import request from 'supertest';
import { app } from '../../index';
import jwt from 'jsonwebtoken';

// Mock the services
jest.mock('../../services/database', () => ({
  prisma: {
    secret: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    projectMember: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('../../secrets/secrets');

const { prisma } = require('../../services/database');

describe('Secrets Routes', () => {
  let authToken: string;
  let userId: string;
  let projectId: string;

  beforeEach(() => {
    userId = 'test-user-id';
    projectId = 'test-project-id';
    authToken = jwt.sign({ userId }, process.env.JWT_SECRET || 'test-secret');

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('GET /api/secrets', () => {
    it('should return secrets for user projects', async () => {
      const mockSecrets = [
        {
          id: 'secret-1',
          key: 'API_KEY',
          value: 'encrypted-value',
          description: 'Test API key',
          projectId,
          project: { id: projectId, name: 'Test Project', slug: 'test-project' },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prisma.secret.findMany.mockResolvedValue(mockSecrets);
      prisma.secret.count.mockResolvedValue(1);

      const response = await request(app)
        .get('/api/secrets')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secrets).toHaveLength(1);
      expect(response.body.data.secrets[0].value).toMatch(/^\*+$/); // Should be masked
    });

    it('should filter secrets by project', async () => {
      prisma.secret.findMany.mockResolvedValue([]);
      prisma.secret.count.mockResolvedValue(0);

      await request(app)
        .get('/api/secrets?projectId=test-project')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(prisma.secret.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            projectId: 'test-project',
          }),
        })
      );
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/secrets')
        .expect(401);
    });
  });

  describe('POST /api/secrets', () => {
    it('should create a new secret', async () => {
      const secretData = {
        key: 'NEW_SECRET',
        value: 'secret-value',
        description: 'Test secret',
        projectId,
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue({
        id: 'member-1',
        projectId,
        userId,
        role: 'DEVELOPER',
        joinedAt: new Date(),
      });

      mockPrisma.secret.findFirst.mockResolvedValue(null); // No existing secret

      const mockCreatedSecret = {
        id: 'new-secret-id',
        ...secretData,
        value: 'encrypted-value',
        project: { id: projectId, name: 'Test Project', slug: 'test-project' },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.secret.create.mockResolvedValue(mockCreatedSecret);

      const response = await request(app)
        .post('/api/secrets')
        .set('Authorization', `Bearer ${authToken}`)
        .send(secretData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secret.key).toBe(secretData.key);
      expect(response.body.data.secret.value).toMatch(/^\*+$/); // Should be masked
    });

    it('should validate secret key format', async () => {
      const invalidSecretData = {
        key: 'invalid-key-format',
        value: 'secret-value',
        projectId,
      };

      await request(app)
        .post('/api/secrets')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidSecretData)
        .expect(400);
    });

    it('should prevent duplicate secret keys in same project', async () => {
      const secretData = {
        key: 'EXISTING_SECRET',
        value: 'secret-value',
        projectId,
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue({
        id: 'member-1',
        projectId,
        userId,
        role: 'DEVELOPER',
        joinedAt: new Date(),
      });

      mockPrisma.secret.findFirst.mockResolvedValue({
        id: 'existing-secret',
        key: 'EXISTING_SECRET',
        value: 'encrypted-value',
        description: null,
        projectId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await request(app)
        .post('/api/secrets')
        .set('Authorization', `Bearer ${authToken}`)
        .send(secretData)
        .expect(409);
    });

    it('should require project access', async () => {
      const secretData = {
        key: 'NEW_SECRET',
        value: 'secret-value',
        projectId,
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue(null);

      await request(app)
        .post('/api/secrets')
        .set('Authorization', `Bearer ${authToken}`)
        .send(secretData)
        .expect(404);
    });
  });

  describe('GET /api/secrets/:id', () => {
    it('should return secret with masked value by default', async () => {
      const mockSecret = {
        id: 'secret-1',
        key: 'API_KEY',
        value: 'encrypted-value',
        description: 'Test API key',
        project: { id: projectId, name: 'Test Project', slug: 'test-project' },
      };

      mockPrisma.secret.findFirst.mockResolvedValue(mockSecret);

      const response = await request(app)
        .get('/api/secrets/secret-1')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secret.value).toMatch(/^\*+$/);
      expect(response.body.data.secret.maskedValue).toBe(true);
    });

    it('should reveal actual value when requested', async () => {
      const mockSecret = {
        id: 'secret-1',
        key: 'API_KEY',
        value: 'encrypted-value',
        description: 'Test API key',
        project: { id: projectId, name: 'Test Project', slug: 'test-project' },
      };

      mockPrisma.secret.findFirst.mockResolvedValue(mockSecret);

      const response = await request(app)
        .get('/api/secrets/secret-1?reveal=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secret.maskedValue).toBe(false);
    });

    it('should return 404 for non-existent secret', async () => {
      mockPrisma.secret.findFirst.mockResolvedValue(null);

      await request(app)
        .get('/api/secrets/non-existent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/secrets/:id', () => {
    it('should update secret', async () => {
      const updateData = {
        value: 'new-secret-value',
        description: 'Updated description',
      };

      const mockExistingSecret = {
        id: 'secret-1',
        key: 'API_KEY',
        value: 'old-encrypted-value',
        description: 'Old description',
        projectId,
        project: { id: projectId, name: 'Test Project' },
      };

      const mockUpdatedSecret = {
        ...mockExistingSecret,
        ...updateData,
        value: 'new-encrypted-value',
        project: { id: projectId, name: 'Test Project', slug: 'test-project' },
      };

      mockPrisma.secret.findFirst.mockResolvedValue(mockExistingSecret);
      mockPrisma.secret.update.mockResolvedValue(mockUpdatedSecret);

      const response = await request(app)
        .put('/api/secrets/secret-1')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secret.description).toBe(updateData.description);
    });

    it('should require proper permissions', async () => {
      mockPrisma.secret.findFirst.mockResolvedValue(null);

      await request(app)
        .put('/api/secrets/secret-1')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ value: 'new-value' })
        .expect(404);
    });
  });

  describe('DELETE /api/secrets/:id', () => {
    it('should delete secret', async () => {
      const mockSecret = {
        id: 'secret-1',
        key: 'API_KEY',
        value: 'encrypted-value',
        projectId,
        project: { id: projectId, name: 'Test Project' },
      };

      mockPrisma.secret.findFirst.mockResolvedValue(mockSecret);
      mockPrisma.secret.delete.mockResolvedValue(mockSecret);

      const response = await request(app)
        .delete('/api/secrets/secret-1')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockPrisma.secret.delete).toHaveBeenCalledWith({
        where: { id: 'secret-1' },
      });
    });

    it('should require maintainer permissions', async () => {
      mockPrisma.secret.findFirst.mockResolvedValue(null);

      await request(app)
        .delete('/api/secrets/secret-1')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('GET /api/secrets/project/:projectId/pipeline/:pipelineId', () => {
    it('should return decrypted secrets for pipeline execution', async () => {
      const mockSecrets = [
        {
          key: 'API_KEY',
          value: 'encrypted-api-key',
        },
        {
          key: 'DATABASE_URL',
          value: 'encrypted-db-url',
        },
      ];

      mockPrisma.projectMember.findFirst.mockResolvedValue({
        id: 'member-1',
        projectId,
        userId,
        role: 'DEVELOPER',
        joinedAt: new Date(),
      });

      mockPrisma.secret.findMany.mockResolvedValue(mockSecrets);

      const response = await request(app)
        .get(`/api/secrets/project/${projectId}/pipeline/pipeline-1`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.secrets).toHaveProperty('API_KEY');
      expect(response.body.data.secrets).toHaveProperty('DATABASE_URL');
    });

    it('should require project access', async () => {
      mockPrisma.projectMember.findFirst.mockResolvedValue(null);

      await request(app)
        .get(`/api/secrets/project/${projectId}/pipeline/pipeline-1`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
