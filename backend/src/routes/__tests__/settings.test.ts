import request from 'supertest';
import { app } from '../../index';
import { prisma } from '../../services/database';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

// Mock the services
jest.mock('../../services/database');
jest.mock('bcryptjs');

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('Settings Routes', () => {
  let authToken: string;
  let userId: string;
  let adminToken: string;
  let adminUserId: string;

  beforeEach(() => {
    userId = 'test-user-id';
    adminUserId = 'admin-user-id';
    authToken = jwt.sign({ userId }, process.env.JWT_SECRET || 'test-secret');
    adminToken = jwt.sign({ userId: adminUserId }, process.env.JWT_SECRET || 'test-secret');

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('GET /api/settings/user', () => {
    it('should return user settings', async () => {
      const mockUser = {
        id: userId,
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
        role: 'USER',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);

      const response = await request(app)
        .get('/api/settings/user')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        id: userId,
        email: '<EMAIL>',
        username: 'testuser',
      });
      expect(response.body.data.preferences).toBeDefined();
    });

    it('should return 404 if user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      await request(app)
        .get('/api/settings/user')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/settings/user')
        .expect(401);
    });
  });

  describe('PUT /api/settings/user/profile', () => {
    it('should update user profile', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        avatar: 'https://example.com/avatar.jpg',
      };

      const mockUpdatedUser = {
        id: userId,
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Updated',
        lastName: 'Name',
        avatar: 'https://example.com/avatar.jpg',
        role: 'USER',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.user.update.mockResolvedValue(mockUpdatedUser);

      const response = await request(app)
        .put('/api/settings/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.firstName).toBe('Updated');
      expect(response.body.data.user.lastName).toBe('Name');
    });

    it('should validate input data', async () => {
      const invalidData = {
        firstName: 'A'.repeat(51), // Too long
      };

      await request(app)
        .put('/api/settings/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('PUT /api/settings/user/password', () => {
    it('should update user password', async () => {
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123',
      };

      const mockUser = {
        id: userId,
        password: 'hashed-old-password',
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockBcrypt.compare.mockResolvedValue(true);
      mockBcrypt.hash.mockResolvedValue('hashed-new-password');
      mockPrisma.user.update.mockResolvedValue({ ...mockUser, password: 'hashed-new-password' });

      const response = await request(app)
        .put('/api/settings/user/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(mockBcrypt.compare).toHaveBeenCalledWith('oldpassword', 'hashed-old-password');
      expect(mockBcrypt.hash).toHaveBeenCalledWith('newpassword123', 12);
    });

    it('should reject incorrect current password', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
      };

      const mockUser = {
        id: userId,
        password: 'hashed-old-password',
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockBcrypt.compare.mockResolvedValue(false);

      await request(app)
        .put('/api/settings/user/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);
    });

    it('should validate password length', async () => {
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: 'short', // Too short
      };

      await request(app)
        .put('/api/settings/user/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);
    });
  });

  describe('PUT /api/settings/user/preferences', () => {
    it('should update user preferences', async () => {
      const preferencesData = {
        theme: 'dark',
        timezone: 'America/New_York',
        notifications: {
          email: false,
          browser: true,
        },
      };

      const response = await request(app)
        .put('/api/settings/user/preferences')
        .set('Authorization', `Bearer ${authToken}`)
        .send(preferencesData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.preferences).toMatchObject(preferencesData);
    });

    it('should validate theme values', async () => {
      const invalidPreferences = {
        theme: 'invalid-theme',
      };

      await request(app)
        .put('/api/settings/user/preferences')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidPreferences)
        .expect(400);
    });
  });

  describe('GET /api/settings/project/:projectId', () => {
    it('should return project settings', async () => {
      const projectId = 'test-project-id';
      const mockProjectMember = {
        id: 'member-1',
        projectId,
        userId,
        role: 'OWNER',
        project: {
          id: projectId,
          name: 'Test Project',
          slug: 'test-project',
          description: 'Test description',
          repositoryUrl: 'https://github.com/test/repo',
          defaultBranch: 'main',
          settings: { custom: 'setting' },
          isActive: true,
        },
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue(mockProjectMember);

      const response = await request(app)
        .get(`/api/settings/project/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.project.name).toBe('Test Project');
      expect(response.body.data.settings).toBeDefined();
      expect(response.body.data.userRole).toBe('OWNER');
    });

    it('should return 404 for unauthorized project access', async () => {
      const projectId = 'unauthorized-project';
      mockPrisma.projectMember.findFirst.mockResolvedValue(null);

      await request(app)
        .get(`/api/settings/project/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('PUT /api/settings/project/:projectId', () => {
    it('should update project settings', async () => {
      const projectId = 'test-project-id';
      const settingsData = {
        settings: {
          general: {
            autoDeleteArtifacts: false,
            artifactRetentionDays: 60,
          },
          notifications: {
            onFailure: true,
            channels: ['email', 'slack'],
          },
        },
      };

      const mockProjectMember = {
        id: 'member-1',
        projectId,
        userId,
        role: 'OWNER',
      };

      const mockUpdatedProject = {
        id: projectId,
        name: 'Test Project',
        slug: 'test-project',
        settings: settingsData.settings,
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue(mockProjectMember);
      mockPrisma.project.update.mockResolvedValue(mockUpdatedProject);

      const response = await request(app)
        .put(`/api/settings/project/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(settingsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.settings).toMatchObject(settingsData.settings);
    });

    it('should require maintainer or owner permissions', async () => {
      const projectId = 'test-project-id';
      const mockProjectMember = {
        id: 'member-1',
        projectId,
        userId,
        role: 'VIEWER', // Insufficient permissions
      };

      mockPrisma.projectMember.findFirst.mockResolvedValue(mockProjectMember);

      await request(app)
        .put(`/api/settings/project/${projectId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ settings: {} })
        .expect(403);
    });
  });

  describe('GET /api/settings/system', () => {
    it('should return system settings for admin users', async () => {
      const mockAdminUser = {
        id: adminUserId,
        role: 'ADMIN',
      };

      // Mock the auth middleware to return admin user
      mockPrisma.user.findUnique.mockResolvedValue(mockAdminUser);

      const response = await request(app)
        .get('/api/settings/system')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.settings).toBeDefined();
      expect(response.body.data.settings.general).toBeDefined();
      expect(response.body.data.settings.integrations).toBeDefined();
    });

    it('should deny access to non-admin users', async () => {
      const mockRegularUser = {
        id: userId,
        role: 'USER',
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockRegularUser);

      await request(app)
        .get('/api/settings/system')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);
    });
  });
});
