import { Router, Response } from 'express';
import { prisma } from '../services/database';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Get dashboard statistics
router.get('/stats', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  // Get user's accessible projects
  const userProjects = await prisma.project.findMany({
    where: {
      members: {
        some: {
          userId,
        },
      },
    },
    select: { id: true },
  });

  const projectIds = userProjects.map(p => p.id);

  // Get statistics
  const [totalPipelines, activeJobs, pipelineRuns] = await Promise.all([
    // Total pipelines user has access to
    prisma.pipeline.count({
      where: {
        projectId: { in: projectIds },
        isActive: true,
      },
    }),

    // Active jobs
    prisma.job.count({
      where: {
        pipelineRun: {
          pipeline: {
            projectId: { in: projectIds },
          },
        },
        status: { in: ['PENDING', 'QUEUED', 'RUNNING'] },
      },
    }),

    // Recent pipeline runs for success rate calculation
    prisma.pipelineRun.findMany({
      where: {
        pipeline: {
          projectId: { in: projectIds },
        },
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      select: {
        status: true,
        startedAt: true,
        finishedAt: true,
      },
    }),
  ]);

  // Calculate success rate
  const totalRuns = pipelineRuns.length;
  const successfulRuns = pipelineRuns.filter(run => run.status === 'SUCCESS').length;
  const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;

  // Calculate average duration
  const completedRuns = pipelineRuns.filter(run => 
    run.startedAt && run.finishedAt && run.status !== 'RUNNING'
  );
  
  let avgDuration = '0s';
  if (completedRuns.length > 0) {
    const totalDuration = completedRuns.reduce((sum, run) => {
      const duration = run.finishedAt!.getTime() - run.startedAt!.getTime();
      return sum + duration;
    }, 0);
    
    const avgMs = totalDuration / completedRuns.length;
    const avgSeconds = Math.round(avgMs / 1000);
    
    if (avgSeconds < 60) {
      avgDuration = `${avgSeconds}s`;
    } else if (avgSeconds < 3600) {
      const minutes = Math.floor(avgSeconds / 60);
      const seconds = avgSeconds % 60;
      avgDuration = `${minutes}m ${seconds}s`;
    } else {
      const hours = Math.floor(avgSeconds / 3600);
      const minutes = Math.floor((avgSeconds % 3600) / 60);
      avgDuration = `${hours}h ${minutes}m`;
    }
  }

  res.json({
    success: true,
    data: {
      totalPipelines,
      activeJobs,
      successRate: Math.round(successRate * 10) / 10, // Round to 1 decimal
      avgDuration,
    },
  });
}));

// Get recent activity
router.get('/activity', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 10;

  // Get user's accessible projects
  const userProjects = await prisma.project.findMany({
    where: {
      members: {
        some: {
          userId,
        },
      },
    },
    select: { id: true },
  });

  const projectIds = userProjects.map(p => p.id);

  // Get recent pipeline runs
  const recentRuns = await prisma.pipelineRun.findMany({
    where: {
      pipeline: {
        projectId: { in: projectIds },
      },
    },
    include: {
      pipeline: {
        include: {
          project: {
            select: { name: true },
          },
        },
      },
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
  });

  // Transform to activity items
  const activities = recentRuns.map(run => {
    const userName = 'System'; // Default to 'System' if user info is not available

    let title = '';
    let type: 'pipeline_run' | 'job_completed' | 'deployment' | 'user_action' = 'pipeline_run';

    switch (run.status) {
      case 'SUCCESS':
        title = `Pipeline "${run.pipeline.name}" completed successfully`;
        break;
      case 'FAILED':
        title = `Pipeline "${run.pipeline.name}" failed`;
        break;
      case 'RUNNING':
        title = `Pipeline "${run.pipeline.name}" is running`;
        break;
      case 'CANCELLED':
        title = `Pipeline "${run.pipeline.name}" was cancelled`;
        break;
      default:
        title = `Pipeline "${run.pipeline.name}" is ${run.status.toLowerCase()}`;
    }

    return {
      id: run.id,
      type,
      title,
      description: run.pipeline.project.name,
      timestamp: run.createdAt.toISOString(),
      status: run.status.toLowerCase() as 'success' | 'failed' | 'running' | 'pending',
      user: {
        name: userName,
      },
    };
  });

  res.json({
    success: true,
    data: { activities },
  });
}));

// Get pipeline status overview
router.get('/pipelines', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 10;

  // Get user's accessible projects
  const userProjects = await prisma.project.findMany({
    where: {
      members: {
        some: {
          userId,
        },
      },
    },
    select: { id: true },
  });

  const projectIds = userProjects.map(p => p.id);

  // Get pipelines with their latest runs
  const pipelines = await prisma.pipeline.findMany({
    where: {
      projectId: { in: projectIds },
      isActive: true,
    },
    include: {
      project: {
        select: { name: true },
      },
      runs: {
        orderBy: { createdAt: 'desc' },
        take: 1,
        select: {
          id: true,
          status: true,
          startedAt: true,
          finishedAt: true,
          createdAt: true,
          trigger: true,
        },
      },
    },
    orderBy: { updatedAt: 'desc' },
    take: limit,
  });

  // Transform to pipeline status
  const pipelineStatus = pipelines.map(pipeline => {
    const latestRun = pipeline.runs[0];
    
    let duration = '0s';
    if (latestRun?.startedAt && latestRun?.finishedAt) {
      const durationMs = latestRun.finishedAt.getTime() - latestRun.startedAt.getTime();
      const durationSeconds = Math.round(durationMs / 1000);
      
      if (durationSeconds < 60) {
        duration = `${durationSeconds}s`;
      } else {
        const minutes = Math.floor(durationSeconds / 60);
        const seconds = durationSeconds % 60;
        duration = `${minutes}m ${seconds}s`;
      }
    }

    // Extract branch from trigger data
    let branch = 'main';
    if (latestRun?.trigger && typeof latestRun.trigger === 'object') {
      const trigger = latestRun.trigger as any;
      branch = trigger.branch || 'main';
    }

    return {
      id: pipeline.id,
      name: pipeline.name,
      project: pipeline.project.name,
      status: latestRun?.status?.toLowerCase() || 'pending',
      lastRun: latestRun?.createdAt?.toISOString() || new Date().toISOString(),
      duration,
      branch,
    };
  });

  res.json({
    success: true,
    data: { pipelines: pipelineStatus },
  });
}));

export default router;
