import { Router, Request, Response } from 'express';
import { DatabaseService } from '../services/database';
import { QueueService } from '../services/queue';
import { MinioService } from '../services/minio';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// Basic health check
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    message: 'ChainOps Backend is healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
}));

// Detailed health check
router.get('/detailed', asyncHandler(async (req: Request, res: Response) => {
  const checks = {
    database: false,
    queue: false,
    storage: false,
  };

  const startTime = Date.now();

  try {
    // Check database
    checks.database = await DatabaseService.healthCheck();
  } catch (error) {
    checks.database = false;
  }

  try {
    // Check queue service
    checks.queue = await QueueService.healthCheck();
  } catch (error) {
    checks.queue = false;
  }

  try {
    // Check MinIO storage
    checks.storage = await MinioService.healthCheck();
  } catch (error) {
    checks.storage = false;
  }

  const responseTime = Date.now() - startTime;
  const allHealthy = Object.values(checks).every(check => check === true);

  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    message: allHealthy ? 'All services are healthy' : 'Some services are unhealthy',
    timestamp: new Date().toISOString(),
    responseTime: `${responseTime}ms`,
    checks,
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
}));

// Readiness probe
router.get('/ready', asyncHandler(async (req: Request, res: Response) => {
  const isReady = await DatabaseService.healthCheck();
  
  res.status(isReady ? 200 : 503).json({
    success: isReady,
    message: isReady ? 'Service is ready' : 'Service is not ready',
    timestamp: new Date().toISOString(),
  });
}));

// Liveness probe
router.get('/live', asyncHandler(async (req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    message: 'Service is alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  });
}));

export default router;
