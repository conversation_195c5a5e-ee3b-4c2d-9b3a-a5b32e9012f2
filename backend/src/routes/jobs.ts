import { Router, Response } from 'express';
import { prisma } from '../services/database';
import { async<PERSON><PERSON><PERSON>, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { MinioService } from '../services/minio';
import { SocketService } from '../services/socket';
import { logger } from '../utils/logger';
import { Prisma } from '@prisma/client';

const router = Router();

// Get all jobs for user's projects
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { page = 1, limit = 10, search, projectId, pipelineId, status } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    pipelineRun: {
      pipeline: {
        project: {
          members: {
            some: {
              userId: req.user!.id,
            },
          },
        },
      },
    },
  };
  
  if (search) {
    where.name = { contains: search as string, mode: 'insensitive' };
  }
  
  if (projectId) {
    where.pipelineRun.pipeline.projectId = projectId as string;
  }
  
  if (pipelineId) {
    where.pipelineRun.pipelineId = pipelineId as string;
  }
  
  if (status) {
    where.status = status as string;
  }

  const [jobs, total] = await Promise.all([
    prisma.job.findMany({
      where,
      skip,
      take,
      include: {
        pipelineRun: {
          include: {
            pipeline: {
              select: {
                id: true,
                name: true,
                project: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        environment: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.job.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      jobs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Get job by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const job = await prisma.job.findFirst({
    where: {
      id,
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
              },
            },
          },
        },
      },
    },
    include: {
      pipelineRun: {
        include: {
          pipeline: {
            select: {
              id: true,
              name: true,
              project: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          },
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      environment: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },
  });

  if (!job) {
    throw new CustomError('Job not found', 404);
  }

  res.json({
    success: true,
    data: { job },
  });
}));

// Get job logs
router.get('/:id/logs', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { stream = false, follow = false } = req.query;

  const job = await prisma.job.findFirst({
    where: {
      id,
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
              },
            },
          },
        },
      },
    },
    select: {
      id: true,
      name: true,
      status: true,
      logs: true,
      pipelineRun: {
        select: {
          id: true,
          pipeline: {
            select: {
              id: true,
              name: true,
              project: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!job) {
    throw new CustomError('Job not found', 404);
  }

  // If streaming is requested and job is running, set up SSE
  if (stream === 'true' && follow === 'true' && job.status === 'RUNNING') {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Send initial logs
    let logs = job.logs || '';
    try {
      const logBuffer = await MinioService.downloadFile(`jobs/${id}/logs/output.log`);
      logs = logBuffer.toString('utf8');
    } catch (error) {
      logger.warn('Failed to get logs from MinIO, using database logs:', error);
    }

    // Send existing logs
    res.write(`data: ${JSON.stringify({ type: 'logs', data: logs })}\n\n`);

    // Set up real-time log streaming
    const logListener = (data: any) => {
      if (data.jobId === id && data.type === 'log') {
        res.write(`data: ${JSON.stringify({ type: 'log-line', data: data.message })}\n\n`);
      } else if (data.jobId === id && data.type === 'status') {
        res.write(`data: ${JSON.stringify({ type: 'status', data: data.status })}\n\n`);

        // Close stream if job is finished
        if (['SUCCESS', 'FAILED', 'CANCELLED'].includes(data.status)) {
          res.write(`data: ${JSON.stringify({ type: 'end' })}\n\n`);
          res.end();
        }
      }
    };

    // Subscribe to job updates
    const socketService = SocketService.getInstance();
    socketService.subscribeToJobLogs(id, logListener);

    // Clean up on client disconnect
    req.on('close', () => {
      socketService.unsubscribeFromJobLogs(id, logListener);
    });

    return;
  }

  // Regular log retrieval
  let logs = job.logs || '';

  try {
    const logBuffer = await MinioService.downloadFile(`jobs/${id}/logs/output.log`);
    logs = logBuffer.toString('utf8');
  } catch (error) {
    logger.warn('Failed to get logs from MinIO, using database logs:', error);
  }

  res.json({
    success: true,
    data: {
      logs,
      job: {
        id: job.id,
        name: job.name,
        status: job.status,
        pipeline: job.pipelineRun.pipeline,
      },
    },
  });
}));

// Get job artifacts
router.get('/:id/artifacts', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const job = await prisma.job.findFirst({
    where: {
      id,
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
              },
            },
          },
        },
      },
    },
    select: {
      id: true,
      artifacts: true,
    },
  });

  if (!job) {
    throw new CustomError('Job not found', 404);
  }

  // Get artifacts from MinIO
  let artifacts: any[] = [];
  
  try {
    const minioArtifacts = await MinioService.getJobArtifacts(id);
    artifacts = await Promise.all(
      minioArtifacts.map(async (artifact) => {
        const url = await MinioService.getFileUrl(artifact.name!);
        return {
          name: artifact.name!.split('/').pop(),
          path: artifact.name,
          size: artifact.size,
          lastModified: artifact.lastModified,
          url,
        };
      })
    );
  } catch (error) {
    console.warn('Failed to get artifacts from MinIO:', error);
    // Fallback to database artifacts if available
    artifacts = job.artifacts as any[] || [];
  }

  res.json({
    success: true,
    data: { artifacts },
  });
}));

// Download artifact
router.get('/:id/artifacts/:artifactPath(*)', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id, artifactPath } = req.params;

  const job = await prisma.job.findFirst({
    where: {
      id,
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
              },
            },
          },
        },
      },
    },
    select: {
      id: true,
    },
  });

  if (!job) {
    throw new CustomError('Job not found', 404);
  }

  try {
    const objectName = `jobs/${id}/artifacts/${artifactPath}`;
    const fileBuffer = await MinioService.downloadFile(objectName);
    const fileInfo = await MinioService.getFileInfo(objectName);

    res.setHeader('Content-Type', fileInfo.metaData?.['content-type'] || 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${artifactPath.split('/').pop()}"`);
    res.setHeader('Content-Length', fileBuffer.length);

    res.send(fileBuffer);
  } catch (error) {
    throw new CustomError('Artifact not found', 404);
  }
}));

// Cancel job
router.post('/:id/cancel', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const job = await prisma.job.findFirst({
    where: {
      id,
      status: { in: ['PENDING', 'QUEUED', 'RUNNING'] },
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
                role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
              },
            },
          },
        },
      },
    },
  });

  if (!job) {
    throw new CustomError('Job not found or cannot be cancelled', 404);
  }

  const updatedJob = await prisma.job.update({
    where: { id },
    data: {
      status: 'CANCELLED',
      finishedAt: new Date(),
    },
  });

  res.json({
    success: true,
    message: 'Job cancelled successfully',
    data: { job: updatedJob },
  });
}));

// Retry job
router.post('/:id/retry', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const job = await prisma.job.findFirst({
    where: {
      id,
      status: { in: ['FAILED', 'CANCELLED'] },
      pipelineRun: {
        pipeline: {
          project: {
            members: {
              some: {
                userId: req.user!.id,
                role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
              },
            },
          },
        },
      },
    },
    include: {
      pipelineRun: true,
    },
  });

  if (!job) {
    throw new CustomError('Job not found or cannot be retried', 404);
  }

  // Create a new job with the same configuration
  const newJob = await prisma.job.create({
    data: {
      name: job.name,
      pipelineRunId: job.pipelineRunId,
      userId: req.user!.id,
      environmentId: job.environmentId,
      config: job.config as Prisma.InputJsonValue,
      status: 'PENDING',
    },
  });

  // Add to queue for processing
  // Note: This would need to be implemented with the actual job queue system

  res.status(201).json({
    success: true,
    message: 'Job retry initiated successfully',
    data: { job: newJob },
  });
}));

export default router;
