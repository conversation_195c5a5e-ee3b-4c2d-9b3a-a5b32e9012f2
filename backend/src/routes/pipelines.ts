import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { QueueService } from '../services/queue';

const router = Router();

// Get all pipelines for user's projects
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { page = 1, limit = 10, search, projectId, status } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    project: {
      members: {
        some: {
          userId: req.user!.id,
        },
      },
    },
  };
  
  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }
  
  if (projectId) {
    where.projectId = projectId as string;
  }
  
  if (status) {
    where.status = status as string;
  }

  const [pipelines, total] = await Promise.all([
    prisma.pipeline.findMany({
      where,
      skip,
      take,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        runs: {
          take: 1,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            number: true,
            status: true,
            startedAt: true,
            finishedAt: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            runs: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    }),
    prisma.pipeline.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      pipelines,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Create new pipeline
router.post('/', [
  body('name').isLength({ min: 1, max: 100 }),
  body('slug').isLength({ min: 1, max: 50 }).matches(/^[a-z0-9-]+$/),
  body('description').optional().isLength({ max: 500 }),
  body('projectId').isUUID(),
  body('config').isObject(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { name, slug, description, projectId, config } = req.body;

  // Check if user has access to project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
    },
  });

  if (!projectMember) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Check if pipeline slug already exists in project
  const existingPipeline = await prisma.pipeline.findFirst({
    where: {
      slug,
      projectId,
    },
  });

  if (existingPipeline) {
    throw new CustomError('Pipeline with this slug already exists in project', 409);
  }

  const pipeline = await prisma.pipeline.create({
    data: {
      name,
      slug,
      description,
      projectId,
      userId: req.user!.id,
      config,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  res.status(201).json({
    success: true,
    message: 'Pipeline created successfully',
    data: { pipeline },
  });
}));

// Get pipeline by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      triggers: true,
      runs: {
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          jobs: {
            select: {
              id: true,
              name: true,
              status: true,
              startedAt: true,
              finishedAt: true,
            },
          },
        },
      },
      _count: {
        select: {
          runs: true,
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found', 404);
  }

  res.json({
    success: true,
    data: { pipeline },
  });
}));

// Update pipeline
router.put('/:id', [
  body('name').optional().isLength({ min: 1, max: 100 }),
  body('description').optional().isLength({ max: 500 }),
  body('config').optional().isObject(),
  body('isActive').optional().isBoolean(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { name, description, config, isActive } = req.body;

  // Check if user has permission to update pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or insufficient permissions', 404);
  }

  const updatedPipeline = await prisma.pipeline.update({
    where: { id },
    data: {
      name,
      description,
      config,
      isActive,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  res.json({
    success: true,
    message: 'Pipeline updated successfully',
    data: { pipeline: updatedPipeline },
  });
}));

// Trigger pipeline run
router.post('/:id/run', [
  body('variables').optional().isObject(),
  body('branch').optional().isString(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { variables, branch } = req.body;

  // Check if user has permission to run pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      isActive: true,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
    include: {
      project: true,
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or inactive', 404);
  }

  // Get next run number
  const lastRun = await prisma.pipelineRun.findFirst({
    where: { pipelineId: id },
    orderBy: { number: 'desc' },
    select: { number: true },
  });

  const runNumber = (lastRun?.number || 0) + 1;

  // Create pipeline run
  const pipelineRun = await prisma.pipelineRun.create({
    data: {
      number: runNumber,
      pipelineId: id,
      status: 'PENDING',
      trigger: {
        type: 'MANUAL',
        userId: req.user!.id,
        branch: branch || pipeline.project.defaultBranch,
      },
      variables: variables || {},
    },
    include: {
      pipeline: {
        select: {
          id: true,
          name: true,
          config: true,
        },
      },
    },
  });

  // Add to queue for processing
  await QueueService.addPipelineJob({
    id: pipelineRun.id,
    type: 'pipeline-run',
    payload: {
      pipelineRunId: pipelineRun.id,
      pipelineId: id,
      config: pipeline.config,
      variables: variables || {},
    },
    userId: req.user!.id,
    projectId: pipeline.projectId,
    pipelineId: id,
    runId: pipelineRun.id,
  });

  res.status(201).json({
    success: true,
    message: 'Pipeline run triggered successfully',
    data: { pipelineRun },
  });
}));

// Delete pipeline
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if user has permission to delete pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER'] },
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or insufficient permissions', 404);
  }

  await prisma.pipeline.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Pipeline deleted successfully',
  });
}));

export default router;
