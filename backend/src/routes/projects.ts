import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { QueueService } from '../services/queue';
import { logger } from '../utils/logger';

const router = Router();

// Get all projects for current user
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { page = 1, limit = 10, search, organizationId } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    members: {
      some: {
        userId: req.user!.id,
      },
    },
  };
  
  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }
  
  if (organizationId) {
    where.organizationId = organizationId as string;
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where,
      skip,
      take,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        },
        pipelines: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        _count: {
          select: {
            pipelines: true,
            environments: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    }),
    prisma.project.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      projects,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Create new project
router.post('/', [
  body('name').isLength({ min: 1, max: 100 }),
  body('slug').isLength({ min: 1, max: 50 }).matches(/^[a-z0-9-]+$/),
  body('description').optional().isLength({ max: 500 }),
  body('repositoryUrl').optional().isURL(),
  body('defaultBranch').optional().isLength({ min: 1, max: 50 }),
  body('organizationId').optional().isUUID(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { name, slug, description, repositoryUrl, defaultBranch, organizationId } = req.body;

  // Check if project slug already exists in organization
  const existingProject = await prisma.project.findFirst({
    where: {
      slug,
      organizationId: organizationId || null,
    },
  });

  if (existingProject) {
    throw new CustomError('Project with this slug already exists', 409);
  }

  // If organizationId is provided, check if user is member
  if (organizationId) {
    const orgMember = await prisma.organizationMember.findFirst({
      where: {
        organizationId,
        userId: req.user!.id,
      },
    });

    if (!orgMember) {
      throw new CustomError('You are not a member of this organization', 403);
    }
  }

  const project = await prisma.project.create({
    data: {
      name,
      slug,
      description,
      repositoryUrl,
      defaultBranch: defaultBranch || 'main',
      organizationId,
      members: {
        create: {
          userId: req.user!.id,
          role: 'OWNER',
        },
      },
      environments: {
        createMany: {
          data: [
            {
              name: 'Development',
              slug: 'development',
              description: 'Development environment',
            },
            {
              name: 'Staging',
              slug: 'staging',
              description: 'Staging environment',
            },
            {
              name: 'Production',
              slug: 'production',
              description: 'Production environment',
            },
          ],
        },
      },
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      },
      environments: true,
    },
  });

  // Register project with pipeline engine
  try {
    await QueueService.addNotification({
      type: 'project-created',
      projectId: project.id,
      userId: req.user!.id,
      data: {
        name: project.name,
        slug: project.slug,
        repositoryUrl: project.repositoryUrl,
      },
    });

    logger.info(`Project ${project.name} registered with pipeline engine`, {
      projectId: project.id,
      userId: req.user!.id,
    });
  } catch (error) {
    logger.error('Failed to register project with pipeline engine:', error);
    // Don't fail the request, just log the error
  }

  res.status(201).json({
    success: true,
    message: 'Project created successfully',
    data: { project },
  });
}));

// Get project by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const project = await prisma.project.findFirst({
    where: {
      id,
      members: {
        some: {
          userId: req.user!.id,
        },
      },
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      },
      environments: true,
      pipelines: {
        include: {
          runs: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              number: true,
              status: true,
              startedAt: true,
              finishedAt: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      },
      _count: {
        select: {
          pipelines: true,
          environments: true,
          secrets: true,
        },
      },
    },
  });

  if (!project) {
    throw new CustomError('Project not found', 404);
  }

  res.json({
    success: true,
    data: { project },
  });
}));

// Update project
router.put('/:id', [
  body('name').optional().isLength({ min: 1, max: 100 }),
  body('description').optional().isLength({ max: 500 }),
  body('repositoryUrl').optional().isURL(),
  body('defaultBranch').optional().isLength({ min: 1, max: 50 }),
  body('isActive').optional().isBoolean(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { name, description, repositoryUrl, defaultBranch, isActive } = req.body;

  // Check if user has permission to update project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId: id,
      userId: req.user!.id,
      role: { in: ['OWNER', 'MAINTAINER'] },
    },
  });

  if (!projectMember) {
    throw new CustomError('Insufficient permissions', 403);
  }

  const project = await prisma.project.update({
    where: { id },
    data: {
      name,
      description,
      repositoryUrl,
      defaultBranch,
      isActive,
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      members: {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      },
    },
  });

  res.json({
    success: true,
    message: 'Project updated successfully',
    data: { project },
  });
}));

// Delete project
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if user is project owner
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId: id,
      userId: req.user!.id,
      role: 'OWNER',
    },
  });

  if (!projectMember) {
    throw new CustomError('Only project owners can delete projects', 403);
  }

  await prisma.project.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Project deleted successfully',
  });
}));

export default router;
