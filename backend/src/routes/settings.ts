import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { logger } from '../utils/logger';
import bcrypt from 'bcryptjs';

const router = Router();

// Get user settings
router.get('/user', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      username: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Get user preferences (stored in a separate table or JSON field)
  // For now, we'll use mock preferences
  const preferences = {
    theme: 'light',
    notifications: {
      email: true,
      browser: true,
      slack: false,
    },
    dashboard: {
      defaultView: 'overview',
      itemsPerPage: 10,
    },
    timezone: 'UTC',
    language: 'en',
  };

  res.json({
    success: true,
    data: {
      user,
      preferences,
    },
  });
}));

// Update user profile
router.put('/user/profile', [
  body('firstName').optional().isLength({ min: 1, max: 50 }),
  body('lastName').optional().isLength({ min: 1, max: 50 }),
  body('avatar').optional().isURL(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { firstName, lastName, avatar } = req.body;

  const user = await prisma.user.update({
    where: { id: req.user!.id },
    data: {
      firstName,
      lastName,
      avatar,
    },
    select: {
      id: true,
      email: true,
      username: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  logger.info(`User profile updated`, {
    userId: req.user!.id,
    changes: { firstName, lastName, avatar },
  });

  res.json({
    success: true,
    message: 'Profile updated successfully',
    data: { user },
  });
}));

// Update user password
router.put('/user/password', [
  body('currentPassword').isLength({ min: 1 }),
  body('newPassword').isLength({ min: 8, max: 128 }),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { currentPassword, newPassword } = req.body;

  // Get current user with password
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      password: true,
    },
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
  if (!isCurrentPasswordValid) {
    throw new CustomError('Current password is incorrect', 400);
  }

  // Hash new password
  const hashedNewPassword = await bcrypt.hash(newPassword, 12);

  // Update password
  await prisma.user.update({
    where: { id: req.user!.id },
    data: {
      password: hashedNewPassword,
    },
  });

  logger.info(`User password updated`, {
    userId: req.user!.id,
  });

  res.json({
    success: true,
    message: 'Password updated successfully',
  });
}));

// Update user preferences
router.put('/user/preferences', [
  body('theme').optional().isIn(['light', 'dark', 'auto']),
  body('notifications').optional().isObject(),
  body('dashboard').optional().isObject(),
  body('timezone').optional().isString(),
  body('language').optional().isString(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const preferences = req.body;

  // For now, we'll just return the preferences
  // In a real implementation, you'd store these in a user_preferences table
  // or in a JSON field on the user model

  logger.info(`User preferences updated`, {
    userId: req.user!.id,
    preferences,
  });

  res.json({
    success: true,
    message: 'Preferences updated successfully',
    data: { preferences },
  });
}));

// Get project settings
router.get('/project/:projectId', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { projectId } = req.params;

  // Check if user has access to project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          repositoryUrl: true,
          defaultBranch: true,
          settings: true,
          isActive: true,
        },
      },
    },
  });

  if (!projectMember) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Default project settings
  const defaultSettings = {
    general: {
      autoDeleteArtifacts: true,
      artifactRetentionDays: 30,
      maxConcurrentJobs: 5,
    },
    notifications: {
      onSuccess: false,
      onFailure: true,
      onStart: false,
      channels: ['email'],
    },
    security: {
      requireApprovalForProduction: true,
      allowForkPullRequests: false,
      secretsAccessLevel: 'maintainer',
    },
    integrations: {
      slack: {
        enabled: false,
        webhook: '',
        channel: '',
      },
      github: {
        enabled: false,
        webhookSecret: '',
      },
    },
  };

  const settings = {
    ...defaultSettings,
    ...(projectMember.project.settings as any || {}),
  };

  res.json({
    success: true,
    data: {
      project: projectMember.project,
      settings,
      userRole: projectMember.role,
    },
  });
}));

// Update project settings
router.put('/project/:projectId', [
  body('settings').isObject(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { projectId } = req.params;
  const { settings } = req.body;

  // Check if user has permission to update project settings
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
      role: { in: ['OWNER', 'MAINTAINER'] },
    },
  });

  if (!projectMember) {
    throw new CustomError('Insufficient permissions to update project settings', 403);
  }

  // Update project settings
  const project = await prisma.project.update({
    where: { id: projectId },
    data: {
      settings: settings,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      settings: true,
    },
  });

  logger.info(`Project settings updated`, {
    projectId,
    userId: req.user!.id,
    settings,
  });

  res.json({
    success: true,
    message: 'Project settings updated successfully',
    data: {
      project,
      settings: project.settings,
    },
  });
}));

// Get system settings (admin only)
router.get('/system', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // Check if user is admin
  if (req.user!.role !== 'ADMIN') {
    throw new CustomError('Admin access required', 403);
  }

  // Mock system settings
  const systemSettings = {
    general: {
      siteName: 'ChainOps CI/CD',
      siteUrl: process.env.SITE_URL || 'http://localhost:3000',
      allowRegistration: true,
      defaultUserRole: 'USER',
    },
    security: {
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      passwordMinLength: 8,
      requireTwoFactor: false,
    },
    integrations: {
      vault: {
        enabled: !!process.env.VAULT_ADDR,
        address: process.env.VAULT_ADDR,
      },
      redis: {
        enabled: !!process.env.REDIS_URL,
        url: process.env.REDIS_URL,
      },
      minio: {
        enabled: !!process.env.MINIO_ENDPOINT,
        endpoint: process.env.MINIO_ENDPOINT,
      },
    },
    limits: {
      maxProjectsPerUser: 10,
      maxPipelinesPerProject: 50,
      maxJobsPerPipeline: 100,
      artifactSizeLimit: 1024 * 1024 * 1024, // 1GB
    },
  };

  res.json({
    success: true,
    data: { settings: systemSettings },
  });
}));

export default router;
