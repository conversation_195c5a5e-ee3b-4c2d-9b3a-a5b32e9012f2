import { Router, Request, Response } from 'express';
import crypto from 'crypto';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { QueueService } from '../services/queue';
import { logger } from '../utils/logger';

const router = Router();

// Verify webhook signature
function verifySignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(`sha256=${expectedSignature}`),
    Buffer.from(signature)
  );
}

// GitHub webhook handler
router.post('/github', asyncHandler(async (req: Request, res: Response) => {
  const signature = req.headers['x-hub-signature-256'] as string;
  const event = req.headers['x-github-event'] as string;
  const payload = JSON.stringify(req.body);

  if (!signature) {
    throw new CustomError('Missing signature', 400);
  }

  // Verify signature
  const webhookSecret = process.env.WEBHOOK_SECRET || 'webhook-secret-key';
  if (!verifySignature(payload, signature, webhookSecret)) {
    throw new CustomError('Invalid signature', 401);
  }

  logger.info(`Received GitHub webhook: ${event}`);

  // Handle different GitHub events
  switch (event) {
    case 'push':
      await handleGitHubPush(req.body);
      break;
    case 'pull_request':
      await handleGitHubPullRequest(req.body);
      break;
    case 'release':
      await handleGitHubRelease(req.body);
      break;
    default:
      logger.info(`Unhandled GitHub event: ${event}`);
  }

  res.status(200).json({ success: true, message: 'Webhook processed' });
}));

// GitLab webhook handler
router.post('/gitlab', asyncHandler(async (req: Request, res: Response) => {
  const token = req.headers['x-gitlab-token'] as string;
  const event = req.headers['x-gitlab-event'] as string;

  if (!token) {
    throw new CustomError('Missing token', 400);
  }

  // Verify token
  const webhookSecret = process.env.WEBHOOK_SECRET || 'webhook-secret-key';
  if (token !== webhookSecret) {
    throw new CustomError('Invalid token', 401);
  }

  logger.info(`Received GitLab webhook: ${event}`);

  // Handle different GitLab events
  switch (event) {
    case 'Push Hook':
      await handleGitLabPush(req.body);
      break;
    case 'Merge Request Hook':
      await handleGitLabMergeRequest(req.body);
      break;
    case 'Tag Push Hook':
      await handleGitLabTagPush(req.body);
      break;
    default:
      logger.info(`Unhandled GitLab event: ${event}`);
  }

  res.status(200).json({ success: true, message: 'Webhook processed' });
}));

// Bitbucket webhook handler
router.post('/bitbucket', asyncHandler(async (req: Request, res: Response) => {
  const event = req.headers['x-event-key'] as string;

  logger.info(`Received Bitbucket webhook: ${event}`);

  // Handle different Bitbucket events
  switch (event) {
    case 'repo:push':
      await handleBitbucketPush(req.body);
      break;
    case 'pullrequest:created':
    case 'pullrequest:updated':
      await handleBitbucketPullRequest(req.body);
      break;
    default:
      logger.info(`Unhandled Bitbucket event: ${event}`);
  }

  res.status(200).json({ success: true, message: 'Webhook processed' });
}));

// GitHub event handlers
async function handleGitHubPush(payload: any) {
  const { repository, ref, commits, pusher } = payload;
  
  if (!repository || !ref) return;

  const branch = ref.replace('refs/heads/', '');
  const repositoryUrl = repository.clone_url;

  await triggerPipelinesForRepository(repositoryUrl, branch, {
    type: 'push',
    commits,
    pusher,
    repository: repository.full_name,
  });
}

async function handleGitHubPullRequest(payload: any) {
  const { action, pull_request, repository } = payload;
  
  if (!['opened', 'synchronize', 'reopened'].includes(action)) return;

  const branch = pull_request.head.ref;
  const repositoryUrl = repository.clone_url;

  await triggerPipelinesForRepository(repositoryUrl, branch, {
    type: 'pull_request',
    action,
    pull_request: {
      number: pull_request.number,
      title: pull_request.title,
      head: pull_request.head.sha,
      base: pull_request.base.ref,
    },
    repository: repository.full_name,
  });
}

async function handleGitHubRelease(payload: any) {
  const { action, release, repository } = payload;
  
  if (action !== 'published') return;

  const tag = release.tag_name;
  const repositoryUrl = repository.clone_url;

  await triggerPipelinesForRepository(repositoryUrl, tag, {
    type: 'release',
    release: {
      tag: release.tag_name,
      name: release.name,
      prerelease: release.prerelease,
    },
    repository: repository.full_name,
  });
}

// GitLab event handlers
async function handleGitLabPush(payload: any) {
  const { project, ref, commits, user_name } = payload;
  
  if (!project || !ref) return;

  const branch = ref.replace('refs/heads/', '');
  const repositoryUrl = project.git_http_url;

  await triggerPipelinesForRepository(repositoryUrl, branch, {
    type: 'push',
    commits,
    pusher: user_name,
    repository: project.path_with_namespace,
  });
}

async function handleGitLabMergeRequest(payload: any) {
  const { object_attributes, project } = payload;
  
  if (!['opened', 'updated', 'reopen'].includes(object_attributes.action)) return;

  const branch = object_attributes.source_branch;
  const repositoryUrl = project.git_http_url;

  await triggerPipelinesForRepository(repositoryUrl, branch, {
    type: 'merge_request',
    action: object_attributes.action,
    merge_request: {
      iid: object_attributes.iid,
      title: object_attributes.title,
      source_branch: object_attributes.source_branch,
      target_branch: object_attributes.target_branch,
    },
    repository: project.path_with_namespace,
  });
}

async function handleGitLabTagPush(payload: any) {
  const { project, ref } = payload;
  
  if (!project || !ref) return;

  const tag = ref.replace('refs/tags/', '');
  const repositoryUrl = project.git_http_url;

  await triggerPipelinesForRepository(repositoryUrl, tag, {
    type: 'tag',
    tag,
    repository: project.path_with_namespace,
  });
}

// Bitbucket event handlers
async function handleBitbucketPush(payload: any) {
  const { repository, push } = payload;
  
  if (!repository || !push?.changes) return;

  for (const change of push.changes) {
    if (change.new) {
      const branch = change.new.name;
      const repositoryUrl = repository.links.clone.find((link: any) => link.name === 'https')?.href;

      if (repositoryUrl) {
        await triggerPipelinesForRepository(repositoryUrl, branch, {
          type: 'push',
          commits: change.commits,
          repository: repository.full_name,
        });
      }
    }
  }
}

async function handleBitbucketPullRequest(payload: any) {
  const { pullrequest, repository } = payload;
  
  if (!pullrequest || !repository) return;

  const branch = pullrequest.source.branch.name;
  const repositoryUrl = repository.links.clone.find((link: any) => link.name === 'https')?.href;

  if (repositoryUrl) {
    await triggerPipelinesForRepository(repositoryUrl, branch, {
      type: 'pull_request',
      pull_request: {
        id: pullrequest.id,
        title: pullrequest.title,
        source_branch: pullrequest.source.branch.name,
        destination_branch: pullrequest.destination.branch.name,
      },
      repository: repository.full_name,
    });
  }
}

// Common function to trigger pipelines for a repository
async function triggerPipelinesForRepository(repositoryUrl: string, branch: string, triggerData: any) {
  try {
    // Find projects with matching repository URL
    const projects = await prisma.project.findMany({
      where: {
        repositoryUrl,
        isActive: true,
      },
      include: {
        pipelines: {
          where: {
            isActive: true,
          },
          include: {
            triggers: {
              where: {
                isActive: true,
                type: 'WEBHOOK',
              },
            },
          },
        },
      },
    });

    for (const project of projects) {
      for (const pipeline of project.pipelines) {
        // Check if pipeline has webhook triggers
        const webhookTriggers = pipeline.triggers.filter(trigger => trigger.type === 'WEBHOOK');
        
        for (const trigger of webhookTriggers) {
          const triggerConfig = trigger.config as any;
          
          // Check if trigger matches the event
          if (shouldTriggerPipeline(triggerConfig, triggerData, branch)) {
            await createPipelineRun(pipeline.id, triggerData, branch);
          }
        }
      }
    }
  } catch (error) {
    logger.error('Error triggering pipelines for repository:', error);
  }
}

// Check if pipeline should be triggered based on trigger configuration
function shouldTriggerPipeline(triggerConfig: any, triggerData: any, branch: string): boolean {
  // Check branch filters
  if (triggerConfig.branches) {
    const branchPatterns = Array.isArray(triggerConfig.branches) ? triggerConfig.branches : [triggerConfig.branches];
    const branchMatches = branchPatterns.some((pattern: string) => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(branch);
      }
      return pattern === branch;
    });
    
    if (!branchMatches) return false;
  }

  // Check event types
  if (triggerConfig.events) {
    const allowedEvents = Array.isArray(triggerConfig.events) ? triggerConfig.events : [triggerConfig.events];
    if (!allowedEvents.includes(triggerData.type)) return false;
  }

  return true;
}

// Create and queue pipeline run
async function createPipelineRun(pipelineId: string, triggerData: any, branch: string) {
  try {
    // Get pipeline details
    const pipeline = await prisma.pipeline.findUnique({
      where: { id: pipelineId },
      include: { project: true },
    });

    if (!pipeline) return;

    // Get next run number
    const lastRun = await prisma.pipelineRun.findFirst({
      where: { pipelineId },
      orderBy: { number: 'desc' },
      select: { number: true },
    });

    const runNumber = (lastRun?.number || 0) + 1;

    // Create pipeline run
    const pipelineRun = await prisma.pipelineRun.create({
      data: {
        number: runNumber,
        pipelineId,
        status: 'PENDING',
        trigger: {
          type: 'WEBHOOK',
          data: triggerData,
          branch,
        },
      },
    });

    // Add to queue for processing
    await QueueService.addPipelineJob({
      id: pipelineRun.id,
      type: 'pipeline-run',
      payload: {
        pipelineRunId: pipelineRun.id,
        pipelineId,
        config: pipeline.config,
        trigger: triggerData,
      },
      userId: pipeline.userId,
      projectId: pipeline.projectId,
      pipelineId,
      runId: pipelineRun.id,
    });

    logger.info(`Pipeline run ${pipelineRun.id} created and queued for pipeline ${pipelineId}`);
  } catch (error) {
    logger.error('Error creating pipeline run:', error);
  }
}

export default router;
