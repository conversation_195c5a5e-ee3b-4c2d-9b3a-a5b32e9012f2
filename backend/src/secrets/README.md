# Secrets Management Engine

This module provides a secure way to manage secrets using HashiCorp Vault in the ChainOps backend.

## Features

- Secure storage and retrieval of secrets
- Support for Vault KV v2 engine
- TypeScript support with proper error handling
- Easy integration with existing services

## Setup

1. Install dependencies:
   ```bash
   npm install node-vault
   ```

2. Set up environment variables:
   ```bash
   export VAULT_ADDR='http://127.0.0.1:8200'
   export VAULT_TOKEN='your-root-token'
   ```

3. Start Vault server (see [Vault docs](https://developer.hashicorp.com/vault/docs))

## Usage

```typescript
import { SecretsManager } from './secrets';

// Initialize the secrets manager
const secretsManager = new SecretsManager(
    process.env.VAULT_ADDR!,
    process.env.VAULT_TOKEN!
);

// Write secrets
await secretsManager.writeSecrets('secret/data/myapp/config', {
    API_KEY: '12345',
    DATABASE_URL: 'postgresql://user:pass@localhost:5432/db'
});

// Fetch secrets
const secrets = await secretsManager.fetchSecrets('secret/data/myapp/config');
console.log(secrets);

// Delete secrets
await secretsManager.deleteSecrets('secret/data/myapp/config');
```

## Integration with Runner

The secrets management engine is designed to work seamlessly with the ChainOps runner:

1. Before executing a job/step, the runner fetches required secrets
2. Secrets are injected as environment variables or files into the container
3. After execution, sensitive data is cleaned up

## Security Best Practices

1. Always use environment variables for Vault configuration
2. Implement proper access control in Vault
3. Rotate secrets regularly
4. Use the principle of least privilege
5. Never log or expose secrets in plain text

## Error Handling

The module includes comprehensive error handling:
- Invalid paths
- Missing secrets
- Authentication failures
- Network issues

All errors are properly typed and include descriptive messages. 