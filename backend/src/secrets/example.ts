import { SecretsManager } from './secrets';

async function main() {
    // Get Vault configuration from environment variables
    const vaultAddr = process.env.VAULT_ADDR;
    const vaultToken = process.env.VAULT_TOKEN;

    if (!vaultAddr || !vaultToken) {
        console.error('Please set VAULT_ADDR and VAULT_TOKEN environment variables');
        process.exit(1);
    }

    try {
        // Initialize the secrets manager
        const secretsManager = new SecretsManager(vaultAddr, vaultToken);

        // Example: Write a secret
        const secretPath = 'secret/data/chainops/project1/build';
        await secretsManager.writeSecrets(secretPath, {
            API_KEY: '12345',
            DATABASE_URL: 'postgresql://user:pass@localhost:5432/db'
        });

        // Example: Fetch secrets
        const secrets = await secretsManager.fetchSecrets(secretPath);
        console.log('Fetched secrets:', secrets);

        // Example: Delete secrets (uncomment to use)
        // await secretsManager.deleteSecrets(secretPath);
    } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : 'Unknown error');
        process.exit(1);
    }
}

// Run the example
main().catch(console.error); 