import { Client } from 'node-vault';

export class SecretsManager {
    private client: Client;

    constructor(vaultAddr: string, vaultToken: string) {
        this.client = new Client({
            endpoint: vaultAddr,
            token: vaultToken,
        });
    }

    /**
     * Fetch secrets for a given path (e.g., "secret/data/project/job/step")
     * @param path The Vault path to fetch secrets from
     * @returns A promise that resolves to the secrets object
     */
    async fetchSecrets(path: string): Promise<Record<string, any>> {
        try {
            const secret = await this.client.read(path);
            if (!secret || !secret.data) {
                throw new Error(`No secrets found at ${path}`);
            }

            // For KV v2, secrets are under "data"
            if (secret.data.data) {
                return secret.data.data;
            }
            return secret.data;
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to fetch secrets from ${path}: ${errorMessage}`);
        }
    }

    /**
     * Write secrets to a given path
     * @param path The Vault path to write secrets to
     * @param data The secrets data to write
     */
    async writeSecrets(path: string, data: Record<string, any>): Promise<void> {
        try {
            await this.client.write(path, { data });
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to write secrets to ${path}: ${errorMessage}`);
        }
    }

    /**
     * Delete secrets at a given path
     * @param path The Vault path to delete secrets from
     */
    async deleteSecrets(path: string): Promise<void> {
        try {
            await this.client.delete(path);
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to delete secrets at ${path}: ${errorMessage}`);
        }
    }
} 