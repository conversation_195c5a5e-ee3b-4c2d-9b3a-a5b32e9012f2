import { MinioService } from '../minio';

// Mock Minio Client
const mockMinioClient = {
  bucketExists: jest.fn(),
  makeBucket: jest.fn(),
  putObject: jest.fn(),
  getObject: jest.fn(),
  removeObject: jest.fn(),
  listObjects: jest.fn(),
  statObject: jest.fn(),
  presignedGetObject: jest.fn(),
  presignedPutObject: jest.fn(),
};

jest.mock('minio', () => ({
  Client: jest.fn(() => mockMinioClient),
}));

describe('MinioService', () => {
  beforeEach(() => {
    (MinioService as any).instance = null;
    jest.clearAllMocks();
  });

  describe('Core Methods', () => {
    it('should get singleton instance', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize when bucket exists', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);

      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).not.toHaveBeenCalled();
    });

    it('should initialize when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockResolvedValue(undefined);

      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Bucket check failed'));

      await expect(MinioService.initialize()).rejects.toThrow('Bucket check failed');
    });

    it('should handle bucket creation errors', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockRejectedValue(new Error('Bucket creation failed'));

      await expect(MinioService.initialize()).rejects.toThrow('Bucket creation failed');
    });

    it('should perform health check successfully', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);

      const result = await MinioService.healthCheck();
      expect(result).toBe(true);
    });

    it('should handle health check errors', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Health check failed'));

      const result = await MinioService.healthCheck();
      expect(result).toBe(false);
    });

    it('should return false when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);

      const result = await MinioService.healthCheck();
      expect(result).toBe(false);
    });
  });

  describe('File Operations', () => {
    it('should upload file successfully', async () => {
      const mockEtag = 'test-etag-123';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });

      const buffer = Buffer.from('test content');
      const result = await MinioService.uploadFile('test.txt', buffer);

      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer
      );
    });

    it('should upload file with metadata', async () => {
      const mockEtag = 'test-etag-456';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });

      const buffer = Buffer.from('test content');
      const metadata = { 'Content-Type': 'text/plain' };
      const result = await MinioService.uploadFile('test.txt', buffer, metadata);

      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer,
        metadata
      );
    });

    it('should handle upload errors', async () => {
      mockMinioClient.putObject.mockRejectedValue(new Error('Upload failed'));

      const buffer = Buffer.from('test content');
      await expect(MinioService.uploadFile('test.txt', buffer)).rejects.toThrow('Upload failed');
    });

    it('should download file successfully', async () => {
      const mockStream = { pipe: jest.fn(), on: jest.fn() };
      mockMinioClient.getObject.mockResolvedValue(mockStream);

      const result = await MinioService.downloadFile('test.txt');
      expect(result).toBe(mockStream);
    });

    it('should handle download errors', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('Download failed'));

      await expect(MinioService.downloadFile('test.txt')).rejects.toThrow('Download failed');
    });

    it('should delete file successfully', async () => {
      mockMinioClient.removeObject.mockResolvedValue(undefined);

      await MinioService.deleteFile('test.txt');
      expect(mockMinioClient.removeObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt'
      );
    });

    it('should handle delete errors', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('Delete failed'));

      await expect(MinioService.deleteFile('test.txt')).rejects.toThrow('Delete failed');
    });

    it('should get file info successfully', async () => {
      const mockStat = {
        size: 1024,
        lastModified: new Date(),
        etag: 'test-etag',
        contentType: 'text/plain',
      };
      mockMinioClient.statObject.mockResolvedValue(mockStat);

      const result = await MinioService.getFileInfo('test.txt');
      expect(result).toBe(mockStat);
    });

    it('should handle file info errors', async () => {
      mockMinioClient.statObject.mockRejectedValue(new Error('Stat failed'));

      await expect(MinioService.getFileInfo('test.txt')).rejects.toThrow('Stat failed');
    });
  });

  describe('List Operations', () => {
    it('should list files successfully', async () => {
      const mockObjects = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 },
      ];

      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            mockObjects.forEach(obj => callback(obj));
          } else if (event === 'end') {
            callback();
          }
        }),
      };

      mockMinioClient.listObjects.mockReturnValue(mockStream);

      const result = await MinioService.listFiles();
      expect(result).toEqual(mockObjects);
    });

    it('should list files with prefix', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') callback();
        }),
      };

      mockMinioClient.listObjects.mockReturnValue(mockStream);

      await MinioService.listFiles('logs/');
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        'logs/',
        true
      );
    });

    it('should handle list errors', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('List failed'));
          }
        }),
      };

      mockMinioClient.listObjects.mockReturnValue(mockStream);

      await expect(MinioService.listFiles()).rejects.toThrow('List failed');
    });
  });
});