import { QueueService } from '../queue';

// Mock Redis
const mockRedisClient = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  ping: jest.fn(),
  lPush: jest.fn(),
  rPop: jest.fn(),
  lLen: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  isReady: true,
};

jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient),
}));

describe('QueueService', () => {
  beforeEach(() => {
    (QueueService as any).instance = null;
    jest.clearAllMocks();
  });

  describe('Core Methods', () => {
    it('should get singleton instance', () => {
      const instance1 = QueueService.getInstance();
      const instance2 = QueueService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize successfully', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      await QueueService.initialize();
      expect(mockRedisClient.connect).toHaveBeenCalled();
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should handle initialization errors', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('Connection failed'));
      await expect(QueueService.initialize()).rejects.toThrow('Connection failed');
    });

    it('should close successfully', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      await QueueService.close();
      expect(mockRedisClient.disconnect).toHaveBeenCalled();
      expect(mockRedisClient.off).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should handle close errors', async () => {
      mockRedisClient.disconnect.mockRejectedValue(new Error('Disconnect failed'));
      await expect(QueueService.close()).rejects.toThrow('Disconnect failed');
    });

    it('should perform health check successfully', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');
      const result = await QueueService.healthCheck();
      expect(result).toBe(true);
    });

    it('should handle health check errors', async () => {
      mockRedisClient.ping.mockRejectedValue(new Error('Ping failed'));
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
    });

    it('should return false for wrong ping response', async () => {
      mockRedisClient.ping.mockResolvedValue('WRONG');
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
    });

    it('should return false when client not ready', async () => {
      mockRedisClient.isReady = false;
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
      mockRedisClient.isReady = true;
    });
  });

  describe('Queue Operations', () => {
    it('should add job successfully', async () => {
      mockRedisClient.lPush.mockResolvedValue(1);
      const job = { id: 'test-job', type: 'test', data: { test: 'data' } };

      await QueueService.addJob('test-queue', job);
      expect(mockRedisClient.lPush).toHaveBeenCalledWith('test-queue', JSON.stringify(job));
    });

    it('should handle add job errors', async () => {
      mockRedisClient.lPush.mockRejectedValue(new Error('Push failed'));
      const job = { id: 'test-job', type: 'test' };

      await expect(QueueService.addJob('test-queue', job)).rejects.toThrow('Push failed');
    });

    it('should get job successfully', async () => {
      const job = { id: 'test-job', type: 'test' };
      mockRedisClient.rPop.mockResolvedValue(JSON.stringify(job));

      const result = await QueueService.getJob('test-queue');
      expect(result).toEqual(job);
    });

    it('should return null when queue is empty', async () => {
      mockRedisClient.rPop.mockResolvedValue(null);

      const result = await QueueService.getJob('empty-queue');
      expect(result).toBeNull();
    });

    it('should handle get job errors', async () => {
      mockRedisClient.rPop.mockRejectedValue(new Error('Pop failed'));

      await expect(QueueService.getJob('test-queue')).rejects.toThrow('Pop failed');
    });

    it('should handle invalid JSON', async () => {
      mockRedisClient.rPop.mockResolvedValue('invalid-json');

      await expect(QueueService.getJob('test-queue')).rejects.toThrow();
    });

    it('should get queue length', async () => {
      mockRedisClient.lLen.mockResolvedValue(5);

      const result = await QueueService.getQueueLength('test-queue');
      expect(result).toBe(5);
    });

    it('should handle get length errors', async () => {
      mockRedisClient.lLen.mockRejectedValue(new Error('Length failed'));

      await expect(QueueService.getQueueLength('test-queue')).rejects.toThrow('Length failed');
    });

    it('should clear queue', async () => {
      mockRedisClient.del.mockResolvedValue(1);

      await QueueService.clearQueue('test-queue');
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-queue');
    });

    it('should handle clear queue errors', async () => {
      mockRedisClient.del.mockRejectedValue(new Error('Delete failed'));

      await expect(QueueService.clearQueue('test-queue')).rejects.toThrow('Delete failed');
    });

    it('should check if queue exists', async () => {
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await QueueService.queueExists('test-queue');
      expect(result).toBe(true);
    });

    it('should return false for non-existent queue', async () => {
      mockRedisClient.exists.mockResolvedValue(0);

      const result = await QueueService.queueExists('non-existent');
      expect(result).toBe(false);
    });

    it('should handle exists check errors', async () => {
      mockRedisClient.exists.mockRejectedValue(new Error('Exists failed'));

      await expect(QueueService.queueExists('test-queue')).rejects.toThrow('Exists failed');
    });
  });
});