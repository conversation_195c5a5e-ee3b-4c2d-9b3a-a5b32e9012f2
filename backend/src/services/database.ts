import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'info' },
        { emit: 'event', level: 'warn' },
      ],
    });

    // Log database queries in development
    if (process.env.NODE_ENV === 'development') {
      // @ts-ignore
      this.prisma.$on('query', (e: any) => {
        logger.debug(`Query: ${e.query}`);
        logger.debug(`Params: ${e.params}`);
        logger.debug(`Duration: ${e.duration}ms`);
      });
    }

    // @ts-ignore
    this.prisma.$on('error', (e: any) => {
      logger.error('Database error:', e);
    });

    // @ts-ignore
    this.prisma.$on('info', (e: any) => {
      logger.info('Database info:', e.message);
    });

    // @ts-ignore
    this.prisma.$on('warn', (e: any) => {
      logger.warn('Database warning:', e.message);
    });
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = DatabaseService.getInstance();
    try {
      await instance.prisma.$connect();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Failed to connect to database:', error);
      throw error;
    }
  }

  public static async close(): Promise<void> {
    const instance = DatabaseService.getInstance();
    try {
      await instance.prisma.$disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from database:', error);
      throw error;
    }
  }

  public static getClient(): PrismaClient {
    return DatabaseService.getInstance().prisma;
  }

  public static async healthCheck(): Promise<boolean> {
    try {
      const instance = DatabaseService.getInstance();
      await instance.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  public static async runMigrations(): Promise<void> {
    try {
      const instance = DatabaseService.getInstance();
      // Note: In production, migrations should be run separately
      // This is just for development convenience
      if (process.env.NODE_ENV === 'development') {
        logger.info('Running database migrations...');
        // Migrations would be run via CLI: npx prisma migrate deploy
      }
    } catch (error) {
      logger.error('Failed to run migrations:', error);
      throw error;
    }
  }
}

export { DatabaseService };
export const prisma = DatabaseService.getClient();
