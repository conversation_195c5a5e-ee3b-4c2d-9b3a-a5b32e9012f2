import * as Minio from 'minio';
import { logger } from '../utils/logger';

class MinioService {
  private static instance: MinioService;
  private client: Minio.Client;
  private bucketName: string;

  private constructor() {
    this.bucketName = process.env.MINIO_BUCKET || 'chainops-artifacts';
    
    this.client = new Minio.Client({
      endPoint: process.env.MINIO_ENDPOINT?.split(':')[0] || 'localhost',
      port: parseInt(process.env.MINIO_ENDPOINT?.split(':')[1] || '9000'),
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: process.env.MINIO_ACCESS_KEY || 'chainops',
      secretKey: process.env.MINIO_SECRET_KEY || 'chainops_password',
    });
  }

  public static getInstance(): MinioService {
    if (!MinioService.instance) {
      MinioService.instance = new MinioService();
    }
    return MinioService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = MinioService.getInstance();
    try {
      // Check if bucket exists, create if not
      const bucketExists = await instance.client.bucketExists(instance.bucketName);
      
      if (!bucketExists) {
        await instance.client.makeBucket(instance.bucketName, 'us-east-1');
        logger.info(`Created MinIO bucket: ${instance.bucketName}`);
      }

      // Set bucket policy for public read access to artifacts
      const policy = {
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${instance.bucketName}/public/*`],
          },
        ],
      };

      await instance.client.setBucketPolicy(instance.bucketName, JSON.stringify(policy));
      logger.info('MinIO service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize MinIO service:', error);
      throw error;
    }
  }

  public static async uploadFile(
    objectName: string,
    filePath: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    const instance = MinioService.getInstance();
    try {
      await instance.client.fPutObject(instance.bucketName, objectName, filePath, metadata);
      logger.info(`File uploaded successfully: ${objectName}`);
      return objectName;
    } catch (error) {
      logger.error(`Failed to upload file ${objectName}:`, error);
      throw error;
    }
  }

  public static async uploadBuffer(
    objectName: string,
    buffer: Buffer,
    size: number,
    metadata?: Record<string, string>
  ): Promise<string> {
    const instance = MinioService.getInstance();
    try {
      await instance.client.putObject(instance.bucketName, objectName, buffer, size, metadata);
      logger.info(`Buffer uploaded successfully: ${objectName}`);
      return objectName;
    } catch (error) {
      logger.error(`Failed to upload buffer ${objectName}:`, error);
      throw error;
    }
  }

  public static async downloadFile(objectName: string): Promise<Buffer> {
    const instance = MinioService.getInstance();
    try {
      const stream = await instance.client.getObject(instance.bucketName, objectName);
      const chunks: Buffer[] = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error(`Failed to download file ${objectName}:`, error);
      throw error;
    }
  }

  public static async getFileUrl(objectName: string, expiry: number = 7 * 24 * 60 * 60): Promise<string> {
    const instance = MinioService.getInstance();
    try {
      return await instance.client.presignedGetObject(instance.bucketName, objectName, expiry);
    } catch (error) {
      logger.error(`Failed to get file URL for ${objectName}:`, error);
      throw error;
    }
  }

  public static async deleteFile(objectName: string): Promise<void> {
    const instance = MinioService.getInstance();
    try {
      await instance.client.removeObject(instance.bucketName, objectName);
      logger.info(`File deleted successfully: ${objectName}`);
    } catch (error) {
      logger.error(`Failed to delete file ${objectName}:`, error);
      throw error;
    }
  }

  public static async listFiles(prefix?: string): Promise<Minio.BucketItem[]> {
    const instance = MinioService.getInstance();
    try {
      const objects: Minio.BucketItem[] = [];
      const stream = instance.client.listObjects(instance.bucketName, prefix, true);
      
      return new Promise((resolve, reject) => {
        stream.on('data', (obj) => objects.push(obj));
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error('Failed to list files:', error);
      throw error;
    }
  }

  public static async getFileInfo(objectName: string): Promise<Minio.BucketItemStat> {
    const instance = MinioService.getInstance();
    try {
      return await instance.client.statObject(instance.bucketName, objectName);
    } catch (error) {
      logger.error(`Failed to get file info for ${objectName}:`, error);
      throw error;
    }
  }

  public static async healthCheck(): Promise<boolean> {
    try {
      const instance = MinioService.getInstance();
      await instance.client.bucketExists(instance.bucketName);
      return true;
    } catch (error) {
      logger.error('MinIO health check failed:', error);
      return false;
    }
  }

  // Helper methods for common artifact operations
  public static async uploadJobArtifact(
    jobId: string,
    fileName: string,
    buffer: Buffer,
    metadata?: Record<string, string>
  ): Promise<string> {
    const objectName = `jobs/${jobId}/artifacts/${fileName}`;
    return this.uploadBuffer(objectName, buffer, buffer.length, metadata);
  }

  public static async uploadJobLog(jobId: string, logContent: string): Promise<string> {
    const objectName = `jobs/${jobId}/logs/output.log`;
    const buffer = Buffer.from(logContent, 'utf8');
    return this.uploadBuffer(objectName, buffer, buffer.length, {
      'Content-Type': 'text/plain',
    });
  }

  public static async getJobArtifacts(jobId: string): Promise<Minio.BucketItem[]> {
    return this.listFiles(`jobs/${jobId}/artifacts/`);
  }

  public static async getJobLogUrl(jobId: string): Promise<string> {
    return this.getFileUrl(`jobs/${jobId}/logs/output.log`);
  }

  public static getBucketName(): string {
    return MinioService.getInstance().bucketName;
  }

  public static getClient(): Minio.Client {
    return MinioService.getInstance().client;
  }
}

export { MinioService };
