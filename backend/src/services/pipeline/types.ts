// Pipeline Engine Types
export interface PipelineConfig {
  version?: string;
  name?: string;
  on?: TriggerConfig;
  env?: Record<string, string>;
  defaults?: {
    run?: {
      shell?: string;
      'working-directory'?: string;
    };
  };
  jobs: Record<string, JobConfig>;
  workflows?: Record<string, WorkflowConfig>;
}

export interface TriggerConfig {
  push?: {
    branches?: string[];
    tags?: string[];
    paths?: string[];
    'paths-ignore'?: string[];
  };
  pull_request?: {
    branches?: string[];
    types?: string[];
    paths?: string[];
    'paths-ignore'?: string[];
  };
  schedule?: Array<{
    cron: string;
  }>;
  workflow_dispatch?: {
    inputs?: Record<string, WorkflowInput>;
  };
  webhook?: {
    types?: string[];
  };
}

export interface WorkflowInput {
  description?: string;
  required?: boolean;
  default?: string;
  type?: 'string' | 'number' | 'boolean' | 'choice';
  options?: string[];
}

export interface JobConfig {
  name?: string;
  'runs-on'?: string | string[];
  needs?: string | string[];
  if?: string;
  environment?: string | EnvironmentConfig;
  'timeout-minutes'?: number;
  'continue-on-error'?: boolean;
  strategy?: StrategyConfig;
  env?: Record<string, string>;
  defaults?: {
    run?: {
      shell?: string;
      'working-directory'?: string;
    };
  };
  steps: StepConfig[];
  services?: Record<string, ServiceConfig>;
  container?: ContainerConfig;
}

export interface EnvironmentConfig {
  name: string;
  url?: string;
}

export interface StrategyConfig {
  matrix?: MatrixConfig;
  'fail-fast'?: boolean;
  'max-parallel'?: number;
}

export interface MatrixConfig {
  [key: string]: any[] | { include?: any[]; exclude?: any[] };
}

export interface StepConfig {
  id?: string;
  name?: string;
  uses?: string;
  run?: string;
  with?: Record<string, any>;
  env?: Record<string, string>;
  if?: string;
  'continue-on-error'?: boolean;
  'timeout-minutes'?: number;
  shell?: string;
  'working-directory'?: string;
}

export interface ServiceConfig {
  image: string;
  env?: Record<string, string>;
  ports?: string[];
  volumes?: string[];
  options?: string;
}

export interface ContainerConfig {
  image: string;
  env?: Record<string, string>;
  ports?: string[];
  volumes?: string[];
  options?: string;
  credentials?: {
    username?: string;
    password?: string;
  };
}

export interface WorkflowConfig {
  name?: string;
  on: TriggerConfig;
  env?: Record<string, string>;
  defaults?: {
    run?: {
      shell?: string;
      'working-directory'?: string;
    };
  };
  jobs: Record<string, JobConfig>;
}

// DAG Types
export interface DAGNode {
  id: string;
  jobName: string;
  config: JobConfig;
  dependencies: string[];
  dependents: string[];
  level: number;
  matrixJobs?: MatrixJob[];
}

export interface MatrixJob {
  id: string;
  jobName: string;
  config: JobConfig;
  matrixValues: Record<string, any>;
  dependencies: string[];
}

export interface DAG {
  nodes: Map<string, DAGNode>;
  edges: Map<string, string[]>;
  levels: string[][];
  hasMatrix: boolean;
}

// Execution Types
export interface ExecutionContext {
  pipelineRunId: string;
  pipelineId: string;
  variables: Record<string, any>;
  secrets: Record<string, string>;
  github?: {
    event: any;
    ref: string;
    sha: string;
    repository: string;
    actor: string;
  };
  runner?: {
    os: string;
    arch: string;
    temp: string;
    tool_cache: string;
  };
}

export interface JobExecution {
  id: string;
  jobId: string;
  jobName: string;
  config: JobConfig;
  context: ExecutionContext;
  matrixValues?: Record<string, any>;
  status: 'pending' | 'queued' | 'running' | 'success' | 'failure' | 'cancelled' | 'skipped';
  startedAt?: Date;
  finishedAt?: Date;
  logs: string[];
  artifacts: string[];
  outputs: Record<string, string>;
}

// Validation Types
export interface ValidationError {
  path: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Expression Types
export interface ExpressionContext {
  github?: any;
  env?: Record<string, string>;
  vars?: Record<string, any>;
  secrets?: Record<string, string>;
  strategy?: {
    matrix?: Record<string, any>;
  };
  matrix?: Record<string, any>;
  needs?: Record<string, any>;
  runner?: any;
  job?: any;
  steps?: any;
}

// Runner Types
export interface RunnerRequirements {
  os?: string;
  arch?: string;
  labels?: string[];
  'self-hosted'?: boolean;
}

export interface RunnerCapabilities {
  os: string;
  arch: string;
  labels: string[];
  tools: string[];
  containers: boolean;
  services: boolean;
}

// Pipeline Processing Result
export interface PipelineProcessingResult {
  dag: DAG;
  jobs: JobExecution[];
  validation: ValidationResult;
  estimatedDuration?: number;
  resourceRequirements?: {
    cpu: number;
    memory: number;
    storage: number;
  };
}
