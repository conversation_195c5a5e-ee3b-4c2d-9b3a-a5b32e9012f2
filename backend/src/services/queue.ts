import Bull, { Queue, Job } from 'bull';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface JobData {
  id: string;
  type: string;
  payload: any;
  userId: string;
  projectId?: string;
  pipelineId?: string;
  runId?: string;
}

class QueueService {
  private static instance: QueueService;
  private redis: Redis;
  private pipelineQueue: Queue;
  private jobQueue: Queue;
  private notificationQueue: Queue;

  private constructor() {
    // Initialize Redis connection
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      retryStrategy: (times) => Math.min(times * 100, 3000),
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });

    // Initialize queues
    this.pipelineQueue = new Bull('pipeline-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.jobQueue = new Bull('job-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.notificationQueue = new Bull('notification-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
      },
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 5000,
        },
      },
    });

    this.setupEventHandlers();
  }

  private getRedisHost(): string {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.hostname;
  }

  private getRedisPort(): number {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return parseInt(url.port) || 6379;
  }

  private getRedisPassword(): string | undefined {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.password || undefined;
  }

  private setupEventHandlers(): void {
    // Pipeline queue events
    this.pipelineQueue.on('completed', (job: Job) => {
      logger.info(`Pipeline job ${job.id} completed`);
    });

    this.pipelineQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Pipeline job ${job.id} failed:`, err);
    });

    // Job queue events
    this.jobQueue.on('completed', (job: Job) => {
      logger.info(`Job ${job.id} completed`);
    });

    this.jobQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Job ${job.id} failed:`, err);
    });

    // Notification queue events
    this.notificationQueue.on('completed', (job: Job) => {
      logger.info(`Notification job ${job.id} completed`);
    });

    this.notificationQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Notification job ${job.id} failed:`, err);
    });
  }

  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = QueueService.getInstance();
    try {
      await instance.redis.ping();
      logger.info('Queue service connected to Redis');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  public static async close(): Promise<void> {
    const instance = QueueService.getInstance();
    try {
      await instance.pipelineQueue.close();
      await instance.jobQueue.close();
      await instance.notificationQueue.close();
      await instance.redis.quit();
      logger.info('Queue service disconnected');
    } catch (error) {
      logger.error('Error closing queue service:', error);
      throw error;
    }
  }

  // Pipeline queue methods
  public static async addPipelineJob(data: JobData, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.pipelineQueue.add('execute-pipeline', data, options);
  }

  public static async addJob(data: JobData, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.jobQueue.add('execute-job', data, options);
  }

  public static async addNotification(data: any, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.notificationQueue.add('send-notification', data, options);
  }

  // Queue status methods
  public static async getPipelineQueueStats(): Promise<any> {
    const instance = QueueService.getInstance();
    return {
      waiting: await instance.pipelineQueue.getWaiting(),
      active: await instance.pipelineQueue.getActive(),
      completed: await instance.pipelineQueue.getCompleted(),
      failed: await instance.pipelineQueue.getFailed(),
    };
  }

  public static async getJobQueueStats(): Promise<any> {
    const instance = QueueService.getInstance();
    return {
      waiting: await instance.jobQueue.getWaiting(),
      active: await instance.jobQueue.getActive(),
      completed: await instance.jobQueue.getCompleted(),
      failed: await instance.jobQueue.getFailed(),
    };
  }

  // Health check
  public static async healthCheck(): Promise<boolean> {
    try {
      const instance = QueueService.getInstance();
      await instance.redis.ping();
      return true;
    } catch (error) {
      logger.error('Queue service health check failed:', error);
      return false;
    }
  }

  // Get queue instances for processors
  public static getPipelineQueue(): Queue {
    return QueueService.getInstance().pipelineQueue;
  }

  public static getJobQueue(): Queue {
    return QueueService.getInstance().jobQueue;
  }

  public static getNotificationQueue(): Queue {
    return QueueService.getInstance().notificationQueue;
  }
}

export { QueueService };
