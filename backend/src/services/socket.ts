import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import { prisma } from './database';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    email: string;
    username: string;
    role: string;
  };
}

class SocketService {
  private static instance: SocketService;
  private io: SocketIOServer;
  private connectedUsers: Map<string, string[]> = new Map(); // userId -> socketIds

  private constructor(io: SocketIOServer) {
    this.io = io;
    this.setupMiddleware();
    this.setupEventHandlers();
  }

  public static initialize(io: SocketIOServer): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService(io);
    }
    return SocketService.instance;
  }

  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      throw new Error('SocketService not initialized');
    }
    return SocketService.instance;
  }

  private setupMiddleware(): void {
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            email: true,
            username: true,
            role: true,
            isActive: true,
          },
        });

        if (!user || !user.isActive) {
          return next(new Error('User not found or inactive'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      logger.info(`User ${socket.user?.username} connected via WebSocket`);

      // Track connected user
      if (socket.userId) {
        const userSockets = this.connectedUsers.get(socket.userId) || [];
        userSockets.push(socket.id);
        this.connectedUsers.set(socket.userId, userSockets);

        // Join user-specific room
        socket.join(`user:${socket.userId}`);
      }

      // Handle joining project rooms
      socket.on('join-project', (projectId: string) => {
        socket.join(`project:${projectId}`);
        logger.debug(`User ${socket.user?.username} joined project room: ${projectId}`);
      });

      // Handle leaving project rooms
      socket.on('leave-project', (projectId: string) => {
        socket.leave(`project:${projectId}`);
        logger.debug(`User ${socket.user?.username} left project room: ${projectId}`);
      });

      // Handle joining pipeline rooms
      socket.on('join-pipeline', (pipelineId: string) => {
        socket.join(`pipeline:${pipelineId}`);
        logger.debug(`User ${socket.user?.username} joined pipeline room: ${pipelineId}`);
      });

      // Handle leaving pipeline rooms
      socket.on('leave-pipeline', (pipelineId: string) => {
        socket.leave(`pipeline:${pipelineId}`);
        logger.debug(`User ${socket.user?.username} left pipeline room: ${pipelineId}`);
      });

      // Handle joining job rooms for real-time logs
      socket.on('join-job', (jobId: string) => {
        socket.join(`job:${jobId}`);
        logger.debug(`User ${socket.user?.username} joined job room: ${jobId}`);
      });

      // Handle leaving job rooms
      socket.on('leave-job', (jobId: string) => {
        socket.leave(`job:${jobId}`);
        logger.debug(`User ${socket.user?.username} left job room: ${jobId}`);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        logger.info(`User ${socket.user?.username} disconnected from WebSocket`);

        // Remove from connected users tracking
        if (socket.userId) {
          const userSockets = this.connectedUsers.get(socket.userId) || [];
          const updatedSockets = userSockets.filter(id => id !== socket.id);
          
          if (updatedSockets.length === 0) {
            this.connectedUsers.delete(socket.userId);
          } else {
            this.connectedUsers.set(socket.userId, updatedSockets);
          }
        }
      });
    });
  }

  // Emit events to specific users
  public emitToUser(userId: string, event: string, data: any): void {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  // Emit events to project members
  public emitToProject(projectId: string, event: string, data: any): void {
    this.io.to(`project:${projectId}`).emit(event, data);
  }

  // Emit events to pipeline watchers
  public emitToPipeline(pipelineId: string, event: string, data: any): void {
    this.io.to(`pipeline:${pipelineId}`).emit(event, data);
  }

  // Emit events to job watchers
  public emitToJob(jobId: string, event: string, data: any): void {
    this.io.to(`job:${jobId}`).emit(event, data);
  }

  // Emit to all connected clients
  public emitToAll(event: string, data: any): void {
    this.io.emit(event, data);
  }

  // Get connected users count
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Check if user is connected
  public isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  // Get all connected users
  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  // Pipeline status updates
  public emitPipelineStatusUpdate(pipelineId: string, status: string, data?: any): void {
    this.emitToPipeline(pipelineId, 'pipeline:status', {
      pipelineId,
      status,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }

  // Job status updates
  public emitJobStatusUpdate(jobId: string, status: string, data?: any): void {
    this.emitToJob(jobId, 'job:status', {
      jobId,
      status,
      timestamp: new Date().toISOString(),
      ...data,
    });
  }

  // Job log streaming
  public emitJobLog(jobId: string, log: string): void {
    this.emitToJob(jobId, 'job:log', {
      jobId,
      log,
      timestamp: new Date().toISOString(),
    });
  }

  // Notification events
  public emitNotification(userId: string, notification: any): void {
    this.emitToUser(userId, 'notification', notification);
  }

  // Job log subscription management
  private jobLogSubscriptions: Map<string, Set<Function>> = new Map();

  public subscribeToJobLogs(jobId: string, callback: Function): void {
    if (!this.jobLogSubscriptions.has(jobId)) {
      this.jobLogSubscriptions.set(jobId, new Set());
    }
    this.jobLogSubscriptions.get(jobId)!.add(callback);
  }

  public unsubscribeFromJobLogs(jobId: string, callback: Function): void {
    const subscribers = this.jobLogSubscriptions.get(jobId);
    if (subscribers) {
      subscribers.delete(callback);
      if (subscribers.size === 0) {
        this.jobLogSubscriptions.delete(jobId);
      }
    }
  }

  public notifyJobLogSubscribers(jobId: string, data: any): void {
    const subscribers = this.jobLogSubscriptions.get(jobId);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logger.error('Error in job log subscriber callback:', error);
        }
      });
    }
  }

  // Enhanced job log streaming with buffering
  public emitJobLogBatch(jobId: string, logs: string[]): void {
    this.emitToJob(jobId, 'job:log-batch', {
      jobId,
      logs,
      timestamp: new Date().toISOString(),
    });

    // Also notify SSE subscribers
    logs.forEach(log => {
      this.notifyJobLogSubscribers(jobId, {
        jobId,
        type: 'log',
        message: log,
      });
    });
  }

  // Enhanced job status update with SSE support
  public emitJobStatusUpdateWithSSE(jobId: string, status: string, data?: any): void {
    const statusData = {
      jobId,
      status,
      timestamp: new Date().toISOString(),
      ...data,
    };

    // Emit to WebSocket subscribers
    this.emitToJob(jobId, 'job:status', statusData);

    // Notify SSE subscribers
    this.notifyJobLogSubscribers(jobId, {
      jobId,
      type: 'status',
      status,
      ...data,
    });
  }
}

export { SocketService };
