// Simple starter script for the backend
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting ChainOps Backend...');
console.log('Current directory:', process.cwd());

// Start the TypeScript file with tsx
const child = spawn('npx', ['tsx', 'src/index.ts'], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('❌ Failed to start backend:', error);
});

child.on('close', (code) => {
  console.log(`Backend process exited with code ${code}`);
});
