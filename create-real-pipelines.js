#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:8081';

// Demo credentials from seed
const DEMO_EMAIL = '<EMAIL>';
const DEMO_PASSWORD = 'demo123';

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${API_BASE}/auth/login`, {
      login: DEMO_EMAIL,
      password: DEMO_PASSWORD
    });
    
    if (response.data.success && response.data.data.token) {
      authToken = response.data.data.token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.log('❌ Login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.response?.data || error.message);
    return false;
  }
}

async function createPipeline(pipelineData) {
  try {
    const response = await axios.post(`${API_BASE}/api/pipelines`, pipelineData, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log(`✅ Created pipeline: ${pipelineData.name}`);
      return response.data.data;
    } else {
      console.log(`❌ Failed to create pipeline: ${pipelineData.name}`, response.data);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error creating pipeline: ${pipelineData.name}`, error.response?.data || error.message);
    return null;
  }
}

async function createJob(pipelineId, jobData) {
  try {
    const response = await axios.post(`${API_BASE}/api/jobs`, {
      ...jobData,
      pipelineId
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log(`✅ Created job: ${jobData.name}`);
      return response.data.data;
    } else {
      console.log(`❌ Failed to create job: ${jobData.name}`, response.data);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error creating job: ${jobData.name}`, error.response?.data || error.message);
    return null;
  }
}

async function main() {
  console.log('🚀 Creating real pipelines for ChainOps...\n');
  
  // Login first
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    process.exit(1);
  }
  
  // Real pipeline configurations
  const pipelines = [
    {
      name: 'React Frontend CI/CD',
      slug: 'react-frontend-cicd',
      description: 'Build, test, and deploy React application to production',
      projectId: 'cmbkrevcm0006e0q32rn0tjio', // Web Application project
      config: {
        version: '1.0',
        on: {
          push: { branches: ['main'] },
          pull_request: { branches: ['main'] }
        },
        env: {
          NODE_ENV: 'production',
          REACT_APP_API_URL: 'https://api.company.com'
        },
        jobs: {
          build: {
            'runs-on': 'ubuntu-latest',
            steps: [
              { name: 'Checkout code', uses: 'actions/checkout@v3' },
              { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
              { name: 'Install dependencies', run: 'npm ci' },
              { name: 'Run linter', run: 'npm run lint' },
              { name: 'Run tests', run: 'npm test -- --coverage' },
              { name: 'Build application', run: 'npm run build' },
              { name: 'Deploy to Vercel', run: 'npm run deploy' }
            ]
          }
        }
      }
    },
    {
      name: 'Node.js API Backend',
      slug: 'nodejs-api-backend',
      description: 'Test, build, and deploy Node.js API server',
      projectId: 'cmbkrevct000ee0q3kxlva55u', // API Server project
      config: {
        version: '1.0',
        on: {
          push: { branches: ['main', 'develop'] }
        },
        env: {
          NODE_ENV: 'production',
          DATABASE_URL: 'postgresql://prod-db:5432/api'
        },
        jobs: {
          test: {
            'runs-on': 'ubuntu-latest',
            steps: [
              { name: 'Checkout code', uses: 'actions/checkout@v3' },
              { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
              { name: 'Install dependencies', run: 'npm ci' },
              { name: 'Run tests', run: 'npm test' },
              { name: 'Build application', run: 'npm run build' }
            ]
          },
          deploy: {
            'runs-on': 'ubuntu-latest',
            needs: ['test'],
            steps: [
              { name: 'Build Docker image', run: 'docker build -t api-server .' },
              { name: 'Deploy to Kubernetes', run: 'kubectl apply -f k8s/' }
            ]
          }
        }
      }
    },
    {
      name: 'Mobile App Build',
      slug: 'mobile-app-build',
      description: 'Build and distribute mobile application',
      projectId: 'cmbkrevcm0006e0q32rn0tjio', // Web Application project
      config: {
        version: '1.0',
        on: {
          push: { branches: ['main'] },
          workflow_dispatch: {}
        },
        env: {
          EXPO_TOKEN: 'expo-token-here',
          APP_ENV: 'production'
        },
        jobs: {
          build: {
            'runs-on': 'macos-latest',
            steps: [
              { name: 'Checkout code', uses: 'actions/checkout@v3' },
              { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
              { name: 'Install dependencies', run: 'npm ci' },
              { name: 'Run tests', run: 'npm test' },
              { name: 'Build iOS', run: 'expo build:ios' },
              { name: 'Build Android', run: 'expo build:android' },
              { name: 'Upload to App Store', run: 'fastlane ios release' }
            ]
          }
        }
      }
    },
    {
      name: 'Documentation Site',
      slug: 'documentation-site',
      description: 'Build and deploy documentation website',
      projectId: 'cmbkrevcm0006e0q32rn0tjio', // Web Application project
      config: {
        version: '1.0',
        on: {
          push: { branches: ['main'] }
        },
        env: {
          NODE_ENV: 'production'
        },
        jobs: {
          build: {
            'runs-on': 'ubuntu-latest',
            steps: [
              { name: 'Checkout code', uses: 'actions/checkout@v3' },
              { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
              { name: 'Install dependencies', run: 'npm ci' },
              { name: 'Build docs', run: 'npm run build' },
              { name: 'Deploy to Netlify', run: 'netlify deploy --prod' }
            ]
          }
        }
      }
    },
    {
      name: 'Microservice Security Scan',
      slug: 'microservice-security-scan',
      description: 'Security scanning and vulnerability assessment',
      projectId: 'cmbkrevct000ee0q3kxlva55u', // API Server project
      config: {
        version: '1.0',
        on: {
          push: { branches: ['main'] },
          schedule: [{ cron: '0 2 * * *' }]
        },
        env: {
          SECURITY_SCAN: 'true'
        },
        jobs: {
          security: {
            'runs-on': 'ubuntu-latest',
            steps: [
              { name: 'Checkout code', uses: 'actions/checkout@v3' },
              { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
              { name: 'Install dependencies', run: 'npm ci' },
              { name: 'Run security audit', run: 'npm audit --audit-level high' },
              { name: 'Run SonarQube scan', run: 'sonar-scanner' },
              { name: 'Scan Docker image', run: 'trivy image myapp:latest' },
              { name: 'Send Slack notification', run: 'curl -X POST $SLACK_WEBHOOK_URL' }
            ]
          }
        }
      }
    }
  ];
  
  // Create pipelines
  console.log('\n📋 Creating pipelines...');
  for (const pipelineData of pipelines) {
    await createPipeline(pipelineData);
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
  }
  
  console.log('\n🎉 All pipelines created successfully!');
  console.log('\n🌐 Visit http://localhost:4000 to see your real pipelines!');
  console.log('🔐 Login with: <EMAIL> / demo123');
}

main().catch(console.error);
