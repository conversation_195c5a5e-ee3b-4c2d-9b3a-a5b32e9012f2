version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainops-postgres-dev
    environment:
      POSTGRES_DB: chainops
      POSTGRES_USER: chainops
      POSTGRES_PASSWORD: chainops_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chainops-network

  # Redis for Queue and Caching
  redis:
    image: redis:7-alpine
    container_name: chainops-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - chainops-network

  # MinIO for Artifact Storage
  minio:
    image: minio/minio:latest
    container_name: chainops-minio-dev
    environment:
      MINIO_ROOT_USER: chainops
      MINIO_ROOT_PASSWORD: chainops_password
    ports:
      - "9002:9000"
      - "9003:9001"
    volumes:
      - minio_data_dev:/data
    command: server /data --console-address ":9001"
    networks:
      - chainops-network

volumes:
  postgres_data_dev:
  redis_data_dev:
  minio_data_dev:

networks:
  chainops-network:
    driver: bridge
