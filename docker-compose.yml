version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chainops-postgres
    environment:
      POSTGRES_DB: chainops
      POSTGRES_USER: chainops
      POSTGRES_PASSWORD: chainops_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chainops-network

  # Redis for Queue and Caching
  redis:
    image: redis:7-alpine
    container_name: chainops-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chainops-network

  # MinIO for Artifact Storage
  minio:
    image: minio/minio:latest
    container_name: chainops-minio
    environment:
      MINIO_ROOT_USER: chainops
      MINIO_ROOT_PASSWORD: chainops_password
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - chainops-network

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chainops-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: *****************************************************/chainops
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: chainops
      MINIO_SECRET_KEY: chainops_password
      JWT_SECRET: your-super-secret-jwt-key
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
      - minio
    volumes:
      - ./backend:/app
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - chainops-network

  # Frontend Web UI
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chainops-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_PUBLIC_WS_URL: ws://localhost:3001
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
    networks:
      - chainops-network

  # Runner Agent
  runner:
    build:
      context: ./runner
      dockerfile: Dockerfile
    container_name: chainops-runner
    environment:
      API_URL: http://backend:3001
      RUNNER_TOKEN: runner-secret-token
      DOCKER_HOST: unix:///var/run/docker.sock
    depends_on:
      - backend
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - runner_workspace:/workspace
    networks:
      - chainops-network

volumes:
  postgres_data:
  redis_data:
  minio_data:
  runner_workspace:

networks:
  chainops-network:
    driver: bridge
