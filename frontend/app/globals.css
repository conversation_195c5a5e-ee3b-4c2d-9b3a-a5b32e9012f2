@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-pending {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-queued {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-running {
    @apply bg-blue-100 text-blue-800;
  }
  
  .status-success {
    @apply bg-green-100 text-green-800;
  }
  
  .status-failed {
    @apply bg-red-100 text-red-800;
  }
  
  .status-cancelled {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-skipped {
    @apply bg-purple-100 text-purple-800;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100;
  }
  
  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* Card component */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form elements */
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .form-textarea {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .form-checkbox {
    @apply rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }
  
  .form-radio {
    @apply border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  /* Loading animations */
  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
  
  .animate-pulse-fast {
    animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Code blocks */
  .code-block {
    @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto font-mono text-sm;
  }
  
  .code-inline {
    @apply bg-gray-100 text-gray-900 rounded px-1.5 py-0.5 font-mono text-sm;
  }

  /* Sidebar navigation */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .nav-item-active {
    @apply bg-primary-100 text-primary-700;
  }
  
  .nav-item-inactive {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }

  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-row {
    @apply hover:bg-gray-50 transition-colors;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
