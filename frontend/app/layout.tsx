import type { Metadata } from 'next';
import './globals.css';
import { Providers } from './providers';

export const metadata: Metadata = {
  title: 'ChainOps - CI/CD Platform',
  description: 'Modern CI/CD platform with real-time monitoring and deployment automation',
  keywords: ['CI/CD', 'DevOps', 'Pipeline', 'Automation', 'Docker', 'Kubernetes'],
  authors: [{ name: 'ChainOps Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#3b82f6',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    title: 'ChainOps - CI/CD Platform',
    description: 'Modern CI/CD platform with real-time monitoring and deployment automation',
    type: 'website',
    locale: 'en_US',
    siteName: 'ChainOps',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ChainOps - CI/CD Platform',
    description: 'Modern CI/CD platform with real-time monitoring and deployment automation',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full bg-gray-50 antialiased font-sans">
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
