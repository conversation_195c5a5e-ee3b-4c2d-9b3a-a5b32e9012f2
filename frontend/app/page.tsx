'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function HomePage() {
  const router = useRouter();

  useEffect(() => {
    // Simple redirect to login for now
    router.push('/auth/login');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">ChainOps</h1>
          <p className="text-lg text-gray-600">Modern CI/CD Platform</p>
        </div>
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600 mx-auto"></div>
        <p className="mt-4 text-sm text-gray-500">Redirecting to login...</p>
      </div>
    </div>
  );
}
