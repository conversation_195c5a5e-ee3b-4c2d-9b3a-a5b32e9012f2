'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeftIcon, 
  PlayIcon, 
  Cog6ToothIcon,
  ClockIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { pipelineService } from '@/services/pipelines';
import { Pipeline, PipelineRun } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';

export default function PipelineDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pipelineId = params?.id as string;
  const [activeTab, setActiveTab] = useState<'runs' | 'config' | 'stats'>('runs');

  // Fetch pipeline details
  const { 
    data: pipeline, 
    isLoading: pipelineLoading, 
    error: pipelineError 
  } = useQuery({
    queryKey: ['pipeline', pipelineId],
    queryFn: () => pipelineService.getPipeline(pipelineId),
    enabled: !!pipelineId,
  });

  // Fetch pipeline runs
  const { 
    data: runsData, 
    isLoading: runsLoading, 
    refetch: refetchRuns 
  } = useQuery({
    queryKey: ['pipeline-runs', pipelineId],
    queryFn: () => pipelineService.getPipelineRuns(pipelineId, { page: 1, limit: 20 }),
    enabled: !!pipelineId,
    refetchInterval: 30000,
  });

  // Fetch pipeline stats
  const { data: stats } = useQuery({
    queryKey: ['pipeline-stats', pipelineId],
    queryFn: () => pipelineService.getPipelineStats(pipelineId),
    enabled: !!pipelineId,
  });

  const handleTriggerPipeline = async () => {
    if (!pipeline) return;
    
    try {
      const run = await pipelineService.triggerPipeline(pipeline.id);
      if (run) {
        router.push(`/pipelines/${pipeline.id}/runs/${run.id}`);
      }
    } catch (error) {
      console.error('Failed to trigger pipeline:', error);
    }
  };

  const handleViewRun = (run: PipelineRun) => {
    router.push(`/pipelines/${pipelineId}/runs/${run.id}`);
  };

  const getStatusBadge = (status: string) => {
    return (
      <span className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        jobService.getStatusColorClass(status.toLowerCase())
      )}>
        {status}
      </span>
    );
  };

  if (pipelineLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (pipelineError || !pipeline) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load pipeline</p>
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              Go back
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const tabs = [
    { id: 'runs', label: 'Runs', icon: ClockIcon, count: runsData?.total },
    { id: 'config', label: 'Configuration', icon: Cog6ToothIcon },
    { id: 'stats', label: 'Statistics', icon: ChartBarIcon },
  ];

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            
            <div>
              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-semibold text-gray-900">
                  {pipeline.name}
                </h1>
                {getStatusBadge(pipeline.status)}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {pipeline.project.name} • {pipeline.description || 'No description'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => router.push(`/pipelines/${pipeline.id}/edit`)}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Cog6ToothIcon className="w-5 h-5" />
              <span>Configure</span>
            </button>
            
            <button
              onClick={handleTriggerPipeline}
              disabled={pipeline.status === 'RUNNING'}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <PlayIcon className="w-5 h-5" />
              <span>Run Pipeline</span>
            </button>
          </div>
        </div>

        {/* Stats overview */}
        {stats && (
          <div className="grid grid-cols-4 gap-6 p-6 bg-gray-50 border-b border-gray-200">
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">{stats.totalRuns}</div>
              <div className="text-sm text-gray-600">Total Runs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-green-600">{stats.successRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">{stats.avgDuration}s</div>
              <div className="text-sm text-gray-600">Avg Duration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">
                {stats.lastRun ? formatDistanceToNow(new Date(stats.lastRun.createdAt), { addSuffix: true }) : 'Never'}
              </div>
              <div className="text-sm text-gray-600">Last Run</div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b border-gray-200 bg-white">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                'flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              )}
            >
              <tab.icon className="w-5 h-5" />
              <span>{tab.label}</span>
              {tab.count !== undefined && (
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'runs' && (
            <div className="p-6">
              {runsLoading ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : runsData?.runs.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">No pipeline runs yet</p>
                  <button
                    onClick={handleTriggerPipeline}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Run your first pipeline
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {runsData?.runs.map(run => (
                    <div
                      key={run.id}
                      className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => handleViewRun(run)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div>
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold text-gray-900">
                                Run #{run.number}
                              </h3>
                              {getStatusBadge(run.status)}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              Started {formatDistanceToNow(new Date(run.createdAt), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          {run.startedAt && run.finishedAt && (
                            <div className="text-sm text-gray-600">
                              Duration: {jobService.formatDuration(run.startedAt, run.finishedAt)}
                            </div>
                          )}
                          <div className="text-sm text-gray-500">
                            {run.jobs?.length || 0} jobs
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'config' && (
            <div className="p-6">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pipeline Configuration</h3>
                <pre className="bg-gray-50 rounded-lg p-4 text-sm overflow-auto">
                  {JSON.stringify(pipeline.config, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
                  {stats ? (
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Runs:</span>
                        <span className="font-medium">{stats.totalRuns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Success Rate:</span>
                        <span className="font-medium text-green-600">{stats.successRate.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Average Duration:</span>
                        <span className="font-medium">{stats.avgDuration}s</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500">No statistics available</p>
                  )}
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <p className="text-gray-500">Activity chart would go here</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
