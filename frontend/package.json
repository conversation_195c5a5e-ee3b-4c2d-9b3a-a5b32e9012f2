{"name": "chainops-frontend", "version": "1.0.0", "description": "ChainOps Frontend Web Interface", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start -p 4000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.11", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.14.2", "axios": "^1.6.2", "clsx": "^2.0.0", "concurrently": "^9.1.2", "d3": "^7.8.5", "d3-hierarchy": "^3.1.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "^14.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-resizable-panels": "^0.0.55", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "reactflow": "^11.10.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.1.0", "three": "^0.158.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/env": "^15.3.3", "@tanstack/react-query-devtools": "^5.80.6", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/d3": "^7.4.3", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/three": "^0.158.3", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}