import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useParams } from 'react-router-dom';
import PipelineEditor from './components/pipeline/PipelineEditor';
import { 
  HomeIcon, 
  ChartBarIcon, 
  Cog6ToothIcon,
  BeakerIcon 
} from '@heroicons/react/24/outline';

function HomePage() {
  const [selectedPipeline, setSelectedPipeline] = useState('pipeline-1');
  const [selectedRun, setSelectedRun] = useState('');

  const samplePipelines = [
    { id: 'pipeline-1', name: 'ChainOps Demo Pipeline', status: 'running' },
    { id: 'pipeline-2', name: 'Frontend Build Pipeline', status: 'success' },
    { id: 'pipeline-3', name: 'Backend API Pipeline', status: 'error' },
    { id: 'pipeline-4', name: 'Integration Test Pipeline', status: 'pending' },
  ];

  const sampleRuns = [
    { id: 'run-1', number: 42, status: 'running', commit: 'abc123d', branch: 'main' },
    { id: 'run-2', number: 41, status: 'success', commit: 'def456e', branch: 'main' },
    { id: 'run-3', number: 40, status: 'error', commit: 'ghi789f', branch: 'feature/new-ui' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900">ChainOps</h1>
              </div>
              <div className="ml-10 flex items-baseline space-x-4">
                <Link
                  to="/"
                  className="text-gray-900 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <HomeIcon className="h-4 w-4 mr-1 inline" />
                  Dashboard
                </Link>
                <Link
                  to="/pipelines"
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <BeakerIcon className="h-4 w-4 mr-1 inline" />
                  Pipelines
                </Link>
                <Link
                  to="/analytics"
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <ChartBarIcon className="h-4 w-4 mr-1 inline" />
                  Analytics
                </Link>
                <Link
                  to="/settings"
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <Cog6ToothIcon className="h-4 w-4 mr-1 inline" />
                  Settings
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Pipeline Selection */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Pipeline Visualization</h2>
            <p className="text-sm text-gray-600">
              Select a pipeline and run to visualize the execution flow
            </p>
          </div>
          
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Pipeline Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Pipeline
                </label>
                <select
                  value={selectedPipeline}
                  onChange={(e) => setSelectedPipeline(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {samplePipelines.map((pipeline) => (
                    <option key={pipeline.id} value={pipeline.id}>
                      {pipeline.name} ({pipeline.status})
                    </option>
                  ))}
                </select>
              </div>

              {/* Run Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Run (Optional)
                </label>
                <select
                  value={selectedRun}
                  onChange={(e) => setSelectedRun(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Latest Run</option>
                  {sampleRuns.map((run) => (
                    <option key={run.id} value={run.id}>
                      Run #{run.number} - {run.commit} ({run.status})
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Pipeline Visualization */}
        <div className="bg-white shadow rounded-lg">
          <PipelineEditor 
            pipelineId={selectedPipeline} 
            runId={selectedRun || undefined}
          />
        </div>

        {/* Quick Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BeakerIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Pipelines
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {samplePipelines.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Success Rate
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      75%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <HomeIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Runs
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {sampleRuns.filter(run => run.status === 'running').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function PipelineDetailPage() {
  const { pipelineId, runId } = useParams();
  
  return (
    <div className="min-h-screen bg-gray-50">
      <PipelineEditor pipelineId={pipelineId} runId={runId} />
    </div>
  );
}

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/pipeline/:pipelineId" element={<PipelineDetailPage />} />
          <Route path="/pipeline/:pipelineId/run/:runId" element={<PipelineDetailPage />} />
          <Route path="/pipelines" element={<HomePage />} />
          <Route path="/analytics" element={<HomePage />} />
          <Route path="/settings" element={<HomePage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
