'use client';

import { useAuth } from '@/store/auth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { PipelineStatus } from '@/components/dashboard/PipelineStatus';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { useQuery } from '@tanstack/react-query';
import { dashboardService } from '@/services/dashboard';

export default function DashboardPage() {
  const { user, isLoading } = useAuth();

  // Fetch dashboard data
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: dashboardService.getStats,
    enabled: !!user,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const { data: systemHealth, isLoading: healthLoading } = useQuery({
    queryKey: ['dashboard', 'health'],
    queryFn: dashboardService.getSystemHealth,
    enabled: !!user,
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return null; // This should redirect to login via the auth provider
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user.firstName || user.username}!
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Here's what's happening with your CI/CD pipelines today.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center text-sm text-gray-500">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                All systems operational
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsLoading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))
          ) : (
            <>
              <StatsCard
                title="Total Pipelines"
                value={stats?.totalPipelines?.toString() || '0'}
                icon="pipeline"
              />
              <StatsCard
                title="Active Jobs"
                value={stats?.activeJobs?.toString() || '0'}
                icon="jobs"
              />
              <StatsCard
                title="Success Rate"
                value={`${stats?.successRate || 0}%`}
                icon="success"
              />
              <StatsCard
                title="Avg Duration"
                value={stats?.avgDuration || '0s'}
                icon="time"
              />
            </>
          )}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Pipeline Status */}
          <div className="lg:col-span-2">
            <PipelineStatus />
          </div>

          {/* Quick Actions */}
          <div>
            <QuickActions />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <RecentActivity />
          
          {/* System Health */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
            {healthLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                    <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {systemHealth && Object.entries(systemHealth).map(([service, status]) => {
                  const getStatusStyle = (status: string) => {
                    switch (status) {
                      case 'healthy':
                        return 'bg-green-100 text-green-800';
                      case 'warning':
                        return 'bg-yellow-100 text-yellow-800';
                      case 'unhealthy':
                        return 'bg-red-100 text-red-800';
                      default:
                        return 'bg-gray-100 text-gray-800';
                    }
                  };

                  return (
                    <div key={service} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{service}</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyle(status)}`}>
                        {status === 'healthy' ? 'Healthy' : status === 'warning' ? 'Warning' : 'Unhealthy'}
                      </span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
