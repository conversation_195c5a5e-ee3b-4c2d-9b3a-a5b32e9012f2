'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PipelineVisualization } from '@/components/pipelines/PipelineVisualization';
import { PipelineRun, Job, JobStatus } from '@/types/pipelines';

// Mock data for demonstration
const createMockPipelineRun = (): PipelineRun => {
  const jobs: Job[] = [
    {
      id: 'job-1',
      name: 'lint',
      pipelineRunId: 'run-1',
      userId: 'user-1',
      status: 'SUCCESS' as JobStatus,
      config: {
        steps: [
          { name: 'Checkout code', uses: 'actions/checkout@v3' },
          { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
          { name: 'Install dependencies', run: 'npm ci' },
          { name: 'Run linter', run: 'npm run lint' },
        ],
      },
      startedAt: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
      finishedAt: new Date(Date.now() - 240000).toISOString(), // 4 minutes ago
      createdAt: new Date(Date.now() - 300000).toISOString(),
      updatedAt: new Date(Date.now() - 240000).toISOString(),
    },
    {
      id: 'job-2',
      name: 'test',
      pipelineRunId: 'run-1',
      userId: 'user-1',
      status: 'SUCCESS' as JobStatus,
      config: {
        needs: ['lint'],
        steps: [
          { name: 'Checkout code', uses: 'actions/checkout@v3' },
          { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
          { name: 'Install dependencies', run: 'npm ci' },
          { name: 'Run tests', run: 'npm test' },
        ],
      },
      startedAt: new Date(Date.now() - 240000).toISOString(), // 4 minutes ago
      finishedAt: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
      createdAt: new Date(Date.now() - 240000).toISOString(),
      updatedAt: new Date(Date.now() - 180000).toISOString(),
    },
    {
      id: 'job-3',
      name: 'build',
      pipelineRunId: 'run-1',
      userId: 'user-1',
      status: 'RUNNING' as JobStatus,
      config: {
        needs: ['test'],
        steps: [
          { name: 'Checkout code', uses: 'actions/checkout@v3' },
          { name: 'Setup Node.js', uses: 'actions/setup-node@v3', with: { 'node-version': '18' } },
          { name: 'Install dependencies', run: 'npm ci' },
          { name: 'Build application', run: 'npm run build' },
        ],
      },
      startedAt: new Date(Date.now() - 180000).toISOString(), // 3 minutes ago
      createdAt: new Date(Date.now() - 180000).toISOString(),
      updatedAt: new Date(Date.now() - 60000).toISOString(),
    },
    {
      id: 'job-4',
      name: 'deploy-staging',
      pipelineRunId: 'run-1',
      userId: 'user-1',
      status: 'PENDING' as JobStatus,
      config: {
        needs: ['build'],
        steps: [
          { name: 'Deploy to staging', run: 'kubectl apply -f k8s/staging/' },
          { name: 'Run smoke tests', run: 'npm run test:smoke' },
        ],
      },
      createdAt: new Date(Date.now() - 180000).toISOString(),
      updatedAt: new Date(Date.now() - 60000).toISOString(),
    },
    {
      id: 'job-5',
      name: 'deploy-production',
      pipelineRunId: 'run-1',
      userId: 'user-1',
      status: 'PENDING' as JobStatus,
      config: {
        needs: ['deploy-staging'],
        steps: [
          { name: 'Deploy to production', run: 'kubectl apply -f k8s/production/' },
          { name: 'Run health checks', run: 'npm run test:health' },
        ],
      },
      createdAt: new Date(Date.now() - 180000).toISOString(),
      updatedAt: new Date(Date.now() - 60000).toISOString(),
    },
  ];

  return {
    id: 'run-1',
    number: 42,
    pipelineId: 'pipeline-1',
    status: 'RUNNING',
    startedAt: new Date(Date.now() - 300000).toISOString(),
    createdAt: new Date(Date.now() - 300000).toISOString(),
    updatedAt: new Date(Date.now() - 60000).toISOString(),
    jobs,
    pipeline: {
      id: 'pipeline-1',
      name: 'ChainOps Frontend CI/CD',
      slug: 'chainops-frontend-ci-cd',
      description: 'Build, test, and deploy the ChainOps frontend application',
      projectId: 'project-1',
      userId: 'user-1',
      config: {
        jobs: {
          lint: jobs[0].config,
          test: jobs[1].config,
          build: jobs[2].config,
          'deploy-staging': jobs[3].config,
          'deploy-production': jobs[4].config,
        },
      },
      isActive: true,
      status: 'RUNNING',
      createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      updatedAt: new Date(Date.now() - 60000).toISOString(),
      project: {
        id: 'project-1',
        name: 'ChainOps',
        slug: 'chainops',
      },
      user: {
        id: 'user-1',
        username: 'developer',
        firstName: 'John',
        lastName: 'Doe',
      },
    },
  };
};

export default function DemoPage() {
  const [mockPipelineRun] = useState<PipelineRun>(createMockPipelineRun());

  return (
    <DashboardLayout>
      <div className="h-full">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900">
            Pipeline Visualization Demo
          </h1>
          <p className="text-sm text-gray-600 mt-1">
            Interactive demonstration of the ChainOps pipeline visualization features
          </p>
        </div>

        <div className="h-[calc(100vh-200px)] bg-white rounded-lg shadow-sm border border-gray-200">
          <PipelineVisualization
            pipelineRun={mockPipelineRun}
            defaultView="2d"
            showMinimap={true}
            showControls={true}
            animated={true}
            className="h-full"
          />
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">Features Demonstrated</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Interactive 2D/3D pipeline graphs</li>
              <li>• Real-time job status updates</li>
              <li>• Collapsible stage list view</li>
              <li>• Live log streaming</li>
              <li>• Job details and artifacts</li>
              <li>• Resizable panes</li>
            </ul>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">View Controls</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Switch between 2D, 3D, and list views</li>
              <li>• Toggle minimap and controls</li>
              <li>• Enable/disable animations</li>
              <li>• Resize visualization panes</li>
              <li>• Search and filter logs</li>
            </ul>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="font-medium text-gray-900 mb-2">Interactions</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Click jobs to view details</li>
              <li>• Double-click for quick actions</li>
              <li>• Hover for status information</li>
              <li>• Drag to pan in 2D/3D views</li>
              <li>• Scroll to zoom</li>
            </ul>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
