'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  GlobeAltIcon, 
  PlusIcon, 
  CogIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

export default function EnvironmentsPage() {
  const [environments] = useState([
    {
      id: '1',
      name: 'Production',
      description: 'Production environment for live applications',
      type: 'production',
      status: 'active',
      url: 'https://app.company.com',
      lastDeployment: '2024-01-15T10:30:00Z',
      deploymentCount: 45,
    },
    {
      id: '2',
      name: 'Staging',
      description: 'Staging environment for testing before production',
      type: 'staging',
      status: 'active',
      url: 'https://staging.company.com',
      lastDeployment: '2024-01-15T14:20:00Z',
      deploymentCount: 128,
    },
    {
      id: '3',
      name: 'Development',
      description: 'Development environment for feature testing',
      type: 'development',
      status: 'active',
      url: 'https://dev.company.com',
      lastDeployment: '2024-01-15T16:45:00Z',
      deploymentCount: 234,
    },
  ]);

  const getEnvironmentIcon = (type: string) => {
    switch (type) {
      case 'production':
        return <ShieldCheckIcon className="w-6 h-6 text-red-600" />;
      case 'staging':
        return <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />;
      case 'development':
        return <CogIcon className="w-6 h-6 text-blue-600" />;
      default:
        return <GlobeAltIcon className="w-6 h-6 text-gray-600" />;
    }
  };

  const getEnvironmentColor = (type: string) => {
    switch (type) {
      case 'production':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'staging':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'development':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Environments</h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage deployment environments and their configurations
            </p>
          </div>
          
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <PlusIcon className="w-5 h-5" />
            <span>New Environment</span>
          </button>
        </div>

        {/* Environments Grid */}
        <div className="flex-1 p-6 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {environments.map((env) => (
              <div
                key={env.id}
                className={`bg-white rounded-lg border-2 p-6 hover:shadow-md transition-shadow cursor-pointer ${getEnvironmentColor(env.type)}`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center border">
                      {getEnvironmentIcon(env.type)}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {env.name}
                      </h3>
                      <p className="text-sm text-gray-600 capitalize">
                        {env.type}
                      </p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    env.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {env.status}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">
                  {env.description}
                </p>

                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                      URL
                    </label>
                    <p className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                      {env.url}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Deployments
                      </label>
                      <p className="text-lg font-semibold text-gray-900">
                        {env.deploymentCount}
                      </p>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Deploy
                      </label>
                      <p className="text-sm text-gray-600">
                        {new Date(env.lastDeployment).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <button className="flex-1 px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                      Configure
                    </button>
                    <button className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                      Deploy
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {environments.length === 0 && (
            <div className="text-center py-12">
              <GlobeAltIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No environments</h3>
              <p className="text-gray-600 mb-6">
                Create your first deployment environment to get started
              </p>
              <button className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <PlusIcon className="w-5 h-5" />
                <span>Create Environment</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
