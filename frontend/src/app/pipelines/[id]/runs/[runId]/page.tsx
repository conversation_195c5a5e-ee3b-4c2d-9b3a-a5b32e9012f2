'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeftIcon, 
  StopIcon, 
  ArrowPathIcon,
  ShareIcon,
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { PipelineVisualization } from '@/components/pipelines/PipelineVisualization';
import { pipelineService } from '@/services/pipelines';
import { PipelineRun } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';
import { useWebSocket } from '@/services/websocket';

export default function PipelineRunDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pipelineId = params?.id as string;
  const runId = params?.runId as string;
  const [pipelineRun, setPipelineRun] = useState<PipelineRun | null>(null);

  const { subscribe, unsubscribe } = useWebSocket();

  // Fetch pipeline run details
  const { 
    data: fetchedRun, 
    isLoading, 
    error,
    refetch 
  } = useQuery({
    queryKey: ['pipeline-run', pipelineId, runId],
    queryFn: () => pipelineService.getPipelineRun(pipelineId, runId),
    enabled: !!pipelineId && !!runId,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Update local state when data is fetched
  useEffect(() => {
    if (fetchedRun) {
      setPipelineRun(fetchedRun);
    }
  }, [fetchedRun]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!pipelineRun) return;

    const handlePipelineUpdate = (update: any) => {
      if (update.id === pipelineRun.id || update.id === pipelineRun.pipelineId) {
        // Refetch the pipeline run to get latest data
        refetch();
      }
    };

    const handleJobUpdate = (update: any) => {
      if (pipelineRun.jobs?.some(job => job.id === update.id)) {
        // Refetch the pipeline run to get latest job data
        refetch();
      }
    };

    subscribe('pipeline:update', handlePipelineUpdate);
    subscribe('job:update', handleJobUpdate);

    return () => {
      unsubscribe('pipeline:update', handlePipelineUpdate);
      unsubscribe('job:update', handleJobUpdate);
    };
  }, [pipelineRun, subscribe, unsubscribe, refetch]);

  const handleCancelRun = async () => {
    if (!pipelineRun) return;
    
    try {
      const success = await pipelineService.cancelPipelineRun(pipelineId, runId);
      if (success) {
        refetch();
      }
    } catch (error) {
      console.error('Failed to cancel pipeline run:', error);
    }
  };

  const handleRetryRun = async () => {
    if (!pipelineRun) return;
    
    try {
      const newRun = await pipelineService.retryPipelineRun(pipelineId, runId);
      if (newRun) {
        router.push(`/pipelines/${pipelineId}/runs/${newRun.id}`);
      }
    } catch (error) {
      console.error('Failed to retry pipeline run:', error);
    }
  };

  const handleShare = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      // Could show a toast notification here
      console.log('URL copied to clipboard');
    });
  };

  const getStatusBadge = (status: string) => {
    return (
      <span className={cn(
        'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
        jobService.getStatusColorClass(status.toLowerCase())
      )}>
        {status}
      </span>
    );
  };

  const getStatusIcon = (status: string) => {
    return jobService.getStatusIcon(status.toLowerCase());
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !pipelineRun) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load pipeline run</p>
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              Go back
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const canCancel = ['PENDING', 'RUNNING'].includes(pipelineRun.status);
  const canRetry = ['FAILED', 'CANCELLED'].includes(pipelineRun.status);

  const duration = pipelineRun.startedAt && pipelineRun.finishedAt
    ? jobService.formatDuration(pipelineRun.startedAt, pipelineRun.finishedAt)
    : pipelineRun.startedAt
    ? 'Running...'
    : 'Not started';

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push(`/pipelines/${pipelineId}`)}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{getStatusIcon(pipelineRun.status)}</span>
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-xl font-semibold text-gray-900">
                    {pipelineRun.pipeline?.name} • Run #{pipelineRun.number}
                  </h1>
                  {getStatusBadge(pipelineRun.status)}
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                  <span>
                    Started {formatDistanceToNow(new Date(pipelineRun.createdAt), { addSuffix: true })}
                  </span>
                  <span>•</span>
                  <span>Duration: {duration}</span>
                  <span>•</span>
                  <span>{pipelineRun.jobs?.length || 0} jobs</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleShare}
              className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <ShareIcon className="w-4 h-4" />
              <span>Share</span>
            </button>
            
            {canRetry && (
              <button
                onClick={handleRetryRun}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ArrowPathIcon className="w-4 h-4" />
                <span>Retry</span>
              </button>
            )}
            
            {canCancel && (
              <button
                onClick={handleCancelRun}
                className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <StopIcon className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            )}
          </div>
        </div>

        {/* Pipeline Visualization */}
        <div className="flex-1 overflow-hidden">
          <PipelineVisualization
            pipelineRun={pipelineRun}
            defaultView="2d"
            showMinimap={true}
            showControls={true}
            animated={true}
            className="h-full"
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
