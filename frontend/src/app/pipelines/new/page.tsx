'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { ArrowLeftIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { pipelineService } from '@/services/pipelines';
import { projectService } from '@/services/project';

interface CreatePipelineForm {
  name: string;
  slug: string;
  description?: string;
  projectId: string;
  config: string;
}

const defaultPipelineConfig = `# ChainOps Pipeline Configuration
name: "Build and Test"
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_ENV: production

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
      
      - name: Deploy to staging
        run: echo "Deploying to staging environment"
`;

export default function NewPipelinePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreatePipelineForm>({
    defaultValues: {
      projectId: projectId || '',
      config: defaultPipelineConfig,
    },
  });

  // Fetch projects for selection
  const { data: projects } = useQuery({
    queryKey: ['projects'],
    queryFn: () => projectService.getProjects(),
  });

  const createPipelineMutation = useMutation({
    mutationFn: pipelineService.createPipeline,
    onSuccess: (pipeline) => {
      toast.success('Pipeline created successfully!');
      router.push(`/pipelines/${pipeline.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create pipeline');
    },
  });

  const onSubmit = async (data: CreatePipelineForm) => {
    setIsSubmitting(true);
    try {
      await createPipelineMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-generate slug from name
  const watchedName = watch('name');
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    setValue('slug', slug);
  };

  const loadTemplate = (template: string) => {
    let config = '';
    switch (template) {
      case 'nodejs':
        config = defaultPipelineConfig;
        break;
      case 'python':
        config = `# Python Pipeline
name: "Python CI/CD"
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - run: pip install -r requirements.txt
      - run: pytest
      
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - run: echo "Deploying Python app"`;
        break;
      case 'docker':
        config = `# Docker Pipeline
name: "Docker Build and Deploy"
on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: docker build -t myapp .
      - name: Push to registry
        run: docker push myapp:latest`;
        break;
      default:
        config = defaultPipelineConfig;
    }
    setValue('config', config);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Create New Pipeline</h1>
          <p className="text-gray-600 mt-2">
            Define your CI/CD workflow with a new pipeline configuration.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow-sm rounded-lg">
              <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
                {/* Project Selection */}
                <div>
                  <label htmlFor="projectId" className="block text-sm font-medium text-gray-700">
                    Project *
                  </label>
                  <select
                    id="projectId"
                    {...register('projectId', { required: 'Project is required' })}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="">Select a project</option>
                    {projects?.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                  {errors.projectId && (
                    <p className="mt-1 text-sm text-red-600">{errors.projectId.message}</p>
                  )}
                </div>

                {/* Pipeline Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Pipeline Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    {...register('name', {
                      required: 'Pipeline name is required',
                      maxLength: { value: 100, message: 'Name must be less than 100 characters' },
                      onChange: handleNameChange,
                    })}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Build and Deploy"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                {/* Pipeline Slug */}
                <div>
                  <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                    Pipeline Slug *
                  </label>
                  <input
                    type="text"
                    id="slug"
                    {...register('slug', {
                      required: 'Pipeline slug is required',
                      pattern: {
                        value: /^[a-z0-9-]+$/,
                        message: 'Slug can only contain lowercase letters, numbers, and hyphens',
                      },
                      maxLength: { value: 50, message: 'Slug must be less than 50 characters' },
                    })}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="build-and-deploy"
                  />
                  {errors.slug && (
                    <p className="mt-1 text-sm text-red-600">{errors.slug.message}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea
                    id="description"
                    rows={2}
                    {...register('description')}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Brief description of this pipeline..."
                  />
                </div>

                {/* Pipeline Configuration */}
                <div>
                  <label htmlFor="config" className="block text-sm font-medium text-gray-700">
                    Pipeline Configuration *
                  </label>
                  <textarea
                    id="config"
                    rows={20}
                    {...register('config', { required: 'Pipeline configuration is required' })}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm"
                    placeholder="Enter your pipeline configuration in YAML format..."
                  />
                  {errors.config && (
                    <p className="mt-1 text-sm text-red-600">{errors.config.message}</p>
                  )}
                </div>

                {/* Submit Buttons */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isSubmitting ? 'Creating...' : 'Create Pipeline'}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Templates Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow-sm rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                <DocumentTextIcon className="w-5 h-5 inline mr-2" />
                Templates
              </h3>
              <div className="space-y-3">
                <button
                  type="button"
                  onClick={() => loadTemplate('nodejs')}
                  className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50"
                >
                  <div className="font-medium text-gray-900">Node.js</div>
                  <div className="text-sm text-gray-500">Build and test Node.js applications</div>
                </button>
                <button
                  type="button"
                  onClick={() => loadTemplate('python')}
                  className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50"
                >
                  <div className="font-medium text-gray-900">Python</div>
                  <div className="text-sm text-gray-500">Python application CI/CD</div>
                </button>
                <button
                  type="button"
                  onClick={() => loadTemplate('docker')}
                  className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50"
                >
                  <div className="font-medium text-gray-900">Docker</div>
                  <div className="text-sm text-gray-500">Build and deploy Docker containers</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
