'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  PlusIcon,
  PlayIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { pipelineService } from '@/services/pipelines';
import { Pipeline, PipelineFilters, PaginationParams } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';

export default function PipelinesPage() {
  const router = useRouter();
  const [filters, setFilters] = useState<PipelineFilters>({});
  const [pagination, setPagination] = useState<PaginationParams>({ page: 1, limit: 20 });
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch pipelines
  const { 
    data: pipelinesData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['pipelines', filters, pagination],
    queryFn: () => pipelineService.getPipelines(filters, pagination),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Handle search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: searchTerm || undefined }));
      setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleTriggerPipeline = async (pipeline: Pipeline) => {
    try {
      const run = await pipelineService.triggerPipeline(pipeline.id);
      if (run) {
        router.push(`/pipelines/${pipeline.id}/runs/${run.id}`);
      }
    } catch (error) {
      console.error('Failed to trigger pipeline:', error);
    }
  };

  const handleViewPipeline = (pipeline: Pipeline) => {
    router.push(`/pipelines/${pipeline.id}`);
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      IDLE: 'bg-gray-100 text-gray-800',
      RUNNING: 'bg-blue-100 text-blue-800',
      SUCCESS: 'bg-green-100 text-green-800',
      FAILED: 'bg-red-100 text-red-800',
      CANCELLED: 'bg-yellow-100 text-yellow-800',
    };

    return (
      <span className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        statusClasses[status as keyof typeof statusClasses] || statusClasses.IDLE
      )}>
        {status}
      </span>
    );
  };

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Pipelines</h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage and monitor your CI/CD pipelines
            </p>
          </div>
          
          <button
            onClick={() => router.push('/pipelines/new')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            <span>New Pipeline</span>
          </button>
        </div>

        {/* Filters and Search */}
        <div className="flex items-center justify-between p-6 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search pipelines..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-80"
              />
            </div>

            {/* Status filter */}
            <select
              value={filters.status?.[0] || ''}
              onChange={(e) => setFilters(prev => ({
                ...prev,
                status: e.target.value ? [e.target.value as any] : undefined
              }))}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="IDLE">Idle</option>
              <option value="RUNNING">Running</option>
              <option value="SUCCESS">Success</option>
              <option value="FAILED">Failed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>

            {/* Clear filters */}
            {(filters.search || filters.status) && (
              <button
                onClick={() => {
                  setFilters({});
                  setSearchTerm('');
                }}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear filters
              </button>
            )}
          </div>

          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>
              {pipelinesData?.pagination.total || 0} pipelines
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-red-600 mb-2">Failed to load pipelines</p>
                <button
                  onClick={() => refetch()}
                  className="text-blue-600 hover:text-blue-700"
                >
                  Try again
                </button>
              </div>
            </div>
          ) : pipelinesData?.data.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-gray-500 mb-4">No pipelines found</p>
                <button
                  onClick={() => router.push('/pipelines/new')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Create your first pipeline
                </button>
              </div>
            </div>
          ) : (
            <div className="p-6">
              {/* Pipeline grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {pipelinesData?.data.map(pipeline => (
                  <div
                    key={pipeline.id}
                    className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    {/* Pipeline header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {pipeline.name}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2">
                          {pipeline.project.name}
                        </p>
                        {pipeline.description && (
                          <p className="text-sm text-gray-500 line-clamp-2">
                            {pipeline.description}
                          </p>
                        )}
                      </div>
                      
                      <div className="ml-4">
                        {getStatusBadge(pipeline.status)}
                      </div>
                    </div>

                    {/* Pipeline stats */}
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <span className="text-gray-500">Total Runs:</span>
                        <span className="ml-2 font-medium">
                          {pipeline._count?.runs || 0}
                        </span>
                      </div>
                      {pipeline.runs?.[0] && (
                        <div>
                          <span className="text-gray-500">Last Run:</span>
                          <span className="ml-2 font-medium">
                            {formatDistanceToNow(new Date(pipeline.runs[0].createdAt), { 
                              addSuffix: true 
                            })}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Last run info */}
                    {pipeline.runs?.[0] && (
                      <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">
                            Run #{pipeline.runs[0].number}
                          </span>
                          <span className={cn(
                            'px-2 py-1 rounded-full text-xs font-medium',
                            jobService.getStatusColorClass(pipeline.runs[0].status.toLowerCase())
                          )}>
                            {pipeline.runs[0].status}
                          </span>
                        </div>
                        
                        {pipeline.runs[0].startedAt && pipeline.runs[0].finishedAt && (
                          <span className="text-sm text-gray-500">
                            {jobService.formatDuration(
                              pipeline.runs[0].startedAt,
                              pipeline.runs[0].finishedAt
                            )}
                          </span>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewPipeline(pipeline)}
                        className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <EyeIcon className="w-4 h-4" />
                        <span>View</span>
                      </button>
                      
                      <button
                        onClick={() => handleTriggerPipeline(pipeline)}
                        disabled={pipeline.status === 'RUNNING'}
                        className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <PlayIcon className="w-4 h-4" />
                        <span>Run</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pipelinesData && pipelinesData.pagination.pages > 1 && (
                <div className="flex items-center justify-between mt-8">
                  <div className="text-sm text-gray-600">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pipelinesData.pagination.total)} of{' '}
                    {pipelinesData.pagination.total} pipelines
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Previous
                    </button>
                    
                    <span className="px-3 py-2 text-sm text-gray-600">
                      Page {pagination.page} of {pipelinesData.pagination.pages}
                    </span>
                    
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                      disabled={pagination.page === pipelinesData.pagination.pages}
                      className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
