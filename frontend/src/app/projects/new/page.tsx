'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { projectService } from '@/services/project';

interface CreateProjectForm {
  name: string;
  slug: string;
  description?: string;
  repositoryUrl?: string;
  defaultBranch: string;
}

export default function NewProjectPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateProjectForm>({
    defaultValues: {
      defaultBranch: 'main',
    },
  });

  const createProjectMutation = useMutation({
    mutationFn: projectService.createProject,
    onSuccess: (project) => {
      toast.success('Project created successfully!');
      router.push(`/projects/${project.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create project');
    },
  });

  const onSubmit = async (data: CreateProjectForm) => {
    setIsSubmitting(true);
    try {
      await createProjectMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-generate slug from name
  const watchedName = watch('name');
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    setValue('slug', slug);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Create New Project</h1>
          <p className="text-gray-600 mt-2">
            Set up a new CI/CD project to manage your pipelines and deployments.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-sm rounded-lg">
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Project Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Project Name *
              </label>
              <input
                type="text"
                id="name"
                {...register('name', {
                  required: 'Project name is required',
                  maxLength: { value: 100, message: 'Name must be less than 100 characters' },
                  onChange: handleNameChange,
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="My Awesome Project"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            {/* Project Slug */}
            <div>
              <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                Project Slug *
              </label>
              <input
                type="text"
                id="slug"
                {...register('slug', {
                  required: 'Project slug is required',
                  pattern: {
                    value: /^[a-z0-9-]+$/,
                    message: 'Slug can only contain lowercase letters, numbers, and hyphens',
                  },
                  maxLength: { value: 50, message: 'Slug must be less than 50 characters' },
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="my-awesome-project"
              />
              <p className="mt-1 text-sm text-gray-500">
                Used in URLs and API endpoints. Auto-generated from project name.
              </p>
              {errors.slug && (
                <p className="mt-1 text-sm text-red-600">{errors.slug.message}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                rows={3}
                {...register('description', {
                  maxLength: { value: 500, message: 'Description must be less than 500 characters' },
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="Brief description of your project..."
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Repository URL */}
            <div>
              <label htmlFor="repositoryUrl" className="block text-sm font-medium text-gray-700">
                Repository URL
              </label>
              <input
                type="url"
                id="repositoryUrl"
                {...register('repositoryUrl')}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="https://github.com/username/repository"
              />
              <p className="mt-1 text-sm text-gray-500">
                Optional. Link to your source code repository.
              </p>
              {errors.repositoryUrl && (
                <p className="mt-1 text-sm text-red-600">{errors.repositoryUrl.message}</p>
              )}
            </div>

            {/* Default Branch */}
            <div>
              <label htmlFor="defaultBranch" className="block text-sm font-medium text-gray-700">
                Default Branch
              </label>
              <input
                type="text"
                id="defaultBranch"
                {...register('defaultBranch', {
                  required: 'Default branch is required',
                  maxLength: { value: 50, message: 'Branch name must be less than 50 characters' },
                })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="main"
              />
              <p className="mt-1 text-sm text-gray-500">
                The default branch for pipeline triggers.
              </p>
              {errors.defaultBranch && (
                <p className="mt-1 text-sm text-red-600">{errors.defaultBranch.message}</p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Project'}
              </button>
            </div>
          </form>
        </div>

        {/* Info Box */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">What happens next?</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Your project will be created with default environments (Development, Staging, Production)</li>
            <li>• You'll be able to create pipelines and manage CI/CD workflows</li>
            <li>• Team members can be invited with different permission levels</li>
            <li>• Secrets and environment variables can be configured securely</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
