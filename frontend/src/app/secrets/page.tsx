'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { secretService } from '@/services/secret';
import { 
  KeyIcon, 
  PlusIcon, 
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  PencilIcon 
} from '@heroicons/react/24/outline';

export default function SecretsPage() {
  const [showValues, setShowValues] = useState<Record<string, boolean>>({});
  const [selectedProject, setSelectedProject] = useState<string>('all');

  const { 
    data: secrets, 
    isLoading, 
    error,
    refetch 
  } = useQuery({
    queryKey: ['secrets', selectedProject],
    queryFn: () => secretService.getSecrets(
      selectedProject === 'all' ? undefined : selectedProject
    ),
  });

  const toggleValueVisibility = (secretId: string) => {
    setShowValues(prev => ({
      ...prev,
      [secretId]: !prev[secretId]
    }));
  };

  const maskValue = (value: string) => {
    return '*'.repeat(Math.min(value.length, 20));
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load secrets</p>
            <button 
              onClick={() => refetch()}
              className="text-blue-600 hover:text-blue-800"
            >
              Try again
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Secrets</h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage environment variables and sensitive configuration
            </p>
          </div>
          
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <PlusIcon className="w-5 h-5" />
            <span>New Secret</span>
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Project:</span>
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Projects</option>
              <option value="web-app">Web Application</option>
              <option value="api-server">API Server</option>
              <option value="mobile-app">Mobile App</option>
            </select>
          </div>
        </div>

        {/* Secrets List */}
        <div className="flex-1 overflow-auto">
          {!secrets || secrets.length === 0 ? (
            <div className="text-center py-12">
              <KeyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No secrets found</h3>
              <p className="text-gray-600 mb-6">
                {selectedProject === 'all' 
                  ? 'No secrets have been created yet'
                  : `No secrets found for the selected project`
                }
              </p>
              <button className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <PlusIcon className="w-5 h-5" />
                <span>Create Secret</span>
              </button>
            </div>
          ) : (
            <div className="bg-white">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Project
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Environment
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Updated
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {secrets.map((secret) => (
                      <tr key={secret.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <KeyIcon className="w-5 h-5 text-gray-400 mr-3" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {secret.key}
                              </div>
                              {secret.description && (
                                <div className="text-sm text-gray-500">
                                  {secret.description}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {secret.project?.name || 'Global'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {secret.environment || 'All'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono">
                              {showValues[secret.id] ? secret.value : maskValue(secret.value)}
                            </code>
                            <button
                              onClick={() => toggleValueVisibility(secret.id)}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              {showValues[secret.id] ? (
                                <EyeSlashIcon className="w-4 h-4" />
                              ) : (
                                <EyeIcon className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(secret.updatedAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-900">
                              <PencilIcon className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
