import React, { useState } from 'react';
import { 
  XMarkIcon, 
  ClockIcon, 
  ServerIcon, 
  TagIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  PlayIcon,
  StopIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

export default function NodeDetailsPanel({ node, onClose }) {
  const [activeTab, setActiveTab] = useState('overview');

  if (!node) return null;

  const formatDuration = (ms) => {
    if (!ms) return 'N/A';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    return new Date(timeString).toLocaleString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'unstable': return 'text-yellow-600 bg-yellow-100';
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-gray-600 bg-gray-100';
      case 'skipped': return 'text-gray-400 bg-gray-50';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
    { id: 'steps', name: 'Steps', icon: PlayIcon },
    { id: 'logs', name: 'Logs', icon: DocumentTextIcon },
    { id: 'artifacts', name: 'Artifacts', icon: ArrowDownTrayIcon },
  ];

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(node.status)}`}>
            {node.status}
          </div>
          <h2 className="text-lg font-semibold text-gray-900 truncate">{node.label}</h2>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-md transition-colors"
        >
          <XMarkIcon className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-4" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-1 inline" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Info */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Basic Information</h3>
              <dl className="grid grid-cols-1 gap-3">
                <div>
                  <dt className="text-xs font-medium text-gray-500">Job ID</dt>
                  <dd className="text-sm text-gray-900 font-mono">{node.id}</dd>
                </div>
                <div>
                  <dt className="text-xs font-medium text-gray-500">Type</dt>
                  <dd className="text-sm text-gray-900">{node.type || 'job'}</dd>
                </div>
                {node.environment && (
                  <div>
                    <dt className="text-xs font-medium text-gray-500">Environment</dt>
                    <dd className="text-sm text-gray-900">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        <TagIcon className="h-3 w-3 mr-1" />
                        {node.environment}
                      </span>
                    </dd>
                  </div>
                )}
                {node.runs_on && (
                  <div>
                    <dt className="text-xs font-medium text-gray-500">Runner</dt>
                    <dd className="text-sm text-gray-900">
                      <span className="inline-flex items-center">
                        <ServerIcon className="h-4 w-4 mr-1 text-gray-400" />
                        {node.runs_on}
                      </span>
                    </dd>
                  </div>
                )}
              </dl>
            </div>

            {/* Timing */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Timing</h3>
              <dl className="grid grid-cols-1 gap-3">
                <div>
                  <dt className="text-xs font-medium text-gray-500">Start Time</dt>
                  <dd className="text-sm text-gray-900">{formatTime(node.start_time)}</dd>
                </div>
                <div>
                  <dt className="text-xs font-medium text-gray-500">End Time</dt>
                  <dd className="text-sm text-gray-900">{formatTime(node.end_time)}</dd>
                </div>
                <div>
                  <dt className="text-xs font-medium text-gray-500">Duration</dt>
                  <dd className="text-sm text-gray-900">
                    <span className="inline-flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1 text-gray-400" />
                      {formatDuration(node.duration)}
                    </span>
                  </dd>
                </div>
              </dl>
            </div>

            {/* Metadata */}
            {node.metadata && Object.keys(node.metadata).length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Metadata</h3>
                <dl className="grid grid-cols-1 gap-3">
                  {Object.entries(node.metadata).map(([key, value]) => (
                    <div key={key}>
                      <dt className="text-xs font-medium text-gray-500">{key}</dt>
                      <dd className="text-sm text-gray-900">{value}</dd>
                    </div>
                  ))}
                </dl>
              </div>
            )}

            {/* Actions */}
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">Actions</h3>
              <div className="flex flex-wrap gap-2">
                {node.status === 'running' && (
                  <button className="inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100">
                    <StopIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </button>
                )}
                {(node.status === 'error' || node.status === 'success') && (
                  <button className="inline-flex items-center px-3 py-1 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                    <ArrowPathIcon className="h-4 w-4 mr-1" />
                    Retry
                  </button>
                )}
                {node.log_url && (
                  <a
                    href={node.log_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    View Logs
                  </a>
                )}
                {node.artifact_url && (
                  <a
                    href={node.artifact_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                    Download Artifacts
                  </a>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'steps' && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Job Steps</h3>
            {node.steps && node.steps.length > 0 ? (
              <div className="space-y-3">
                {node.steps.map((step, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">{step.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(step.status)}`}>
                        {step.status}
                      </span>
                    </div>
                    {step.duration && (
                      <p className="text-xs text-gray-500">
                        Duration: {formatDuration(step.duration)}
                      </p>
                    )}
                    {step.log_url && (
                      <a
                        href={step.log_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        View step logs →
                      </a>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No step information available</p>
            )}
          </div>
        )}

        {activeTab === 'logs' && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Job Logs</h3>
            {node.log_url ? (
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-xs overflow-x-auto">
                <div className="mb-2 text-gray-400">Loading logs from {node.log_url}...</div>
                <div>$ Starting job execution...</div>
                <div>$ Setting up environment...</div>
                <div>$ Running job steps...</div>
                <div className="text-yellow-400">⚠ This is a demo - actual logs would be fetched from the log URL</div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">No logs available for this job</p>
            )}
          </div>
        )}

        {activeTab === 'artifacts' && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Artifacts</h3>
            {node.artifact_url ? (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Build Artifacts</h4>
                    <p className="text-xs text-gray-500">Generated by this job</p>
                  </div>
                  <a
                    href={node.artifact_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-1 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                    Download
                  </a>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">No artifacts available for this job</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
