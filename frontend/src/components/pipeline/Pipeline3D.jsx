import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Html, Text, Line } from '@react-three/drei';
import * as THREE from 'three';

function Node3D({ position, label, status, onClick, metadata }) {
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      // Gentle rotation for running jobs
      if (status === 'running') {
        meshRef.current.rotation.y += 0.01;
      }
      
      // Gentle floating animation
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + position[0]) * 0.1;
    }
  });

  const getColor = (status) => {
    switch (status) {
      case 'success': return '#10b981'; // green
      case 'error': return '#ef4444';   // red
      case 'unstable': return '#f59e0b'; // yellow
      case 'running': return '#3b82f6';  // blue
      case 'pending': return '#6b7280';  // gray
      case 'skipped': return '#9ca3af';  // light gray
      default: return '#6b7280';
    }
  };

  const getGeometry = (status) => {
    switch (status) {
      case 'error': return <octahedronGeometry args={[0.8]} />;
      case 'running': return <sphereGeometry args={[0.8, 32, 32]} />;
      default: return <boxGeometry args={[1.2, 1.2, 1.2]} />;
    }
  };

  return (
    <group position={position}>
      <mesh
        ref={meshRef}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={hovered ? 1.2 : 1}
      >
        {getGeometry(status)}
        <meshStandardMaterial 
          color={getColor(status)} 
          transparent
          opacity={hovered ? 0.9 : 0.8}
          emissive={status === 'running' ? getColor(status) : '#000000'}
          emissiveIntensity={status === 'running' ? 0.2 : 0}
        />
      </mesh>
      
      {/* Label */}
      <Html center>
        <div 
          className={`
            bg-white bg-opacity-90 px-2 py-1 rounded shadow-lg text-xs font-medium
            transition-all duration-200 pointer-events-none
            ${hovered ? 'scale-110' : 'scale-100'}
          `}
          style={{ 
            transform: 'translate(-50%, 20px)',
            minWidth: '80px',
            textAlign: 'center'
          }}
        >
          <div className="text-gray-900">{label}</div>
          <div className={`text-xs capitalize ${
            status === 'success' ? 'text-green-600' :
            status === 'error' ? 'text-red-600' :
            status === 'running' ? 'text-blue-600' :
            'text-gray-600'
          }`}>
            {status}
          </div>
          {metadata?.duration && (
            <div className="text-xs text-gray-500">
              {Math.round(metadata.duration / 1000)}s
            </div>
          )}
        </div>
      </Html>

      {/* Status indicator particles for running jobs */}
      {status === 'running' && (
        <points>
          <sphereGeometry args={[2, 8, 8]} />
          <pointsMaterial color="#3b82f6" size={0.1} transparent opacity={0.6} />
        </points>
      )}
    </group>
  );
}

function Edge3D({ from, to, color = '#888888' }) {
  const points = [
    new THREE.Vector3(...from),
    new THREE.Vector3(...to)
  ];

  return (
    <Line
      points={points}
      color={color}
      lineWidth={2}
      transparent
      opacity={0.6}
    />
  );
}

function Grid3D() {
  return (
    <gridHelper args={[20, 20, '#e5e7eb', '#f3f4f6']} position={[0, -5, 0]} />
  );
}

function Lights() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <pointLight position={[10, 10, 10]} intensity={0.8} />
      <pointLight position={[-10, -10, -10]} intensity={0.3} />
      <directionalLight
        position={[5, 5, 5]}
        intensity={0.5}
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
      />
    </>
  );
}

export default function Pipeline3D({ pipelineId, runId, onNodeClick }) {
  const [dag, setDag] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPipelineData = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({ id: pipelineId });
        if (runId) params.append('run_id', runId);
        
        const response = await fetch(`/api/editor/pipeline/dag?${params}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Convert 2D positions to 3D with some spacing
        const nodes3D = data.nodes.map((node, index) => ({
          ...node,
          position: [
            node.position.x / 100, // Scale down
            node.position.y / 100,
            (index % 3 - 1) * 2    // Add Z-axis variation
          ]
        }));

        setDag({ ...data, nodes: nodes3D });
        setError(null);
      } catch (err) {
        console.error('Failed to fetch pipeline data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPipelineData();
  }, [pipelineId, runId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading 3D pipeline...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-red-600 mb-4">Failed to load 3D pipeline</p>
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!dag) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-gray-600">No pipeline data available</p>
      </div>
    );
  }

  return (
    <div className="w-full h-96 border border-gray-200 rounded-lg overflow-hidden">
      <Canvas
        camera={{ position: [10, 5, 10], fov: 50 }}
        shadows
        style={{ background: 'linear-gradient(to bottom, #f8fafc, #e2e8f0)' }}
      >
        <Lights />
        <Grid3D />
        
        {/* Render nodes */}
        {dag.nodes.map(node => (
          <Node3D
            key={node.id}
            position={node.position}
            label={node.label}
            status={node.status}
            metadata={node.metadata}
            onClick={() => onNodeClick && onNodeClick(node)}
          />
        ))}

        {/* Render edges */}
        {dag.edges.map((edge, index) => {
          const fromNode = dag.nodes.find(n => n.id === edge.from);
          const toNode = dag.nodes.find(n => n.id === edge.to);
          
          if (!fromNode || !toNode) return null;
          
          return (
            <Edge3D
              key={index}
              from={fromNode.position}
              to={toNode.position}
              color={edge.style?.color || '#888888'}
            />
          );
        })}

        <OrbitControls 
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          maxPolarAngle={Math.PI / 2}
          minDistance={5}
          maxDistance={50}
        />
      </Canvas>
      
      {/* Controls overlay */}
      <div className="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
        <h3 className="font-medium text-sm text-gray-900 mb-2">3D Pipeline View</h3>
        <div className="text-xs text-gray-600 space-y-1">
          <div>• Drag to rotate</div>
          <div>• Scroll to zoom</div>
          <div>• Click nodes for details</div>
        </div>
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
        <h4 className="font-medium text-xs text-gray-900 mb-2">Status Legend</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Success</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>Error</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded animate-pulse"></div>
            <span>Running</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-500 rounded"></div>
            <span>Pending</span>
          </div>
        </div>
      </div>
    </div>
  );
}
