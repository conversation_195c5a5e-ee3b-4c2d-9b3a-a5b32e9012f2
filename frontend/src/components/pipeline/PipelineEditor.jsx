import React, { useState, useCallback } from 'react';
import { 
  EyeIcon, 
  Cog6ToothIcon, 
  PlayIcon, 
  PauseIcon,
  ArrowPathIcon,
  DocumentArrowDownIcon,
  ChartBarIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import PipelineGraph from './PipelineGraph';
import Pipeline3D from './Pipeline3D';
import PipelineStats from './PipelineStats';
import NodeDetailsPanel from './NodeDetailsPanel';

export default function PipelineEditor({ pipelineId, runId }) {
  const [viewMode, setViewMode] = useState('2d'); // '2d', '3d', 'stats'
  const [selectedNode, setSelectedNode] = useState(null);
  const [showDetailsPanel, setShowDetailsPanel] = useState(false);
  const [realTimeUpdate, setRealTimeUpdate] = useState(true);
  const [layoutDirection, setLayoutDirection] = useState('LR');

  const handleNodeClick = useCallback((node) => {
    setSelectedNode(node);
    setShowDetailsPanel(true);
  }, []);

  const handleCloseDetails = useCallback(() => {
    setShowDetailsPanel(false);
    setSelectedNode(null);
  }, []);

  const handleExport = useCallback(() => {
    // TODO: Implement export functionality
    console.log('Exporting pipeline visualization...');
  }, []);

  const handleRefresh = useCallback(() => {
    // Force refresh by updating a key or calling refresh method
    window.location.reload();
  }, []);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pipeline Visualization</h1>
            <p className="text-sm text-gray-600">
              Pipeline ID: {pipelineId} {runId && `• Run ID: ${runId}`}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('2d')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === '2d'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <EyeIcon className="h-4 w-4 mr-1 inline" />
                2D View
              </button>
              <button
                onClick={() => setViewMode('3d')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === '3d'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <CubeIcon className="h-4 w-4 mr-1 inline" />
                3D View
              </button>
              <button
                onClick={() => setViewMode('stats')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'stats'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <ChartBarIcon className="h-4 w-4 mr-1 inline" />
                Stats
              </button>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setRealTimeUpdate(!realTimeUpdate)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  realTimeUpdate
                    ? 'bg-green-100 text-green-700'
                    : 'bg-gray-100 text-gray-700'
                }`}
                title={realTimeUpdate ? 'Disable real-time updates' : 'Enable real-time updates'}
              >
                {realTimeUpdate ? (
                  <PauseIcon className="h-4 w-4" />
                ) : (
                  <PlayIcon className="h-4 w-4" />
                )}
              </button>
              
              <button
                onClick={handleRefresh}
                className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors"
                title="Refresh pipeline data"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
              
              <button
                onClick={handleExport}
                className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors"
                title="Export visualization"
              >
                <DocumentArrowDownIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Layout Controls for 2D view */}
        {viewMode === '2d' && (
          <div className="mt-4 flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Layout:</label>
            <select
              value={layoutDirection}
              onChange={(e) => setLayoutDirection(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="LR">Left to Right</option>
              <option value="TB">Top to Bottom</option>
              <option value="RL">Right to Left</option>
              <option value="BT">Bottom to Top</option>
            </select>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Visualization Area */}
        <div className="flex-1 p-6">
          <div className="h-full bg-white rounded-lg shadow-sm">
            {viewMode === '2d' && (
              <PipelineGraph
                pipelineId={pipelineId}
                runId={runId}
                onNodeClick={handleNodeClick}
                realTimeUpdate={realTimeUpdate}
                layoutDirection={layoutDirection}
              />
            )}
            
            {viewMode === '3d' && (
              <Pipeline3D
                pipelineId={pipelineId}
                runId={runId}
                onNodeClick={handleNodeClick}
              />
            )}
            
            {viewMode === 'stats' && (
              <PipelineStats
                pipelineId={pipelineId}
                runId={runId}
              />
            )}
          </div>
        </div>

        {/* Details Panel */}
        {showDetailsPanel && selectedNode && (
          <div className="w-96 border-l border-gray-200 bg-white">
            <NodeDetailsPanel
              node={selectedNode}
              onClose={handleCloseDetails}
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-white border-t border-gray-200 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>View: {viewMode.toUpperCase()}</span>
            {realTimeUpdate && (
              <span className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Live Updates
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
