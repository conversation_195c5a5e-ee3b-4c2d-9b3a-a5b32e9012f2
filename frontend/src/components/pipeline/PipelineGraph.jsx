import React, { useEffect, useState, useCallback } from "react";
import React<PERSON>low, {
  addEdge,
  MiniMap,
  Controls,
  Background,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
} from "reactflow";
import { getLayoutedElements } from "../../utils/dagreLayout";
import PipelineNode from "./PipelineNode";
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon, 
  ForwardIcon,
  ClockIcon,
  PlayIcon 
} from "@heroicons/react/24/solid";
import "reactflow/dist/style.css";

const statusIcon = {
  success: <CheckCircleIcon className="h-5 w-5 text-green-400" />,
  error: <XCircleIcon className="h-5 w-5 text-red-400" />,
  unstable: <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />,
  skipped: <ForwardIcon className="h-5 w-5 text-gray-400" />,
  pending: <ClockIcon className="h-5 w-5 text-blue-400" />,
  running: <PlayIcon className="h-5 w-5 text-blue-500 animate-pulse" />,
};

function nodeStyle(status) {
  const baseStyle = {
    padding: "10px",
    borderRadius: "8px",
    border: "2px solid",
    background: "white",
    minWidth: "120px",
    textAlign: "center",
    fontSize: "12px",
    fontWeight: "500",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  };

  switch (status) {
    case "success": 
      return { ...baseStyle, borderColor: "#34d399", backgroundColor: "#ecfdf5" };
    case "error": 
      return { ...baseStyle, borderColor: "#f87171", backgroundColor: "#fef2f2" };
    case "unstable": 
      return { ...baseStyle, borderColor: "#fbbf24", backgroundColor: "#fffbeb" };
    case "skipped": 
      return { ...baseStyle, borderColor: "#d1d5db", backgroundColor: "#f3f4f6" };
    case "pending": 
      return { ...baseStyle, borderColor: "#60a5fa", backgroundColor: "#eff6ff" };
    case "running": 
      return { ...baseStyle, borderColor: "#3b82f6", backgroundColor: "#dbeafe", animation: "pulse 2s infinite" };
    default: 
      return { ...baseStyle, borderColor: "#d1d5db", backgroundColor: "#fff" };
  }
}

const nodeTypes = {
  pipelineNode: PipelineNode,
};

export default function PipelineGraph({ pipelineId, runId, onNodeClick, realTimeUpdate = true }) {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  const fetchPipelineData = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({ id: pipelineId });
      if (runId) params.append('run_id', runId);
      
      const response = await fetch(`/api/editor/pipeline/dag?${params}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Convert DAG to React Flow nodes/edges
      const flowNodes = data.nodes.map(node => ({
        id: node.id,
        type: 'pipelineNode',
        data: {
          label: node.label,
          status: node.status,
          icon: statusIcon[node.status],
          metadata: node.metadata,
          startTime: node.start_time,
          endTime: node.end_time,
          duration: node.duration,
          steps: node.steps,
          environment: node.environment,
          runsOn: node.runs_on,
          onClick: () => onNodeClick && onNodeClick(node),
        },
        position: { x: node.position.x, y: node.position.y },
        style: nodeStyle(node.status),
      }));

      const flowEdges = data.edges.map(edge => ({
        id: edge.id,
        source: edge.from,
        target: edge.to,
        type: 'smoothstep',
        animated: edge.style?.animated || false,
        style: {
          stroke: edge.style?.color || "#9ca3af",
          strokeWidth: edge.style?.width || 2,
          strokeDasharray: edge.style?.dashed ? "5,5" : undefined,
        },
        label: edge.label,
      }));

      // Apply automatic layout if positions are not set
      const hasPositions = flowNodes.some(node => node.position.x !== 0 || node.position.y !== 0);
      if (!hasPositions) {
        const layouted = getLayoutedElements(flowNodes, flowEdges);
        setNodes(layouted.nodes);
        setEdges(layouted.edges);
      } else {
        setNodes(flowNodes);
        setEdges(flowEdges);
      }

      setLastUpdate(new Date());
      setError(null);
    } catch (err) {
      console.error('Failed to fetch pipeline data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [pipelineId, runId, onNodeClick]);

  useEffect(() => {
    fetchPipelineData();
  }, [fetchPipelineData]);

  // Real-time updates
  useEffect(() => {
    if (!realTimeUpdate || !runId) return;

    const interval = setInterval(fetchPipelineData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [fetchPipelineData, realTimeUpdate, runId]);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge({ ...params, animated: true }, eds)),
    [setEdges]
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading pipeline...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load pipeline</p>
          <p className="text-gray-500 text-sm mb-4">{error}</p>
          <button
            onClick={fetchPipelineData}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <ReactFlowProvider>
        <div className="w-full h-96 border border-gray-200 rounded-lg">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            snapToGrid={true}
            snapGrid={[15, 15]}
            connectionLineStyle={{ stroke: "#4f46e5", strokeWidth: 2 }}
            defaultViewport={{ x: 0, y: 0, zoom: 1 }}
            fitView
            attributionPosition="bottom-left"
          >
            <MiniMap 
              nodeColor={(node) => {
                switch (node.data.status) {
                  case 'success': return '#34d399';
                  case 'error': return '#f87171';
                  case 'unstable': return '#fbbf24';
                  case 'running': return '#3b82f6';
                  case 'pending': return '#60a5fa';
                  default: return '#d1d5db';
                }
              }}
              maskColor="rgba(0, 0, 0, 0.1)"
            />
            <Controls />
            <Background color="#f3f4f6" gap={16} />
          </ReactFlow>
        </div>
      </ReactFlowProvider>
      
      {lastUpdate && (
        <div className="mt-2 text-xs text-gray-500 text-right">
          Last updated: {lastUpdate.toLocaleTimeString()}
        </div>
      )}
    </div>
  );
}
