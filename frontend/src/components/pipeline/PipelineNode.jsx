import React from 'react';
import { Hand<PERSON>, Position } from 'reactflow';
import { 
  ClockIcon, 
  ServerIcon, 
  TagIcon,
  CalendarIcon 
} from '@heroicons/react/24/outline';

export default function PipelineNode({ data }) {
  const { 
    label, 
    status, 
    icon, 
    metadata, 
    startTime, 
    endTime, 
    duration, 
    steps, 
    environment, 
    runsOn, 
    onClick 
  } = data;

  const formatDuration = (ms) => {
    if (!ms) return '';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatTime = (timeString) => {
    if (!timeString) return '';
    return new Date(timeString).toLocaleTimeString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'unstable': return 'text-yellow-600';
      case 'running': return 'text-blue-600';
      case 'pending': return 'text-gray-600';
      case 'skipped': return 'text-gray-400';
      default: return 'text-gray-600';
    }
  };

  return (
    <div 
      className="pipeline-node bg-white border-2 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      <Handle type="target" position={Position.Left} />
      
      <div className="p-3 min-w-[160px]">
        {/* Header with icon and label */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {icon}
            <span className="font-medium text-sm text-gray-900 truncate">
              {label}
            </span>
          </div>
          <span className={`text-xs font-medium uppercase ${getStatusColor(status)}`}>
            {status}
          </span>
        </div>

        {/* Environment badge */}
        {environment && (
          <div className="mb-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              <TagIcon className="h-3 w-3 mr-1" />
              {environment}
            </span>
          </div>
        )}

        {/* Runner info */}
        {runsOn && (
          <div className="flex items-center text-xs text-gray-500 mb-2">
            <ServerIcon className="h-3 w-3 mr-1" />
            {runsOn}
          </div>
        )}

        {/* Timing info */}
        <div className="space-y-1">
          {startTime && (
            <div className="flex items-center text-xs text-gray-500">
              <CalendarIcon className="h-3 w-3 mr-1" />
              Started: {formatTime(startTime)}
            </div>
          )}
          
          {duration && (
            <div className="flex items-center text-xs text-gray-500">
              <ClockIcon className="h-3 w-3 mr-1" />
              Duration: {formatDuration(duration)}
            </div>
          )}
        </div>

        {/* Steps progress */}
        {steps && steps.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            <div className="text-xs text-gray-500 mb-1">
              Steps: {steps.filter(s => s.status === 'success').length}/{steps.length}
            </div>
            <div className="flex space-x-1">
              {steps.slice(0, 5).map((step, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    step.status === 'success' ? 'bg-green-400' :
                    step.status === 'error' ? 'bg-red-400' :
                    step.status === 'running' ? 'bg-blue-400 animate-pulse' :
                    'bg-gray-300'
                  }`}
                  title={`${step.name}: ${step.status}`}
                />
              ))}
              {steps.length > 5 && (
                <span className="text-xs text-gray-400">+{steps.length - 5}</span>
              )}
            </div>
          </div>
        )}

        {/* Running animation */}
        {status === 'running' && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div className="bg-blue-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        )}
      </div>

      <Handle type="source" position={Position.Right} />
    </div>
  );
}
