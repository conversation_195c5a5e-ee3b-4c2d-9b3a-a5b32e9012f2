'use client';

import { useState, useEffect } from 'react';
import { 
  FolderIcon, 
  DocumentIcon, 
  ArrowDownTrayIcon,
  EyeIcon,
  TrashIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

import { Artifact, Job } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';

interface ArtifactManagerProps {
  job: Job | null;
  className?: string;
  showPreview?: boolean;
  allowDelete?: boolean;
}

export function ArtifactManager({
  job,
  className,
  showPreview = true,
  allowDelete = false,
}: ArtifactManagerProps) {
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [previewContent, setPreviewContent] = useState<string | null>(null);

  // Load artifacts when job changes
  useEffect(() => {
    if (!job) {
      setArtifacts([]);
      return;
    }

    const loadArtifacts = async () => {
      setIsLoading(true);
      try {
        const jobArtifacts = await jobService.getJobArtifacts(job.id);
        setArtifacts(jobArtifacts);
      } catch (error) {
        console.error('Failed to load artifacts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadArtifacts();
  }, [job]);

  // Filter artifacts based on search term
  const filteredArtifacts = artifacts.filter(artifact =>
    artifact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    artifact.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDownload = async (artifact: Artifact) => {
    try {
      const blob = await jobService.downloadArtifact(artifact.id);
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = artifact.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to download artifact:', error);
    }
  };

  const handlePreview = async (artifact: Artifact) => {
    if (!showPreview) return;

    setSelectedArtifact(artifact);
    
    // Only preview text-based files
    const textTypes = ['text/plain', 'application/json', 'text/html', 'text/css', 'text/javascript'];
    if (!textTypes.includes(artifact.type) && !artifact.name.match(/\.(txt|json|html|css|js|md|yml|yaml|xml)$/i)) {
      setPreviewContent('Preview not available for this file type');
      return;
    }

    try {
      const blob = await jobService.downloadArtifact(artifact.id);
      if (blob) {
        const text = await blob.text();
        setPreviewContent(text);
      }
    } catch (error) {
      console.error('Failed to preview artifact:', error);
      setPreviewContent('Failed to load preview');
    }
  };

  const handleDelete = async (artifact: Artifact) => {
    if (!allowDelete) return;
    
    if (confirm(`Are you sure you want to delete "${artifact.name}"?`)) {
      try {
        // This would call an API to delete the artifact
        console.log('Delete artifact:', artifact.id);
        setArtifacts(prev => prev.filter(a => a.id !== artifact.id));
      } catch (error) {
        console.error('Failed to delete artifact:', error);
      }
    }
  };

  const getFileIcon = (artifact: Artifact) => {
    const extension = artifact.name.split('.').pop()?.toLowerCase();
    
    if (artifact.type.startsWith('image/')) {
      return '🖼️';
    } else if (artifact.type.startsWith('text/') || ['txt', 'md', 'json', 'yml', 'yaml'].includes(extension || '')) {
      return '📄';
    } else if (['zip', 'tar', 'gz', 'rar'].includes(extension || '')) {
      return '📦';
    } else if (['pdf'].includes(extension || '')) {
      return '📕';
    } else if (['log'].includes(extension || '')) {
      return '📋';
    } else {
      return '📁';
    }
  };

  const formatFileSize = (bytes: number) => {
    return jobService.formatFileSize(bytes);
  };

  if (!job) {
    return (
      <div className={cn('flex items-center justify-center h-full bg-gray-50', className)}>
        <p className="text-gray-500">Select a job to view artifacts</p>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full bg-white border border-gray-200 rounded-lg', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">
          Artifacts ({filteredArtifacts.length})
        </h3>
        
        {artifacts.length > 0 && (
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search artifacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredArtifacts.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <FolderIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">
                {artifacts.length === 0 ? 'No artifacts available' : 'No artifacts match your search'}
              </p>
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-2">
            {filteredArtifacts.map(artifact => (
              <div
                key={artifact.id}
                className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <span className="text-2xl">{getFileIcon(artifact)}</span>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{artifact.name}</p>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>{formatFileSize(artifact.size)}</span>
                      <span>•</span>
                      <span>{artifact.type}</span>
                      <span>•</span>
                      <span>{new Date(artifact.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {showPreview && (
                    <button
                      onClick={() => handlePreview(artifact)}
                      className="p-2 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100"
                      title="Preview"
                    >
                      <EyeIcon className="w-4 h-4" />
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleDownload(artifact)}
                    className="p-2 text-blue-600 hover:text-blue-700 rounded-md hover:bg-blue-50"
                    title="Download"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4" />
                  </button>
                  
                  {allowDelete && (
                    <button
                      onClick={() => handleDelete(artifact)}
                      className="p-2 text-red-600 hover:text-red-700 rounded-md hover:bg-red-50"
                      title="Delete"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Preview Modal */}
      {selectedArtifact && showPreview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[80vh] w-full mx-4 flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedArtifact.name}
              </h3>
              <button
                onClick={() => {
                  setSelectedArtifact(null);
                  setPreviewContent(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>
            
            {/* Modal Content */}
            <div className="flex-1 overflow-auto p-4">
              {previewContent ? (
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                  {previewContent}
                </pre>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            
            {/* Modal Footer */}
            <div className="flex items-center justify-end space-x-2 p-4 border-t border-gray-200">
              <button
                onClick={() => handleDownload(selectedArtifact)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Download
              </button>
              <button
                onClick={() => {
                  setSelectedArtifact(null);
                  setPreviewContent(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
