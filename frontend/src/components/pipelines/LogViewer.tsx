'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { MagnifyingGlassIcon, ArrowDownIcon, XMarkIcon } from '@heroicons/react/24/outline';
import AutoSizer from 'react-virtualized-auto-sizer';
import { FixedSizeList as List } from 'react-window';

import { LogEntry, Job } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';
import { useWebSocket } from '@/services/websocket';

interface LogViewerProps {
  job: Job | null;
  className?: string;
  maxHeight?: number;
  showSearch?: boolean;
  showControls?: boolean;
  autoScroll?: boolean;
}

interface LogLineProps {
  index: number;
  style: React.CSSProperties;
  data: {
    logs: LogEntry[];
    searchTerm: string;
    highlightedIndex: number;
  };
}

function LogLine({ index, style, data }: LogLineProps) {
  const { logs, searchTerm, highlightedIndex } = data;
  const log = logs[index];
  
  if (!log) return null;

  const isHighlighted = index === highlightedIndex;
  const matchesSearch = searchTerm && log.message.toLowerCase().includes(searchTerm.toLowerCase());

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const highlightText = (text: string, term: string) => {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, i) => 
      regex.test(part) ? (
        <mark key={i} className="bg-yellow-200 text-yellow-900">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div
      style={style}
      className={cn(
        'flex items-start space-x-3 px-4 py-1 text-sm font-mono border-b border-gray-100',
        'hover:bg-gray-50 transition-colors',
        isHighlighted && 'bg-blue-50 border-blue-200',
        matchesSearch && 'bg-yellow-50'
      )}
    >
      {/* Timestamp */}
      <span className="text-gray-500 text-xs whitespace-nowrap">
        {formatTimestamp(log.timestamp)}
      </span>
      
      {/* Log level */}
      <span className={cn(
        'text-xs font-medium uppercase whitespace-nowrap w-12',
        jobService.getLogLevelColor(log.level)
      )}>
        {log.level}
      </span>
      
      {/* Message */}
      <span className="flex-1 whitespace-pre-wrap break-words">
        {searchTerm ? highlightText(log.message, searchTerm) : log.message}
      </span>
    </div>
  );
}

export function LogViewer({
  job,
  className,
  maxHeight = 400,
  showSearch = true,
  showControls = true,
  autoScroll = true,
}: LogViewerProps) {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(autoScroll);
  const [logLevel, setLogLevel] = useState<string>('all');
  
  const listRef = useRef<List>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { subscribeToLogs, unsubscribeFromLogs, subscribe, unsubscribe } = useWebSocket();

  // Filter logs based on search term and log level
  const filteredLogs = logs.filter(log => {
    const matchesLevel = logLevel === 'all' || log.level.toLowerCase() === logLevel;
    const matchesSearch = !searchTerm || log.message.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesLevel && matchesSearch;
  });

  // Load initial logs when job changes
  useEffect(() => {
    if (!job) {
      setLogs([]);
      return;
    }

    const loadLogs = async () => {
      setIsLoading(true);
      try {
        const jobLogs = await jobService.getJobLogs(job.id, { limit: 1000 });
        setLogs(jobLogs);
      } catch (error) {
        console.error('Failed to load logs:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLogs();
  }, [job]);

  // Subscribe to real-time log updates
  useEffect(() => {
    if (!job) return;

    const handleLogEntry = (entry: LogEntry) => {
      if (entry.jobId === job.id) {
        setLogs(prev => [...prev, entry]);
      }
    };

    const handleLogStream = (data: { jobId: string; entries: LogEntry[] }) => {
      if (data.jobId === job.id) {
        setLogs(prev => [...prev, ...data.entries]);
      }
    };

    subscribe('log:entry', handleLogEntry);
    subscribe('log:stream', handleLogStream);
    subscribeToLogs(job.id);

    return () => {
      unsubscribe('log:entry', handleLogEntry);
      unsubscribe('log:stream', handleLogStream);
      unsubscribeFromLogs(job.id);
    };
  }, [job, subscribe, unsubscribe, subscribeToLogs, unsubscribeFromLogs]);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (isAutoScrollEnabled && listRef.current && filteredLogs.length > 0) {
      listRef.current.scrollToItem(filteredLogs.length - 1, 'end');
    }
  }, [filteredLogs.length, isAutoScrollEnabled]);

  // Search functionality
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setHighlightedIndex(-1);
    
    if (term && filteredLogs.length > 0) {
      const firstMatch = filteredLogs.findIndex(log => 
        log.message.toLowerCase().includes(term.toLowerCase())
      );
      if (firstMatch !== -1) {
        setHighlightedIndex(firstMatch);
        listRef.current?.scrollToItem(firstMatch, 'center');
      }
    }
  }, [filteredLogs]);

  const findNext = useCallback(() => {
    if (!searchTerm || filteredLogs.length === 0) return;
    
    const nextIndex = filteredLogs.findIndex((log, index) => 
      index > highlightedIndex && 
      log.message.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (nextIndex !== -1) {
      setHighlightedIndex(nextIndex);
      listRef.current?.scrollToItem(nextIndex, 'center');
    } else {
      // Wrap to beginning
      const firstMatch = filteredLogs.findIndex(log => 
        log.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (firstMatch !== -1) {
        setHighlightedIndex(firstMatch);
        listRef.current?.scrollToItem(firstMatch, 'center');
      }
    }
  }, [searchTerm, filteredLogs, highlightedIndex]);

  const scrollToBottom = () => {
    if (listRef.current && filteredLogs.length > 0) {
      listRef.current.scrollToItem(filteredLogs.length - 1, 'end');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  if (!job) {
    return (
      <div className={cn('flex items-center justify-center h-full bg-gray-50', className)}>
        <p className="text-gray-500">Select a job to view logs</p>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full bg-white border border-gray-200 rounded-lg', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <h3 className="font-semibold text-gray-900">Logs: {job.name}</h3>
          <span className={cn(
            'px-2 py-1 rounded-full text-xs font-medium',
            jobService.getStatusColorClass(job.status.toLowerCase())
          )}>
            {job.status}
          </span>
        </div>
        
        {showControls && (
          <div className="flex items-center space-x-2">
            {/* Log level filter */}
            <select
              value={logLevel}
              onChange={(e) => setLogLevel(e.target.value)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="all">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
            
            {/* Auto-scroll toggle */}
            <label className="flex items-center text-sm">
              <input
                type="checkbox"
                checked={isAutoScrollEnabled}
                onChange={(e) => setIsAutoScrollEnabled(e.target.checked)}
                className="mr-1"
              />
              Auto-scroll
            </label>
            
            {/* Scroll to bottom */}
            <button
              onClick={scrollToBottom}
              className="p-1 text-gray-500 hover:text-gray-700"
              title="Scroll to bottom"
            >
              <ArrowDownIcon className="w-4 h-4" />
            </button>
            
            {/* Clear logs */}
            <button
              onClick={clearLogs}
              className="p-1 text-gray-500 hover:text-gray-700"
              title="Clear logs"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Search */}
      {showSearch && (
        <div className="p-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            {searchTerm && (
              <button
                onClick={findNext}
                className="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Next
              </button>
            )}
          </div>
        </div>
      )}

      {/* Log content */}
      <div ref={containerRef} className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredLogs.length > 0 ? (
          <AutoSizer>
            {({ height, width }) => (
              <List
                ref={listRef}
                height={height}
                width={width}
                itemCount={filteredLogs.length}
                itemSize={32}
                itemData={{
                  logs: filteredLogs,
                  searchTerm,
                  highlightedIndex,
                }}
              >
                {LogLine}
              </List>
            )}
          </AutoSizer>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>No logs available</p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-500">
        {filteredLogs.length} log entries
        {searchTerm && ` (filtered by "${searchTerm}")`}
        {logLevel !== 'all' && ` (${logLevel} level)`}
      </div>
    </div>
  );
}
