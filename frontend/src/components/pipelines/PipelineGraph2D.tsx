'use client';

import { useCallback, useEffect, useState, useMemo } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  NodeTypes,
  EdgeTypes,
  ReactFlowProvider,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { PipelineRun, Job, JobStatus } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';

interface PipelineGraph2DProps {
  pipelineRun: PipelineRun;
  onNodeClick?: (job: Job) => void;
  onNodeDoubleClick?: (job: Job) => void;
  className?: string;
  showMinimap?: boolean;
  showControls?: boolean;
  animated?: boolean;
}

// Custom Job Node Component
function JobNode({ data }: { data: any }) {
  const { job, isSelected, onClick, onDoubleClick } = data;
  const status = job.status.toLowerCase() as JobStatus;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-500 border-green-600';
      case 'failed':
        return 'bg-red-500 border-red-600';
      case 'running':
        return 'bg-blue-500 border-blue-600';
      case 'pending':
      case 'queued':
        return 'bg-yellow-500 border-yellow-600';
      case 'cancelled':
        return 'bg-gray-500 border-gray-600';
      case 'skipped':
        return 'bg-purple-500 border-purple-600';
      default:
        return 'bg-gray-300 border-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'failed':
        return '❌';
      case 'running':
        return '🔄';
      case 'pending':
      case 'queued':
        return '⏳';
      case 'cancelled':
        return '🚫';
      case 'skipped':
        return '⏭️';
      default:
        return '❓';
    }
  };

  const duration = jobService.formatDuration(job.startedAt, job.finishedAt);

  return (
    <div
      className={cn(
        'px-4 py-3 rounded-lg border-2 cursor-pointer transition-all duration-200',
        'hover:shadow-lg hover:scale-105',
        'min-w-[200px] max-w-[300px]',
        getStatusColor(status),
        isSelected && 'ring-2 ring-blue-400 ring-offset-2',
        status === 'RUNNING' && 'animate-pulse'
      )}
      onClick={() => onClick?.(job)}
      onDoubleClick={() => onDoubleClick?.(job)}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon(status)}</span>
          <h3 className="font-semibold text-white text-sm truncate">{job.name}</h3>
        </div>
        <span className="text-xs text-white/80 uppercase font-medium">
          {status}
        </span>
      </div>
      
      <div className="text-xs text-white/90 space-y-1">
        <div className="flex justify-between">
          <span>Duration:</span>
          <span className="font-mono">{duration}</span>
        </div>
        {job.environment && (
          <div className="flex justify-between">
            <span>Environment:</span>
            <span className="font-mono">{job.environment.name}</span>
          </div>
        )}
        {job.startedAt && (
          <div className="flex justify-between">
            <span>Started:</span>
            <span className="font-mono">
              {new Date(job.startedAt).toLocaleTimeString()}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

// Custom Edge Component
function CustomEdge() {
  return null; // Use default edge rendering
}

const nodeTypes: NodeTypes = {
  job: JobNode,
};

const edgeTypes: EdgeTypes = {
  custom: CustomEdge,
};

function PipelineGraph2DInner({
  pipelineRun,
  onNodeClick,
  onNodeDoubleClick,
  className,
  showMinimap = false,
  showControls = true,
  animated = true,
}: PipelineGraph2DProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const { fitView } = useReactFlow();

  // Generate nodes and edges from pipeline run
  const generateGraph = useCallback(() => {
    if (!pipelineRun.jobs) return;

    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];
    const jobMap = new Map<string, Job>();

    // Create job map for easy lookup
    pipelineRun.jobs.forEach(job => {
      jobMap.set(job.name, job);
    });

    // Create nodes
    pipelineRun.jobs.forEach((job, index) => {
      const node: Node = {
        id: job.id,
        type: 'job',
        position: { x: (index % 4) * 300, y: Math.floor(index / 4) * 150 },
        data: {
          job,
          isSelected: selectedNodeId === job.id,
          onClick: (clickedJob: Job) => {
            setSelectedNodeId(clickedJob.id);
            onNodeClick?.(clickedJob);
          },
          onDoubleClick: onNodeDoubleClick,
        },
        draggable: true,
      };
      newNodes.push(node);
    });

    // Create edges based on job dependencies
    pipelineRun.jobs.forEach(job => {
      if (job.config?.needs) {
        job.config.needs.forEach((dependency: string) => {
          const dependencyJob = jobMap.get(dependency);
          if (dependencyJob) {
            const edge: Edge = {
              id: `${dependencyJob.id}-${job.id}`,
              source: dependencyJob.id,
              target: job.id,
              type: 'smoothstep',
              animated: animated && job.status === 'RUNNING',
              style: {
                stroke: '#6b7280',
                strokeWidth: 2,
              },
              markerEnd: {
                type: 'arrowclosed' as any,
                color: '#6b7280',
              },
            };
            newEdges.push(edge);
          }
        });
      }
    });

    setNodes(newNodes);
    setEdges(newEdges);

    // Auto-fit view after a short delay
    setTimeout(() => {
      fitView({ padding: 0.1 });
    }, 100);
  }, [pipelineRun, selectedNodeId, onNodeClick, onNodeDoubleClick, animated, fitView]);

  // Regenerate graph when pipeline run changes
  useEffect(() => {
    generateGraph();
  }, [generateGraph]);

  // Update node selection
  useEffect(() => {
    setNodes(nodes => 
      nodes.map(node => ({
        ...node,
        data: {
          ...node.data,
          isSelected: selectedNodeId === node.id,
        },
      }))
    );
  }, [selectedNodeId, setNodes]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClickHandler = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNodeId(node.id);
    onNodeClick?.(node.data.job);
  }, [onNodeClick]);

  return (
    <div className={cn('h-full w-full', className)}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClickHandler}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-left"
        className="bg-gray-50"
      >
        {showControls && (
          <Controls
            className="bg-white border border-gray-200 rounded-lg shadow-sm"
            showInteractive={false}
          />
        )}
        
        {showMinimap && (
          <MiniMap
            className="bg-white border border-gray-200 rounded-lg shadow-sm"
            nodeColor={(node) => {
              const status = node.data.job.status.toLowerCase();
              switch (status) {
                case 'success': return '#10b981';
                case 'failed': return '#ef4444';
                case 'running': return '#3b82f6';
                case 'pending':
                case 'queued': return '#f59e0b';
                case 'cancelled': return '#6b7280';
                case 'skipped': return '#8b5cf6';
                default: return '#e5e7eb';
              }
            }}
            maskColor="rgba(0, 0, 0, 0.1)"
          />
        )}
        
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="#e5e7eb"
        />
      </ReactFlow>
    </div>
  );
}

export function PipelineGraph2D(props: PipelineGraph2DProps) {
  return (
    <ReactFlowProvider>
      <PipelineGraph2DInner {...props} />
    </ReactFlowProvider>
  );
}
