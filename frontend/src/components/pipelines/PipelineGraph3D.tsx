'use client';

import { Suspense, useRef, useState, useEffect, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Text, Html, Line } from '@react-three/drei';
import * as THREE from 'three';

import { PipelineRun, Job, JobStatus } from '@/types/pipelines';
import { jobService } from '@/services/jobs';
import { cn } from '@/utils/cn';

interface PipelineGraph3DProps {
  pipelineRun: PipelineRun;
  onNodeClick?: (job: Job) => void;
  onNodeDoubleClick?: (job: Job) => void;
  className?: string;
  animated?: boolean;
}

interface JobNodeProps {
  job: Job;
  position: [number, number, number];
  isSelected: boolean;
  onClick: (job: Job) => void;
  onDoubleClick: (job: Job) => void;
  animated: boolean;
}

function JobNode({ job, position, isSelected, onClick, onDoubleClick, animated }: JobNodeProps) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);
  const status = job.status.toLowerCase() as JobStatus;

  // Animation for running jobs
  useFrame((state) => {
    if (meshRef.current && animated && status === 'RUNNING') {
      meshRef.current.rotation.y += 0.02;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'success':
        return '#10b981';
      case 'failed':
        return '#ef4444';
      case 'running':
        return '#3b82f6';
      case 'pending':
      case 'queued':
        return '#f59e0b';
      case 'cancelled':
        return '#6b7280';
      case 'skipped':
        return '#8b5cf6';
      default:
        return '#e5e7eb';
    }
  };

  const getStatusEmoji = (status: string): string => {
    switch (status) {
      case 'success':
        return '✅';
      case 'failed':
        return '❌';
      case 'running':
        return '🔄';
      case 'pending':
      case 'queued':
        return '⏳';
      case 'cancelled':
        return '🚫';
      case 'skipped':
        return '⏭️';
      default:
        return '❓';
    }
  };

  const scale = isSelected ? 1.3 : hovered ? 1.1 : 1;
  const color = getStatusColor(status);

  return (
    <group position={position}>
      {/* Main job sphere */}
      <mesh
        ref={meshRef}
        scale={[scale, scale, scale]}
        onClick={() => onClick(job)}
        onDoubleClick={() => onDoubleClick(job)}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <sphereGeometry args={[0.5, 32, 32]} />
        <meshStandardMaterial
          color={color}
          emissive={isSelected ? color : '#000000'}
          emissiveIntensity={isSelected ? 0.3 : 0}
          roughness={0.3}
          metalness={0.1}
        />
      </mesh>

      {/* Selection ring */}
      {isSelected && (
        <mesh rotation={[Math.PI / 2, 0, 0]}>
          <ringGeometry args={[0.7, 0.8, 32]} />
          <meshBasicMaterial color="#3b82f6" transparent opacity={0.6} />
        </mesh>
      )}

      {/* Job label */}
      <Text
        position={[0, -0.8, 0]}
        fontSize={0.2}
        color="#374151"
        anchorX="center"
        anchorY="middle"
        maxWidth={2}
        textAlign="center"
      >
        {job.name}
      </Text>

      {/* Status emoji */}
      <Text
        position={[0, 0.7, 0]}
        fontSize={0.3}
        anchorX="center"
        anchorY="middle"
      >
        {getStatusEmoji(status)}
      </Text>

      {/* Hover tooltip */}
      {hovered && (
        <Html position={[0, 1, 0]} center>
          <div className="bg-white p-3 rounded-lg shadow-lg border max-w-xs">
            <h3 className="font-semibold text-gray-900 mb-2">{job.name}</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className={cn('font-medium', jobService.getStatusColorClass(status))}>
                  {status.toUpperCase()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Duration:</span>
                <span className="font-mono">
                  {jobService.formatDuration(job.startedAt, job.finishedAt)}
                </span>
              </div>
              {job.environment && (
                <div className="flex justify-between">
                  <span>Environment:</span>
                  <span className="font-mono">{job.environment.name}</span>
                </div>
              )}
            </div>
          </div>
        </Html>
      )}
    </group>
  );
}

interface DependencyLineProps {
  start: [number, number, number];
  end: [number, number, number];
  animated: boolean;
  status: JobStatus;
}

function DependencyLine({ start, end, animated, status }: DependencyLineProps) {
  const points = useMemo(() => [
    new THREE.Vector3(...start),
    new THREE.Vector3(...end),
  ], [start, end]);

  const color = status === 'RUNNING' ? '#3b82f6' : '#6b7280';
  const opacity = status === 'RUNNING' ? 0.8 : 0.4;

  return (
    <Line
      points={points}
      color={color}
      lineWidth={2}
      transparent
      opacity={opacity}
      dashed={animated && status === 'RUNNING'}
      dashSize={0.1}
      gapSize={0.05}
    />
  );
}

function Scene({ pipelineRun, onNodeClick, onNodeDoubleClick, animated }: {
  pipelineRun: PipelineRun;
  onNodeClick?: (job: Job) => void;
  onNodeDoubleClick?: (job: Job) => void;
  animated: boolean;
}) {
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const { camera } = useThree();

  // Position jobs in 3D space
  const jobPositions = useMemo(() => {
    if (!pipelineRun.jobs) return new Map();

    const positions = new Map<string, [number, number, number]>();
    const jobsPerRow = 4;
    const spacing = 3;

    pipelineRun.jobs.forEach((job, index) => {
      const x = (index % jobsPerRow) * spacing - (jobsPerRow - 1) * spacing / 2;
      const y = Math.floor(index / jobsPerRow) * spacing;
      const z = 0;
      positions.set(job.id, [x, y, z]);
    });

    return positions;
  }, [pipelineRun.jobs]);

  // Create dependency lines
  const dependencyLines = useMemo(() => {
    if (!pipelineRun.jobs) return [];

    const lines: Array<{
      id: string;
      start: [number, number, number];
      end: [number, number, number];
      status: JobStatus;
    }> = [];

    const jobMap = new Map<string, Job>();
    pipelineRun.jobs.forEach(job => {
      jobMap.set(job.name, job);
    });

    pipelineRun.jobs.forEach(job => {
      if (job.config?.needs) {
        job.config.needs.forEach((dependency: string) => {
          const dependencyJob = jobMap.get(dependency);
          if (dependencyJob) {
            const startPos = jobPositions.get(dependencyJob.id);
            const endPos = jobPositions.get(job.id);
            
            if (startPos && endPos) {
              lines.push({
                id: `${dependencyJob.id}-${job.id}`,
                start: startPos,
                end: endPos,
                status: job.status,
              });
            }
          }
        });
      }
    });

    return lines;
  }, [pipelineRun.jobs, jobPositions]);

  const handleNodeClick = (job: Job) => {
    setSelectedJobId(job.id);
    onNodeClick?.(job);
  };

  const handleNodeDoubleClick = (job: Job) => {
    onNodeDoubleClick?.(job);
  };

  // Auto-fit camera to show all nodes
  useEffect(() => {
    if (pipelineRun.jobs && pipelineRun.jobs.length > 0) {
      const positions = Array.from(jobPositions.values());
      if (positions.length > 0) {
        const box = new THREE.Box3();
        positions.forEach(pos => {
          box.expandByPoint(new THREE.Vector3(...pos));
        });
        
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        
        camera.position.set(center.x, center.y + maxDim, center.z + maxDim * 1.5);
        camera.lookAt(center);
      }
    }
  }, [pipelineRun.jobs, jobPositions, camera]);

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={0.8} />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />

      {/* Job nodes */}
      {pipelineRun.jobs?.map(job => {
        const position = jobPositions.get(job.id);
        if (!position) return null;

        return (
          <JobNode
            key={job.id}
            job={job}
            position={position}
            isSelected={selectedJobId === job.id}
            onClick={handleNodeClick}
            onDoubleClick={handleNodeDoubleClick}
            animated={animated}
          />
        );
      })}

      {/* Dependency lines */}
      {dependencyLines.map(line => (
        <DependencyLine
          key={line.id}
          start={line.start}
          end={line.end}
          animated={animated}
          status={line.status}
        />
      ))}

      {/* Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        dampingFactor={0.05}
        rotateSpeed={0.5}
        zoomSpeed={0.8}
        panSpeed={0.8}
      />
    </>
  );
}

export function PipelineGraph3D({
  pipelineRun,
  onNodeClick,
  onNodeDoubleClick,
  className,
  animated = true,
}: PipelineGraph3DProps) {
  return (
    <div className={cn('h-full w-full', className)}>
      <Canvas
        camera={{ position: [0, 5, 10], fov: 60 }}
        style={{ background: 'linear-gradient(to bottom, #f8fafc, #e2e8f0)' }}
      >
        <Suspense fallback={null}>
          <Scene
            pipelineRun={pipelineRun}
            onNodeClick={onNodeClick}
            onNodeDoubleClick={onNodeDoubleClick}
            animated={animated}
          />
        </Suspense>
      </Canvas>
    </div>
  );
}
