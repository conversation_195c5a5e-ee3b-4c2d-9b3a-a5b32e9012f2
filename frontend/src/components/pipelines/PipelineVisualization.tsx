'use client';

import { useState, useEffect } from 'react';
import { PipelineRun, Job, StepConfig, ViewMode } from '@/types/pipelines';
import { ResizablePanes } from '@/components/ui/ResizablePanes';
import { ViewToggleBar, useViewToggle } from './ViewToggleBar';
import { PipelineGraph2D } from './PipelineGraph2D';
import { PipelineGraph3D } from './PipelineGraph3D';
import { StageListView } from './StageListView';
import { LogViewer } from './LogViewer';
import { StepDetailsPanel } from './StepDetailsPanel';
import { useWebSocket } from '@/services/websocket';
import { cn } from '@/utils/cn';

interface PipelineVisualizationProps {
  pipelineRun: PipelineRun;
  className?: string;
  defaultView?: ViewMode;
  showMinimap?: boolean;
  showControls?: boolean;
  animated?: boolean;
}

export function PipelineVisualization({
  pipelineRun,
  className,
  defaultView = '2d',
  showMinimap = false,
  showControls = true,
  animated = true,
}: PipelineVisualizationProps) {
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [selectedStep, setSelectedStep] = useState<{
    step: StepConfig;
    index: number;
  } | null>(null);
  const [panesSizes, setPanesSizes] = useState([60, 40]); // Main view, side panel
  const [bottomPaneSize, setBottomPaneSize] = useState(40); // Logs height percentage
  
  const { currentView, changeView } = useViewToggle(defaultView);
  const { subscribeToPipeline, unsubscribeFromPipeline } = useWebSocket();

  // Subscribe to pipeline updates
  useEffect(() => {
    if (pipelineRun.pipelineId) {
      subscribeToPipeline(pipelineRun.pipelineId);
      return () => unsubscribeFromPipeline(pipelineRun.pipelineId);
    }
  }, [pipelineRun.pipelineId, subscribeToPipeline, unsubscribeFromPipeline]);

  const handleJobClick = (job: Job) => {
    setSelectedJob(job);
    setSelectedStep(null); // Clear step selection when job changes
  };

  const handleJobDoubleClick = (job: Job) => {
    // Double-click could open job in new tab or modal
    console.log('Double-clicked job:', job.name);
  };

  const handleStepClick = (job: Job, step: StepConfig, stepIndex: number) => {
    setSelectedJob(job);
    setSelectedStep({ step, index: stepIndex });
  };

  const renderMainView = () => {
    const commonProps = {
      pipelineRun,
      onNodeClick: handleJobClick,
      onNodeDoubleClick: handleJobDoubleClick,
      animated,
    };

    switch (currentView) {
      case '2d':
        return (
          <PipelineGraph2D
            {...commonProps}
            showMinimap={showMinimap}
            showControls={showControls}
            className="h-full"
          />
        );
      
      case '3d':
        return (
          <PipelineGraph3D
            {...commonProps}
            className="h-full"
          />
        );
      
      case 'list':
        return (
          <StageListView
            pipelineRun={pipelineRun}
            onJobClick={handleJobClick}
            onStepClick={handleStepClick}
            className="h-full"
            autoExpand={true}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={cn('h-full flex flex-col bg-gray-50', className)}>
      {/* Header with view controls */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              {pipelineRun.pipeline?.name || 'Pipeline'}
            </h1>
            <p className="text-sm text-gray-500">
              Run #{pipelineRun.number} • {pipelineRun.status}
              {pipelineRun.startedAt && (
                <span className="ml-2">
                  Started {new Date(pipelineRun.startedAt).toLocaleString()}
                </span>
              )}
            </p>
          </div>
        </div>
        
        <ViewToggleBar
          currentView={currentView}
          onViewChange={changeView}
          showLabels={true}
        />
      </div>

      {/* Main content area */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanes
          direction="horizontal"
          defaultSizes={panesSizes}
          minSizes={[30, 20]}
          maxSizes={[80, 70]}
          onResize={setPanesSizes}
          className="h-full"
        >
          {/* Main visualization pane */}
          <div className="h-full">
            <ResizablePanes
              direction="vertical"
              defaultSizes={[100 - bottomPaneSize, bottomPaneSize]}
              minSizes={[40, 20]}
              maxSizes={[80, 60]}
              onResize={(sizes) => setBottomPaneSize(sizes[1])}
              className="h-full"
            >
              {/* Top: Main view */}
              <div className="h-full bg-white border border-gray-200 rounded-lg m-2">
                {renderMainView()}
              </div>

              {/* Bottom: Logs */}
              <div className="h-full m-2">
                <LogViewer
                  job={selectedJob}
                  className="h-full"
                  showSearch={true}
                  showControls={true}
                  autoScroll={true}
                />
              </div>
            </ResizablePanes>
          </div>

          {/* Right side panel: Job/Step details */}
          <div className="h-full m-2">
            <StepDetailsPanel
              job={selectedJob}
              selectedStep={selectedStep}
              className="h-full"
            />
          </div>
        </ResizablePanes>
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-4 py-2 bg-white border-t border-gray-200 text-sm text-gray-600">
        <div className="flex items-center space-x-4">
          <span>
            {pipelineRun.jobs?.length || 0} jobs
          </span>
          <span>
            View: {currentView.toUpperCase()}
          </span>
          {selectedJob && (
            <span>
              Selected: {selectedJob.name}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          {pipelineRun.finishedAt && pipelineRun.startedAt && (
            <span>
              Total duration: {
                Math.round(
                  (new Date(pipelineRun.finishedAt).getTime() - 
                   new Date(pipelineRun.startedAt).getTime()) / 1000
                )
              }s
            </span>
          )}
          <span>
            Last updated: {new Date(pipelineRun.updatedAt).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );
}

// Hook for managing pipeline visualization state
export function usePipelineVisualization(initialPipelineRun: PipelineRun) {
  const [pipelineRun, setPipelineRun] = useState(initialPipelineRun);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Update pipeline run data
  const updatePipelineRun = (newPipelineRun: PipelineRun) => {
    setPipelineRun(newPipelineRun);
    
    // Update selected job if it exists in the new data
    if (selectedJob) {
      const updatedJob = newPipelineRun.jobs?.find(job => job.id === selectedJob.id);
      if (updatedJob) {
        setSelectedJob(updatedJob);
      }
    }
  };

  // Refresh pipeline run data
  const refreshPipelineRun = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // This would typically fetch from an API
      // For now, we'll just use the current data
      setIsLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh pipeline run');
      setIsLoading(false);
    }
  };

  return {
    pipelineRun,
    selectedJob,
    isLoading,
    error,
    updatePipelineRun,
    refreshPipelineRun,
    setSelectedJob,
  };
}
