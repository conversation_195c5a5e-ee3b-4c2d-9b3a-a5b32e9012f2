'use client';

import { useState, useMemo } from 'react';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

import { PipelineRun, Job, JobStatus, StepConfig } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';

interface StageListViewProps {
  pipelineRun: PipelineRun;
  onJobClick?: (job: Job) => void;
  onStepClick?: (job: Job, step: StepConfig, stepIndex: number) => void;
  className?: string;
  autoExpand?: boolean;
}

interface StageGroup {
  name: string;
  jobs: Job[];
  status: JobStatus;
  startedAt?: string;
  finishedAt?: string;
}

export function StageListView({
  pipelineRun,
  onJobClick,
  onStepClick,
  className,
  autoExpand = false,
}: StageListViewProps) {
  const [expandedStages, setExpandedStages] = useState<Set<string>>(new Set());
  const [expandedJobs, setExpandedJobs] = useState<Set<string>>(new Set());

  // Group jobs by stage (based on dependencies and naming patterns)
  const stages = useMemo(() => {
    if (!pipelineRun.jobs) return [];

    const stageGroups = new Map<string, Job[]>();
    
    // Group jobs by stage name (extract from job name or use dependencies)
    pipelineRun.jobs.forEach(job => {
      // Try to extract stage name from job name (e.g., "build-frontend" -> "build")
      const stageName = job.name.split('-')[0] || 'default';
      
      if (!stageGroups.has(stageName)) {
        stageGroups.set(stageName, []);
      }
      stageGroups.get(stageName)!.push(job);
    });

    // Convert to stage objects with computed status
    const stages: StageGroup[] = Array.from(stageGroups.entries()).map(([name, jobs]) => {
      // Determine stage status based on job statuses
      let status: JobStatus = 'PENDING';
      if (jobs.every(job => job.status === 'SUCCESS')) {
        status = 'SUCCESS';
      } else if (jobs.some(job => job.status === 'FAILED')) {
        status = 'FAILED';
      } else if (jobs.some(job => job.status === 'RUNNING')) {
        status = 'RUNNING';
      } else if (jobs.some(job => job.status === 'QUEUED')) {
        status = 'QUEUED';
      }

      // Find earliest start time and latest finish time
      const startTimes = jobs.map(job => job.startedAt).filter(Boolean);
      const finishTimes = jobs.map(job => job.finishedAt).filter(Boolean);
      
      return {
        name,
        jobs: jobs.sort((a, b) => a.name.localeCompare(b.name)),
        status,
        startedAt: startTimes.length > 0 ? startTimes.sort()[0] : undefined,
        finishedAt: finishTimes.length > 0 ? finishTimes.sort().reverse()[0] : undefined,
      };
    });

    return stages.sort((a, b) => a.name.localeCompare(b.name));
  }, [pipelineRun.jobs]);

  // Auto-expand stages if enabled
  useState(() => {
    if (autoExpand) {
      const allStageNames = stages.map(stage => stage.name);
      setExpandedStages(new Set(allStageNames));
    }
  });

  const toggleStage = (stageName: string) => {
    setExpandedStages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stageName)) {
        newSet.delete(stageName);
      } else {
        newSet.add(stageName);
      }
      return newSet;
    });
  };

  const toggleJob = (jobId: string) => {
    setExpandedJobs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const getStatusIcon = (status: JobStatus) => {
    return jobService.getStatusIcon(status.toLowerCase());
  };

  const getStatusColorClass = (status: JobStatus) => {
    return jobService.getStatusColorClass(status.toLowerCase());
  };

  return (
    <div className={cn('h-full overflow-auto bg-white', className)}>
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Pipeline Stages & Jobs
        </h2>
        
        <div className="space-y-2">
          {stages.map(stage => (
            <div key={stage.name} className="border border-gray-200 rounded-lg">
              {/* Stage Header */}
              <button
                onClick={() => toggleStage(stage.name)}
                className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  {expandedStages.has(stage.name) ? (
                    <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getStatusIcon(stage.status)}</span>
                    <span className="font-medium text-gray-900">{stage.name}</span>
                    <span className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium',
                      getStatusColorClass(stage.status)
                    )}>
                      {stage.status}
                    </span>
                  </div>
                </div>
                
                <div className="text-sm text-gray-500">
                  {stage.jobs.length} job{stage.jobs.length !== 1 ? 's' : ''}
                  {stage.startedAt && (
                    <span className="ml-2">
                      • {formatDistanceToNow(new Date(stage.startedAt), { addSuffix: true })}
                    </span>
                  )}
                </div>
              </button>

              {/* Stage Jobs */}
              {expandedStages.has(stage.name) && (
                <div className="border-t border-gray-200">
                  {stage.jobs.map(job => (
                    <div key={job.id} className="border-b border-gray-100 last:border-b-0">
                      {/* Job Header */}
                      <button
                        onClick={() => {
                          toggleJob(job.id);
                          onJobClick?.(job);
                        }}
                        className="w-full px-6 py-3 flex items-center justify-between hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          {job.config?.steps && job.config.steps.length > 0 && (
                            expandedJobs.has(job.id) ? (
                              <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                            ) : (
                              <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                            )
                          )}
                          
                          <div className="flex items-center space-x-2">
                            <span className="text-base">{getStatusIcon(job.status)}</span>
                            <span className="font-medium text-gray-800">{job.name}</span>
                            <span className={cn(
                              'px-2 py-1 rounded-full text-xs font-medium',
                              getStatusColorClass(job.status)
                            )}>
                              {job.status}
                            </span>
                          </div>
                        </div>
                        
                        <div className="text-sm text-gray-500 text-right">
                          <div>
                            {jobService.formatDuration(job.startedAt, job.finishedAt)}
                          </div>
                          {job.environment && (
                            <div className="text-xs">
                              {job.environment.name}
                            </div>
                          )}
                        </div>
                      </button>

                      {/* Job Steps */}
                      {expandedJobs.has(job.id) && job.config?.steps && (
                        <div className="bg-gray-50 px-8 py-2">
                          {job.config.steps.map((step, stepIndex) => (
                            <button
                              key={stepIndex}
                              onClick={() => onStepClick?.(job, step, stepIndex)}
                              className="w-full px-3 py-2 flex items-center justify-between hover:bg-white rounded transition-colors"
                            >
                              <div className="flex items-center space-x-2">
                                <span className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium">
                                  {stepIndex + 1}
                                </span>
                                <span className="text-sm text-gray-700">
                                  {step.name || step.run || step.uses || `Step ${stepIndex + 1}`}
                                </span>
                              </div>
                              
                              <div className="text-xs text-gray-500">
                                {step.uses && (
                                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    {step.uses}
                                  </span>
                                )}
                                {step.run && (
                                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                    script
                                  </span>
                                )}
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {stages.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No jobs found in this pipeline run.</p>
          </div>
        )}
      </div>
    </div>
  );
}
