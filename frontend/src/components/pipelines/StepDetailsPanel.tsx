'use client';

import { useState, useEffect } from 'react';
import { 
  ClockI<PERSON>, 
  PlayIcon, 
  StopIcon, 
  ArrowPathIcon,
  DocumentTextIcon,
  FolderIcon,
  Cog6ToothIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

import { Job, StepConfig, Artifact } from '@/types/pipelines';
import { cn } from '@/utils/cn';
import { jobService } from '@/services/jobs';
import { ArtifactManager } from './ArtifactManager';

interface StepDetailsPanelProps {
  job: Job | null;
  selectedStep?: { step: StepConfig; index: number } | null;
  onClose?: () => void;
  className?: string;
}

export function StepDetailsPanel({
  job,
  selectedStep,
  onClose,
  className,
}: StepDetailsPanelProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'logs' | 'artifacts' | 'environment'>('overview');
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [environment, setEnvironment] = useState<Record<string, string>>({});
  const [metrics, setMetrics] = useState<{
    duration: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  } | null>(null);

  // Load additional data when job changes
  useEffect(() => {
    if (!job) return;

    const loadJobData = async () => {
      try {
        const [jobArtifacts, jobEnvironment, jobMetrics] = await Promise.all([
          jobService.getJobArtifacts(job.id),
          jobService.getJobEnvironment(job.id),
          jobService.getJobMetrics(job.id),
        ]);

        setArtifacts(jobArtifacts);
        setEnvironment(jobEnvironment);
        setMetrics(jobMetrics);
      } catch (error) {
        console.error('Failed to load job data:', error);
      }
    };

    loadJobData();
  }, [job]);

  const handleRetryJob = async () => {
    if (!job) return;
    
    try {
      await jobService.retryJob(job.id);
      // Job will be updated via WebSocket
    } catch (error) {
      console.error('Failed to retry job:', error);
    }
  };

  const handleCancelJob = async () => {
    if (!job) return;
    
    try {
      await jobService.cancelJob(job.id);
      // Job will be updated via WebSocket
    } catch (error) {
      console.error('Failed to cancel job:', error);
    }
  };

  const handleDownloadArtifact = async (artifact: Artifact) => {
    try {
      const blob = await jobService.downloadArtifact(artifact.id);
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = artifact.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to download artifact:', error);
    }
  };

  if (!job) {
    return (
      <div className={cn('flex items-center justify-center h-full bg-gray-50', className)}>
        <p className="text-gray-500">Select a job to view details</p>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: DocumentTextIcon },
    { id: 'artifacts', label: 'Artifacts', icon: FolderIcon, count: artifacts.length },
    { id: 'environment', label: 'Environment', icon: Cog6ToothIcon },
  ];

  const canRetry = ['FAILED', 'CANCELLED'].includes(job.status);
  const canCancel = ['PENDING', 'QUEUED', 'RUNNING'].includes(job.status);

  return (
    <div className={cn('flex flex-col h-full bg-white border border-gray-200 rounded-lg', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <span className="text-lg">{jobService.getStatusIcon(job.status.toLowerCase())}</span>
          <div>
            <h3 className="font-semibold text-gray-900">{job.name}</h3>
            <p className="text-sm text-gray-500">
              {job.pipelineRun?.pipeline?.name} • Run #{job.pipelineRun?.number}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {canRetry && (
            <button
              onClick={handleRetryJob}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>Retry</span>
            </button>
          )}
          
          {canCancel && (
            <button
              onClick={handleCancelJob}
              className="flex items-center space-x-1 px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              <StopIcon className="w-4 h-4" />
              <span>Cancel</span>
            </button>
          )}
          
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={cn(
              'flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            )}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
            {tab.count !== undefined && tab.count > 0 && (
              <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Status and timing */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Status</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Current Status:</span>
                    <span className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium',
                      jobService.getStatusColorClass(job.status.toLowerCase())
                    )}>
                      {job.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <span className="text-sm font-mono">
                      {jobService.formatDuration(job.startedAt, job.finishedAt)}
                    </span>
                  </div>
                  {job.startedAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Started:</span>
                      <span className="text-sm">
                        {formatDistanceToNow(new Date(job.startedAt), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                  {job.finishedAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Finished:</span>
                      <span className="text-sm">
                        {formatDistanceToNow(new Date(job.finishedAt), { addSuffix: true })}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Metrics */}
              {metrics && (
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Resource Usage</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">CPU:</span>
                      <span className="text-sm font-mono">{metrics.cpuUsage.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Memory:</span>
                      <span className="text-sm font-mono">{metrics.memoryUsage.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Disk:</span>
                      <span className="text-sm font-mono">{metrics.diskUsage.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Job configuration */}
            {job.config && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Configuration</h4>
                <div className="bg-gray-50 rounded-lg p-3">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {JSON.stringify(job.config, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Selected step details */}
            {selectedStep && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">
                  Step {selectedStep.index + 1}: {selectedStep.step.name || 'Unnamed Step'}
                </h4>
                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="space-y-2">
                    {selectedStep.step.uses && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">Uses:</span>
                        <span className="ml-2 text-sm text-blue-700">{selectedStep.step.uses}</span>
                      </div>
                    )}
                    {selectedStep.step.run && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">Run:</span>
                        <pre className="mt-1 text-sm text-blue-700 whitespace-pre-wrap">
                          {selectedStep.step.run}
                        </pre>
                      </div>
                    )}
                    {selectedStep.step.with && (
                      <div>
                        <span className="text-sm font-medium text-blue-900">With:</span>
                        <pre className="mt-1 text-sm text-blue-700 whitespace-pre-wrap">
                          {JSON.stringify(selectedStep.step.with, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Error details */}
            {job.status === 'FAILED' && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                  <h4 className="font-medium text-red-900">Error Details</h4>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-700">
                    This job failed during execution. Check the logs for more details.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'artifacts' && (
          <ArtifactManager
            job={job}
            showPreview={true}
            allowDelete={false}
            className="h-96"
          />
        )}

        {activeTab === 'environment' && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Environment Variables</h4>
            {Object.keys(environment).length > 0 ? (
              <div className="space-y-2">
                {Object.entries(environment).map(([key, value]) => (
                  <div
                    key={key}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                  >
                    <span className="font-mono text-sm text-gray-900">{key}</span>
                    <span className="font-mono text-sm text-gray-600 max-w-xs truncate">
                      {value}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No environment variables set for this job.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
