'use client';

import { useState } from 'react';
import { cn } from '@/utils/cn';
import { ViewMode } from '@/types/pipelines';

interface ViewToggleBarProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
  showLabels?: boolean;
  disabled?: boolean;
}

export function ViewToggleBar({
  currentView,
  onViewChange,
  className,
  showLabels = true,
  disabled = false,
}: ViewToggleBarProps) {
  const views: Array<{
    id: ViewMode;
    label: string;
    icon: React.ReactNode;
    description: string;
  }> = [
    {
      id: '2d',
      label: '2D Graph',
      description: 'Interactive 2D pipeline visualization',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 20l-5.447-2.724A1 1 0 013 16.382V7.618a1 1 0 01.553-.894L9 4l6 3 6-3 5.447 2.724A1 1 0 0127 7.618v8.764a1 1 0 01-.553.894L21 20l-6-3-6 3z"
          />
        </svg>
      ),
    },
    {
      id: '3d',
      label: '3D Graph',
      description: 'Immersive 3D pipeline visualization',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
          />
        </svg>
      ),
    },
    {
      id: 'list',
      label: 'List View',
      description: 'Hierarchical list of stages and jobs',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 6h16M4 10h16M4 14h16M4 18h16"
          />
        </svg>
      ),
    },
  ];

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {/* View toggle buttons */}
      <div className="flex bg-gray-100 rounded-lg p-1">
        {views.map((view) => (
          <button
            key={view.id}
            onClick={() => !disabled && onViewChange(view.id)}
            disabled={disabled}
            className={cn(
              'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-all duration-200',
              'hover:bg-white hover:shadow-sm',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              currentView === view.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900',
              !showLabels && 'px-2'
            )}
            title={view.description}
          >
            {view.icon}
            {showLabels && <span className="ml-2">{view.label}</span>}
          </button>
        ))}
      </div>

      {/* View options */}
      <div className="flex items-center space-x-2 ml-4">
        <ViewOptions currentView={currentView} />
      </div>
    </div>
  );
}

interface ViewOptionsProps {
  currentView: ViewMode;
}

function ViewOptions({ currentView }: ViewOptionsProps) {
  const [autoLayout, setAutoLayout] = useState(true);
  const [showMinimap, setShowMinimap] = useState(false);
  const [animateTransitions, setAnimateTransitions] = useState(true);

  if (currentView === 'list') {
    return (
      <div className="flex items-center space-x-3">
        <label className="flex items-center text-sm text-gray-600">
          <input
            type="checkbox"
            checked={autoLayout}
            onChange={(e) => setAutoLayout(e.target.checked)}
            className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          Auto-expand
        </label>
      </div>
    );
  }

  if (currentView === '2d') {
    return (
      <div className="flex items-center space-x-3">
        <label className="flex items-center text-sm text-gray-600">
          <input
            type="checkbox"
            checked={autoLayout}
            onChange={(e) => setAutoLayout(e.target.checked)}
            className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          Auto-layout
        </label>
        <label className="flex items-center text-sm text-gray-600">
          <input
            type="checkbox"
            checked={showMinimap}
            onChange={(e) => setShowMinimap(e.target.checked)}
            className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          Minimap
        </label>
        <label className="flex items-center text-sm text-gray-600">
          <input
            type="checkbox"
            checked={animateTransitions}
            onChange={(e) => setAnimateTransitions(e.target.checked)}
            className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          Animations
        </label>
      </div>
    );
  }

  if (currentView === '3d') {
    return (
      <div className="flex items-center space-x-3">
        <label className="flex items-center text-sm text-gray-600">
          <input
            type="checkbox"
            checked={animateTransitions}
            onChange={(e) => setAnimateTransitions(e.target.checked)}
            className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          Animations
        </label>
        <button
          className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded-md transition-colors"
          onClick={() => {
            // Reset camera position
            console.log('Reset 3D camera');
          }}
        >
          Reset View
        </button>
      </div>
    );
  }

  return null;
}

// Hook for managing view state
export function useViewToggle(initialView: ViewMode = '2d') {
  const [currentView, setCurrentView] = useState<ViewMode>(initialView);
  const [viewHistory, setViewHistory] = useState<ViewMode[]>([initialView]);

  const changeView = (newView: ViewMode) => {
    if (newView !== currentView) {
      setCurrentView(newView);
      setViewHistory(prev => [...prev.slice(-4), newView]); // Keep last 5 views
    }
  };

  const goBack = () => {
    if (viewHistory.length > 1) {
      const newHistory = [...viewHistory];
      newHistory.pop(); // Remove current view
      const previousView = newHistory[newHistory.length - 1];
      setCurrentView(previousView);
      setViewHistory(newHistory);
    }
  };

  const canGoBack = viewHistory.length > 1;

  return {
    currentView,
    changeView,
    goBack,
    canGoBack,
    viewHistory,
  };
}
