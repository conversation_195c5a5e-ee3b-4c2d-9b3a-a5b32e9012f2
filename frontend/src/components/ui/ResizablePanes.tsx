'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { cn } from '@/utils/cn';

interface ResizablePanesProps {
  children: React.ReactNode[];
  direction?: 'horizontal' | 'vertical';
  defaultSizes?: number[];
  minSizes?: number[];
  maxSizes?: number[];
  className?: string;
  resizerClassName?: string;
  onResize?: (sizes: number[]) => void;
}

export function ResizablePanes({
  children,
  direction = 'horizontal',
  defaultSizes,
  minSizes,
  maxSizes,
  className,
  resizerClassName,
  onResize,
}: ResizablePanesProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [sizes, setSizes] = useState<number[]>(() => {
    if (defaultSizes && defaultSizes.length === children.length) {
      return defaultSizes;
    }
    // Equal distribution by default
    const equalSize = 100 / children.length;
    return Array(children.length).fill(equalSize);
  });
  
  const [isDragging, setIsDragging] = useState(false);
  const [dragIndex, setDragIndex] = useState<number>(-1);
  const [startPos, setStartPos] = useState(0);
  const [startSizes, setStartSizes] = useState<number[]>([]);

  const handleMouseDown = useCallback((index: number, event: React.MouseEvent) => {
    event.preventDefault();
    setIsDragging(true);
    setDragIndex(index);
    setStartPos(direction === 'horizontal' ? event.clientX : event.clientY);
    setStartSizes([...sizes]);
  }, [sizes, direction]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || dragIndex === -1 || !containerRef.current) return;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    const containerSize = direction === 'horizontal' 
      ? containerRect.width 
      : containerRect.height;
    
    const currentPos = direction === 'horizontal' ? event.clientX : event.clientY;
    const delta = currentPos - startPos;
    const deltaPercent = (delta / containerSize) * 100;

    const newSizes = [...startSizes];
    const leftIndex = dragIndex;
    const rightIndex = dragIndex + 1;

    // Calculate new sizes
    let newLeftSize = startSizes[leftIndex] + deltaPercent;
    let newRightSize = startSizes[rightIndex] - deltaPercent;

    // Apply min/max constraints
    const minLeft = minSizes?.[leftIndex] || 10;
    const maxLeft = maxSizes?.[leftIndex] || 90;
    const minRight = minSizes?.[rightIndex] || 10;
    const maxRight = maxSizes?.[rightIndex] || 90;

    newLeftSize = Math.max(minLeft, Math.min(maxLeft, newLeftSize));
    newRightSize = Math.max(minRight, Math.min(maxRight, newRightSize));

    // Ensure the total doesn't exceed 100%
    const totalOthers = newSizes.reduce((sum, size, index) => {
      if (index !== leftIndex && index !== rightIndex) {
        return sum + size;
      }
      return sum;
    }, 0);

    const availableSpace = 100 - totalOthers;
    const totalNewSize = newLeftSize + newRightSize;

    if (totalNewSize > availableSpace) {
      const ratio = availableSpace / totalNewSize;
      newLeftSize *= ratio;
      newRightSize *= ratio;
    }

    newSizes[leftIndex] = newLeftSize;
    newSizes[rightIndex] = newRightSize;

    setSizes(newSizes);
    onResize?.(newSizes);
  }, [isDragging, dragIndex, startPos, startSizes, direction, minSizes, maxSizes, onResize]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragIndex(-1);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = direction === 'horizontal' ? 'col-resize' : 'row-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, direction]);

  const isHorizontal = direction === 'horizontal';

  return (
    <div
      ref={containerRef}
      className={cn(
        'flex h-full w-full',
        isHorizontal ? 'flex-row' : 'flex-col',
        className
      )}
    >
      {children.map((child, index) => (
        <div key={index} className="flex">
          {/* Pane content */}
          <div
            className="overflow-hidden"
            style={{
              [isHorizontal ? 'width' : 'height']: `${sizes[index]}%`,
              [isHorizontal ? 'height' : 'width']: '100%',
            }}
          >
            {child}
          </div>

          {/* Resizer (except for the last pane) */}
          {index < children.length - 1 && (
            <div
              className={cn(
                'group relative flex-shrink-0 bg-gray-200 hover:bg-gray-300 transition-colors',
                isHorizontal 
                  ? 'w-1 cursor-col-resize hover:w-2' 
                  : 'h-1 cursor-row-resize hover:h-2',
                isDragging && dragIndex === index && 'bg-blue-500',
                resizerClassName
              )}
              onMouseDown={(e) => handleMouseDown(index, e)}
            >
              {/* Visual indicator */}
              <div
                className={cn(
                  'absolute bg-gray-400 group-hover:bg-gray-500 transition-colors',
                  isHorizontal
                    ? 'left-1/2 top-1/2 h-8 w-0.5 -translate-x-1/2 -translate-y-1/2'
                    : 'left-1/2 top-1/2 h-0.5 w-8 -translate-x-1/2 -translate-y-1/2',
                  isDragging && dragIndex === index && 'bg-blue-600'
                )}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

// Hook for managing resizable pane state
export function useResizablePanes(
  initialSizes?: number[],
  minSizes?: number[],
  maxSizes?: number[]
) {
  const [sizes, setSizes] = useState<number[]>(initialSizes || []);
  const [isResizing, setIsResizing] = useState(false);

  const handleResize = useCallback((newSizes: number[]) => {
    setSizes(newSizes);
  }, []);

  const resetSizes = useCallback(() => {
    if (initialSizes) {
      setSizes([...initialSizes]);
    }
  }, [initialSizes]);

  const setPaneSize = useCallback((index: number, size: number) => {
    setSizes(prev => {
      const newSizes = [...prev];
      const oldSize = newSizes[index];
      const diff = size - oldSize;
      
      // Distribute the difference among other panes
      const otherPanes = newSizes.length - 1;
      if (otherPanes > 0) {
        const adjustment = -diff / otherPanes;
        newSizes.forEach((_, i) => {
          if (i !== index) {
            newSizes[i] += adjustment;
          }
        });
      }
      
      newSizes[index] = size;
      return newSizes;
    });
  }, []);

  return {
    sizes,
    isResizing,
    handleResize,
    resetSizes,
    setPaneSize,
    setIsResizing,
  };
}
