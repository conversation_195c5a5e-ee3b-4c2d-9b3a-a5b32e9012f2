import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock js-cookie
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  error: jest.fn(),
  success: jest.fn(),
  default: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('API Service', () => {
  const mockRequestUse = jest.fn();
  const mockResponseUse = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock interceptors
    const mockAxiosInstance = {
      ...mockedAxios,
      interceptors: {
        request: { use: mockRequestUse },
        response: { use: mockResponseUse },
      },
    };

    // Mock axios.create to return the mocked axios instance
    mockedAxios.create.mockReturnValue(mockAxiosInstance as any);
  });

  describe('get', () => {
    it('should make a GET request', async () => {
      const { apiClient } = require('../api');
      const mockData = { id: 1, name: 'Test' };
      mockedAxios.get.mockResolvedValue({ data: mockData });

      const result = await apiClient.get('/test');

      expect(mockedAxios.get).toHaveBeenCalledWith('/test', undefined);
      expect(result.data).toEqual(mockData);
    });

    it('should handle GET request errors', async () => {
      const { apiClient } = require('../api');
      const error = new Error('Network Error');
      mockedAxios.get.mockRejectedValue(error);

      await expect(apiClient.get('/test')).rejects.toThrow('Network Error');
    });
  });

  describe('post', () => {
    it('should make a POST request with data', async () => {
      const { apiClient } = require('../api');
      const mockData = { id: 1, name: 'Created' };
      const postData = { name: 'New Item' };
      mockedAxios.post.mockResolvedValue({ data: mockData });

      const result = await apiClient.post('/test', postData);

      expect(mockedAxios.post).toHaveBeenCalledWith('/test', postData, undefined);
      expect(result.data).toEqual(mockData);
    });

    it('should handle POST request errors', async () => {
      const { apiClient } = require('../api');
      const error = new Error('Validation Error');
      mockedAxios.post.mockRejectedValue(error);

      await expect(apiClient.post('/test', {})).rejects.toThrow('Validation Error');
    });
  });

  describe('put', () => {
    it('should make a PUT request with data', async () => {
      const { apiClient } = require('../api');
      const mockData = { id: 1, name: 'Updated' };
      const putData = { name: 'Updated Item' };
      mockedAxios.put.mockResolvedValue({ data: mockData });

      const result = await apiClient.put('/test/1', putData);

      expect(mockedAxios.put).toHaveBeenCalledWith('/test/1', putData, undefined);
      expect(result.data).toEqual(mockData);
    });
  });

  describe('delete', () => {
    it('should make a DELETE request', async () => {
      const { apiClient } = require('../api');
      mockedAxios.delete.mockResolvedValue({ data: null });

      const result = await apiClient.delete('/test/1');

      expect(mockedAxios.delete).toHaveBeenCalledWith('/test/1', undefined);
      expect(result.data).toBeNull();
    });
  });

  describe('patch', () => {
    it('should make a PATCH request with data', async () => {
      const { apiClient } = require('../api');
      const mockData = { id: 1, name: 'Patched' };
      const patchData = { name: 'Patched Item' };
      mockedAxios.patch.mockResolvedValue({ data: mockData });

      const result = await apiClient.patch('/test/1', patchData);

      expect(mockedAxios.patch).toHaveBeenCalledWith('/test/1', patchData, undefined);
      expect(result.data).toEqual(mockData);
    });
  });


});
