import { api } from './api';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UpdateProfileRequest,
  ChangePasswordRequest,
} from '@/types/auth';

export const authService = {
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await api.post('/auth/login', {
      login: email,
      password,
    });

    // Handle the backend response format
    if (response.data.success && response.data.data) {
      return {
        user: response.data.data.user,
        token: response.data.data.token,
      };
    }

    throw new Error(response.data.message || 'Login failed');
  },

  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/register', data);
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await api.get('/auth/me');

    if (response.data.success && response.data.data) {
      return response.data.data.user || response.data.data;
    }

    throw new Error('Failed to get current user');
  },

  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    const response = await api.put<{ data: { user: User } }>('/auth/me', data);
    return response.data.data.user;
  },

  async changePassword(data: ChangePasswordRequest): Promise<void> {
    await api.put('/auth/change-password', data);
  },

  async refreshToken(): Promise<{ token: string }> {
    const response = await api.post<{ data: { token: string } }>('/auth/refresh');
    return response.data.data;
  },

  async logout(): Promise<void> {
    // Note: This is a client-side logout
    // Server-side logout would require token blacklisting
    return Promise.resolve();
  },
};
