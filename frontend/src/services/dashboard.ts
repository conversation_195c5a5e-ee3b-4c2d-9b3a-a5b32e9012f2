import { api } from './api';

export interface DashboardStats {
  totalPipelines: number;
  activeJobs: number;
  successRate: number;
  avgDuration: string;
}

export interface ActivityItem {
  id: string;
  type: 'pipeline_run' | 'job_completed' | 'deployment' | 'user_action';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'failed' | 'running' | 'pending';
  user?: {
    name: string;
    avatar?: string;
  };
}

export interface PipelineStatus {
  id: string;
  name: string;
  project: string;
  status: 'success' | 'failed' | 'running' | 'pending';
  lastRun: string;
  duration: string;
  branch: string;
}

export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    try {
      const response = await api.get<{ data: DashboardStats }>('/api/dashboard/stats');
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      // Return default stats if API fails
      return {
        totalPipelines: 0,
        activeJobs: 0,
        successRate: 0,
        avgDuration: '0s',
      };
    }
  },

  async getRecentActivity(): Promise<ActivityItem[]> {
    try {
      const response = await api.get<{ data: { activities: ActivityItem[] } }>('/api/dashboard/activity');
      return response.data.data.activities;
    } catch (error) {
      console.error('Failed to fetch recent activity:', error);
      return [];
    }
  },

  async getPipelineStatus(): Promise<PipelineStatus[]> {
    try {
      const response = await api.get<{ data: { pipelines: PipelineStatus[] } }>('/api/dashboard/pipelines');
      return response.data.data.pipelines;
    } catch (error) {
      console.error('Failed to fetch pipeline status:', error);
      return [];
    }
  },

  async getSystemHealth(): Promise<Record<string, 'healthy' | 'unhealthy' | 'warning'>> {
    try {
      const response = await api.get<{ data: Record<string, any> }>('/api/health/detailed');
      const health = response.data.data;
      
      return {
        'API Server': health.checks?.database ? 'healthy' : 'unhealthy',
        'Database': health.checks?.database ? 'healthy' : 'unhealthy',
        'Queue Service': health.checks?.queue ? 'healthy' : 'unhealthy',
        'Storage': health.checks?.storage ? 'healthy' : 'unhealthy',
        'Runners': 'warning', // This would come from runner status
      };
    } catch (error) {
      console.error('Failed to fetch system health:', error);
      return {
        'API Server': 'unhealthy',
        'Database': 'unhealthy',
        'Queue Service': 'unhealthy',
        'Storage': 'unhealthy',
        'Runners': 'unhealthy',
      };
    }
  },
};
