import { api } from './api';

export interface Project {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization?: {
    id: string;
    name: string;
  };
  _count?: {
    pipelines: number;
  };
}

export interface CreateProjectRequest {
  name: string;
  slug: string;
  description?: string;
  organizationId?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  slug?: string;
  description?: string;
  isActive?: boolean;
}

export interface ProjectListResponse {
  data: Project[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ProjectFilters {
  search?: string;
  organizationId?: string;
  isActive?: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export const projectService = {
  // Get all projects with filtering and pagination
  async getProjects(
    filters: ProjectFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<Project[]> {
    try {
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      
      if (filters.search) params.append('search', filters.search);
      if (filters.organizationId) params.append('organizationId', filters.organizationId);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());

      const response = await api.get(`/api/projects?${params.toString()}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data.projects || response.data.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      return [];
    }
  },

  // Get project by ID
  async getProject(id: string): Promise<Project | null> {
    try {
      const response = await api.get(`/api/projects/${id}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data.project || response.data.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to fetch project:', error);
      return null;
    }
  },

  // Create new project
  async createProject(data: CreateProjectRequest): Promise<Project | null> {
    try {
      const response = await api.post('/api/projects', data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.project || response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to create project');
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  },

  // Update project
  async updateProject(id: string, data: UpdateProjectRequest): Promise<Project | null> {
    try {
      const response = await api.put(`/api/projects/${id}`, data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.project || response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to update project');
    } catch (error) {
      console.error('Failed to update project:', error);
      throw error;
    }
  },

  // Delete project
  async deleteProject(id: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/projects/${id}`);
      return response.data.success;
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  },

  // Get project statistics
  async getProjectStats(id: string): Promise<any> {
    try {
      const response = await api.get(`/api/projects/${id}/stats`);
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to fetch project stats:', error);
      return null;
    }
  },

  // Get project pipelines
  async getProjectPipelines(id: string): Promise<any[]> {
    try {
      const response = await api.get(`/api/projects/${id}/pipelines`);
      
      if (response.data.success && response.data.data) {
        return response.data.data.pipelines || response.data.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch project pipelines:', error);
      return [];
    }
  },
};
