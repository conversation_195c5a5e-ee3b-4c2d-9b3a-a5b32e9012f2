import { api } from './api';

export interface Secret {
  id: string;
  key: string;
  value: string;
  description?: string;
  environment?: string;
  projectId?: string;
  project?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateSecretRequest {
  key: string;
  value: string;
  description?: string;
  environment?: string;
  projectId?: string;
}

export interface UpdateSecretRequest {
  key?: string;
  value?: string;
  description?: string;
  environment?: string;
}

export interface SecretFilters {
  projectId?: string;
  environment?: string;
  search?: string;
}

export const secretService = {
  // Get all secrets with filtering
  async getSecrets(projectId?: string, filters: SecretFilters = {}): Promise<Secret[]> {
    try {
      const params = new URLSearchParams();

      if (projectId) params.append('projectId', projectId);
      if (filters.environment) params.append('environment', filters.environment);
      if (filters.search) params.append('search', filters.search);

      const response = await api.get(`/api/secrets?${params.toString()}`);

      if (response.data.success && response.data.data) {
        return response.data.data.secrets || response.data.data;
      }

      return [];
    } catch (error) {
      console.error('Failed to fetch secrets:', error);
      // Return mock data as fallback
      return [
        {
          id: '1',
          key: 'DATABASE_URL',
          value: 'postgresql://user:pass@localhost:5432/db',
          description: 'Main database connection string',
          environment: 'production',
          projectId: 'web-app',
          project: { id: 'web-app', name: 'Web Application' },
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
        },
        {
          id: '2',
          key: 'API_SECRET_KEY',
          value: 'sk_live_abcd1234567890',
          description: 'API secret key for external service',
          environment: 'production',
          projectId: 'api-server',
          project: { id: 'api-server', name: 'API Server' },
          createdAt: '2024-01-15T11:00:00Z',
          updatedAt: '2024-01-15T11:00:00Z',
        },
        {
          id: '3',
          key: 'STRIPE_SECRET_KEY',
          value: 'sk_test_xyz9876543210',
          description: 'Stripe payment processing key',
          environment: 'staging',
          projectId: 'web-app',
          project: { id: 'web-app', name: 'Web Application' },
          createdAt: '2024-01-15T12:00:00Z',
          updatedAt: '2024-01-15T12:00:00Z',
        },
        {
          id: '4',
          key: 'REDIS_URL',
          value: 'redis://localhost:6379',
          description: 'Redis cache connection',
          environment: 'development',
          projectId: 'api-server',
          project: { id: 'api-server', name: 'API Server' },
          createdAt: '2024-01-15T13:00:00Z',
          updatedAt: '2024-01-15T13:00:00Z',
        },
        {
          id: '5',
          key: 'JWT_SECRET',
          value: 'super-secret-jwt-key-12345',
          description: 'JWT signing secret',
          environment: 'production',
          projectId: 'api-server',
          project: { id: 'api-server', name: 'API Server' },
          createdAt: '2024-01-15T14:00:00Z',
          updatedAt: '2024-01-15T14:00:00Z',
        },
      ];
    }
  },

  // Get secret by ID
  async getSecret(id: string): Promise<Secret | null> {
    try {
      const response = await api.get(`/api/secrets/${id}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data.secret || response.data.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to fetch secret:', error);
      return null;
    }
  },

  // Create new secret
  async createSecret(data: CreateSecretRequest): Promise<Secret | null> {
    try {
      const response = await api.post('/api/secrets', data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.secret || response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to create secret');
    } catch (error) {
      console.error('Failed to create secret:', error);
      throw error;
    }
  },

  // Update secret
  async updateSecret(id: string, data: UpdateSecretRequest): Promise<Secret | null> {
    try {
      const response = await api.put(`/api/secrets/${id}`, data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.secret || response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to update secret');
    } catch (error) {
      console.error('Failed to update secret:', error);
      throw error;
    }
  },

  // Delete secret
  async deleteSecret(id: string): Promise<boolean> {
    try {
      const response = await api.delete(`/api/secrets/${id}`);
      return response.data.success;
    } catch (error) {
      console.error('Failed to delete secret:', error);
      throw error;
    }
  },

  // Get secrets for a specific project and environment
  async getProjectSecrets(projectId: string, environment?: string): Promise<Secret[]> {
    try {
      const params = new URLSearchParams();
      params.append('projectId', projectId);
      if (environment) params.append('environment', environment);

      const response = await api.get(`/api/secrets?${params.toString()}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data.secrets || response.data.data;
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch project secrets:', error);
      return [];
    }
  },
};
