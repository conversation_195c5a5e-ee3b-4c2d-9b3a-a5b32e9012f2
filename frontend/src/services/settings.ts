import { api } from './api';

export interface UserSettings {
  user: {
    id: string;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    notifications: {
      email: boolean;
      browser: boolean;
      slack: boolean;
    };
    dashboard: {
      defaultView: string;
      itemsPerPage: number;
    };
    timezone: string;
    language: string;
  };
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  avatar?: string;
}

export interface UpdatePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdatePreferencesRequest {
  theme?: 'light' | 'dark' | 'auto';
  notifications?: {
    email?: boolean;
    browser?: boolean;
    slack?: boolean;
  };
  dashboard?: {
    defaultView?: string;
    itemsPerPage?: number;
  };
  timezone?: string;
  language?: string;
}

export interface ProjectSettings {
  project: {
    id: string;
    name: string;
    slug: string;
    description?: string;
    repositoryUrl?: string;
    defaultBranch: string;
    settings?: any;
    isActive: boolean;
  };
  settings: {
    general: {
      autoDeleteArtifacts: boolean;
      artifactRetentionDays: number;
      maxConcurrentJobs: number;
    };
    notifications: {
      onSuccess: boolean;
      onFailure: boolean;
      onStart: boolean;
      channels: string[];
    };
    security: {
      requireApprovalForProduction: boolean;
      allowForkPullRequests: boolean;
      secretsAccessLevel: string;
    };
    integrations: {
      slack: {
        enabled: boolean;
        webhook: string;
        channel: string;
      };
      github: {
        enabled: boolean;
        webhookSecret: string;
      };
    };
  };
  userRole: string;
}

export interface SystemSettings {
  settings: {
    general: {
      siteName: string;
      siteUrl: string;
      allowRegistration: boolean;
      defaultUserRole: string;
    };
    security: {
      sessionTimeout: number;
      passwordMinLength: number;
      requireTwoFactor: boolean;
    };
    integrations: {
      vault: {
        enabled: boolean;
        address?: string;
      };
      redis: {
        enabled: boolean;
        url?: string;
      };
      minio: {
        enabled: boolean;
        endpoint?: string;
      };
    };
    limits: {
      maxProjectsPerUser: number;
      maxPipelinesPerProject: number;
      maxJobsPerPipeline: number;
      artifactSizeLimit: number;
    };
  };
}

export const settingsService = {
  // User settings
  async getUserSettings(): Promise<UserSettings> {
    try {
      const response = await api.get('/api/settings/user');
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to fetch user settings');
    } catch (error) {
      console.error('Failed to fetch user settings:', error);
      throw error;
    }
  },

  async updateProfile(data: UpdateProfileRequest): Promise<UserSettings['user']> {
    try {
      const response = await api.put('/api/settings/user/profile', data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.user;
      }
      
      throw new Error(response.data.message || 'Failed to update profile');
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  },

  async updatePassword(data: UpdatePasswordRequest): Promise<void> {
    try {
      const response = await api.put('/api/settings/user/password', data);
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to update password');
      }
    } catch (error) {
      console.error('Failed to update password:', error);
      throw error;
    }
  },

  async updatePreferences(data: UpdatePreferencesRequest): Promise<UserSettings['preferences']> {
    try {
      const response = await api.put('/api/settings/user/preferences', data);
      
      if (response.data.success && response.data.data) {
        return response.data.data.preferences;
      }
      
      throw new Error(response.data.message || 'Failed to update preferences');
    } catch (error) {
      console.error('Failed to update preferences:', error);
      throw error;
    }
  },

  // Project settings
  async getProjectSettings(projectId: string): Promise<ProjectSettings> {
    try {
      const response = await api.get(`/api/settings/project/${projectId}`);
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to fetch project settings');
    } catch (error) {
      console.error('Failed to fetch project settings:', error);
      throw error;
    }
  },

  async updateProjectSettings(projectId: string, settings: Partial<ProjectSettings['settings']>): Promise<ProjectSettings> {
    try {
      const response = await api.put(`/api/settings/project/${projectId}`, { settings });
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to update project settings');
    } catch (error) {
      console.error('Failed to update project settings:', error);
      throw error;
    }
  },

  // System settings (admin only)
  async getSystemSettings(): Promise<SystemSettings> {
    try {
      const response = await api.get('/api/settings/system');
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Failed to fetch system settings');
    } catch (error) {
      console.error('Failed to fetch system settings:', error);
      throw error;
    }
  },

  // Utility methods
  async uploadAvatar(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await api.post('/api/settings/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (response.data.success && response.data.data) {
        return response.data.data.avatarUrl;
      }
      
      throw new Error(response.data.message || 'Failed to upload avatar');
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      throw error;
    }
  },

  async testIntegration(type: 'slack' | 'github', config: any): Promise<boolean> {
    try {
      const response = await api.post(`/api/settings/integrations/${type}/test`, config);
      
      return response.data.success;
    } catch (error) {
      console.error(`Failed to test ${type} integration:`, error);
      return false;
    }
  },
};
