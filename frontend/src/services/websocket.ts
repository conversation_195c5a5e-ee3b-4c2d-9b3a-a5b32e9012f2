import { io, Socket } from 'socket.io-client';
import { PipelineUpdate, LogEntry } from '@/types/pipelines';

export interface WebSocketEvents {
  'pipeline:update': (update: PipelineUpdate) => void;
  'job:update': (update: PipelineUpdate) => void;
  'log:entry': (entry: LogEntry) => void;
  'log:stream': (data: { jobId: string; entries: LogEntry[] }) => void;
  connect: () => void;
  disconnect: () => void;
  error: (error: Error) => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private listeners: Map<string, Set<Function>> = new Map();
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.connect();
  }

  connect(): void {
    if (this.socket?.connected) return;

    const token = this.getAuthToken();
    if (!token) {
      console.warn('No auth token found, WebSocket connection skipped');
      return;
    }

    this.socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001', {
      auth: {
        token,
      },
      transports: ['websocket'],
      upgrade: true,
      rememberUpgrade: true,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connect');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      this.emit('disconnect');
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }
      
      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.emit('error', error);
      this.handleReconnect();
    });

    // Pipeline and job updates
    this.socket.on('pipeline:update', (update: PipelineUpdate) => {
      this.emit('pipeline:update', update);
    });

    this.socket.on('job:update', (update: PipelineUpdate) => {
      this.emit('job:update', update);
    });

    // Log streaming
    this.socket.on('log:entry', (entry: LogEntry) => {
      this.emit('log:entry', entry);
    });

    this.socket.on('log:stream', (data: { jobId: string; entries: LogEntry[] }) => {
      this.emit('log:stream', data);
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.listeners.clear();
  }

  // Subscribe to pipeline updates
  subscribeToPipeline(pipelineId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe:pipeline', { pipelineId });
    }
  }

  // Unsubscribe from pipeline updates
  unsubscribeFromPipeline(pipelineId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe:pipeline', { pipelineId });
    }
  }

  // Subscribe to job updates
  subscribeToJob(jobId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe:job', { jobId });
    }
  }

  // Unsubscribe from job updates
  unsubscribeFromJob(jobId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe:job', { jobId });
    }
  }

  // Subscribe to log streaming
  subscribeToLogs(jobId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe:logs', { jobId });
    }
  }

  // Unsubscribe from log streaming
  unsubscribeFromLogs(jobId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe:logs', { jobId });
    }
  }

  // Event listener management
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  private emit<K extends keyof WebSocketEvents>(
    event: K,
    ...args: Parameters<WebSocketEvents[K]>
  ): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach((listener) => {
        try {
          (listener as any)(...args);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  private getAuthToken(): string | null {
    // Get token from cookies or localStorage
    if (typeof window !== 'undefined') {
      // Try cookies first
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'auth-token') {
          return value;
        }
      }
      
      // Fallback to localStorage
      return localStorage.getItem('auth-token');
    }
    return null;
  }

  // Getters
  get connected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  get connectionState(): 'connected' | 'disconnected' | 'connecting' {
    if (this.isConnected && this.socket?.connected) return 'connected';
    if (this.socket && !this.socket.connected && this.reconnectAttempts > 0) return 'connecting';
    return 'disconnected';
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();

// React hook for WebSocket connection
export function useWebSocket() {
  return {
    connected: websocketService.connected,
    connectionState: websocketService.connectionState,
    subscribe: websocketService.on.bind(websocketService),
    unsubscribe: websocketService.off.bind(websocketService),
    subscribeToPipeline: websocketService.subscribeToPipeline.bind(websocketService),
    unsubscribeFromPipeline: websocketService.unsubscribeFromPipeline.bind(websocketService),
    subscribeToJob: websocketService.subscribeToJob.bind(websocketService),
    unsubscribeFromJob: websocketService.unsubscribeFromJob.bind(websocketService),
    subscribeToLogs: websocketService.subscribeToLogs.bind(websocketService),
    unsubscribeFromLogs: websocketService.unsubscribeFromLogs.bind(websocketService),
  };
}
