export interface Pipeline {
  id: string;
  name: string;
  slug: string;
  description?: string;
  projectId: string;
  userId: string;
  config: PipelineConfig;
  isActive: boolean;
  status: PipelineStatus;
  createdAt: string;
  updatedAt: string;
  project: {
    id: string;
    name: string;
    slug: string;
  };
  user: {
    id: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  runs?: PipelineRun[];
  _count?: {
    runs: number;
  };
}

export interface PipelineRun {
  id: string;
  number: number;
  pipelineId: string;
  status: PipelineRunStatus;
  trigger?: any;
  variables?: any;
  startedAt?: string;
  finishedAt?: string;
  createdAt: string;
  updatedAt: string;
  pipeline?: Pipeline;
  jobs?: Job[];
}

export interface Job {
  id: string;
  name: string;
  pipelineRunId: string;
  userId: string;
  environmentId?: string;
  status: JobStatus;
  config: JobConfig;
  logs?: string;
  artifacts?: any;
  startedAt?: string;
  finishedAt?: string;
  createdAt: string;
  updatedAt: string;
  pipelineRun?: PipelineRun;
  user?: {
    id: string;
    username: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  environment?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface PipelineConfig {
  version?: string;
  jobs: Record<string, JobConfig>;
  workflows?: Record<string, WorkflowConfig>;
}

export interface JobConfig {
  name?: string;
  runs_on?: string;
  needs?: string[];
  if?: string;
  steps: StepConfig[];
  environment?: string;
  timeout_minutes?: number;
  continue_on_error?: boolean;
  strategy?: {
    matrix?: Record<string, any[]>;
    fail_fast?: boolean;
    max_parallel?: number;
  };
}

export interface StepConfig {
  id?: string;
  name?: string;
  uses?: string;
  run?: string;
  with?: Record<string, any>;
  env?: Record<string, string>;
  if?: string;
  continue_on_error?: boolean;
  timeout_minutes?: number;
}

export interface WorkflowConfig {
  on: WorkflowTrigger;
  jobs: Record<string, JobConfig>;
}

export interface WorkflowTrigger {
  push?: {
    branches?: string[];
    tags?: string[];
    paths?: string[];
  };
  pull_request?: {
    branches?: string[];
    types?: string[];
  };
  schedule?: Array<{
    cron: string;
  }>;
  workflow_dispatch?: {
    inputs?: Record<string, any>;
  };
}

// Graph visualization types
export interface PipelineNode {
  id: string;
  type: 'job' | 'step';
  data: {
    label: string;
    status: JobStatus | StepStatus;
    job?: Job;
    step?: StepConfig;
    duration?: number;
    startedAt?: string;
    finishedAt?: string;
  };
  position: { x: number; y: number };
  style?: React.CSSProperties;
}

export interface PipelineEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
  style?: React.CSSProperties;
}

export interface PipelineGraph {
  nodes: PipelineNode[];
  edges: PipelineEdge[];
}

// 3D visualization types
export interface Pipeline3DNode {
  id: string;
  position: [number, number, number];
  status: JobStatus;
  label: string;
  job?: Job;
  dependencies: string[];
}

// Log streaming types
export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  source?: string;
  jobId?: string;
  stepId?: string;
}

export interface LogStream {
  jobId: string;
  entries: LogEntry[];
  isStreaming: boolean;
  hasMore: boolean;
}

// Artifact types
export interface Artifact {
  id: string;
  name: string;
  path: string;
  size: number;
  type: string;
  downloadUrl: string;
  createdAt: string;
  jobId: string;
  metadata?: Record<string, any>;
}

// Status enums
export type PipelineStatus = 'IDLE' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
export type PipelineRunStatus = 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
export type JobStatus = 'PENDING' | 'QUEUED' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'SKIPPED';
export type StepStatus = 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'SKIPPED';

// View types
export type ViewMode = '2d' | '3d' | 'list';

// Filter and search types
export interface PipelineFilters {
  search?: string;
  projectId?: string;
  status?: PipelineStatus[];
  userId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface JobFilters {
  search?: string;
  pipelineId?: string;
  status?: JobStatus[];
  environmentId?: string;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// API response types
export interface PipelineListResponse extends PaginatedResponse<Pipeline> {}
export interface JobListResponse extends PaginatedResponse<Job> {}

// Real-time update types
export interface PipelineUpdate {
  type: 'pipeline' | 'run' | 'job' | 'step';
  id: string;
  status: PipelineStatus | PipelineRunStatus | JobStatus | StepStatus;
  data?: any;
}

// Error types
export interface PipelineError {
  id: string;
  type: 'pipeline' | 'job' | 'step';
  message: string;
  details?: string;
  timestamp: string;
  jobId?: string;
  stepId?: string;
}
