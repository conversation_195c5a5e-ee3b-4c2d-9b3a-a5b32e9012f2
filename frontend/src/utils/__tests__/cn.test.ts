import { cn } from '../cn';

describe('cn utility', () => {
  it('should merge class names correctly', () => {
    const result = cn('base-class', 'additional-class');
    expect(result).toContain('base-class');
    expect(result).toContain('additional-class');
  });

  it('should handle conditional classes', () => {
    const result = cn('base-class', true && 'conditional-class', false && 'hidden-class');
    expect(result).toContain('base-class');
    expect(result).toContain('conditional-class');
    expect(result).not.toContain('hidden-class');
  });

  it('should handle undefined and null values', () => {
    const result = cn('base-class', undefined, null, 'valid-class');
    expect(result).toContain('base-class');
    expect(result).toContain('valid-class');
  });

  it('should handle empty strings', () => {
    const result = cn('base-class', '', 'valid-class');
    expect(result).toContain('base-class');
    expect(result).toContain('valid-class');
  });

  it('should merge Tailwind classes correctly', () => {
    const result = cn('p-4', 'p-2'); // p-2 should override p-4
    expect(result).toContain('p-2');
    expect(result).not.toContain('p-4');
  });

  it('should handle complex Tailwind class merging', () => {
    const result = cn(
      'bg-red-500 text-white p-4',
      'bg-blue-500 p-2', // bg-blue-500 and p-2 should override
      'hover:bg-green-500'
    );
    expect(result).toContain('bg-blue-500');
    expect(result).toContain('p-2');
    expect(result).toContain('text-white');
    expect(result).toContain('hover:bg-green-500');
    expect(result).not.toContain('bg-red-500');
    expect(result).not.toContain('p-4');
  });

  it('should handle object syntax', () => {
    const result = cn({
      'base-class': true,
      'conditional-class': true,
      'hidden-class': false,
    });
    expect(result).toContain('base-class');
    expect(result).toContain('conditional-class');
    expect(result).not.toContain('hidden-class');
  });

  it('should handle array syntax', () => {
    const result = cn(['base-class', 'array-class'], 'additional-class');
    expect(result).toContain('base-class');
    expect(result).toContain('array-class');
    expect(result).toContain('additional-class');
  });

  it('should handle mixed syntax', () => {
    const result = cn(
      'base-class',
      ['array-class'],
      { 'object-class': true, 'hidden-class': false },
      'final-class'
    );
    expect(result).toContain('base-class');
    expect(result).toContain('array-class');
    expect(result).toContain('object-class');
    expect(result).toContain('final-class');
    expect(result).not.toContain('hidden-class');
  });
});
