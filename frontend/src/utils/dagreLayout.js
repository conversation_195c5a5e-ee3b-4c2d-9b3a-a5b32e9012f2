import dagre from 'dagre';

/**
 * Applies automatic layout to React Flow elements using Dagre
 * @param {Array} nodes - React Flow nodes
 * @param {Array} edges - React Flow edges
 * @param {string} direction - Layout direction ('TB', 'BT', 'LR', 'RL')
 * @param {Object} options - Layout options
 * @returns {Object} - Layouted nodes and edges
 */
export function getLayoutedElements(nodes, edges, direction = 'LR', options = {}) {
  const {
    nodeWidth = 172,
    nodeHeight = 80,
    rankSep = 150,
    nodeSep = 100,
    edgeSep = 50,
    marginX = 50,
    marginY = 50,
  } = options;

  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));
  
  const isHorizontal = direction === 'LR' || direction === 'RL';
  
  dagreGraph.setGraph({ 
    rankdir: direction,
    ranksep: rankSep,
    nodesep: nodeSep,
    edgesep: edgeSep,
    marginx: marginX,
    marginy: marginY,
  });

  // Add nodes to dagre graph
  nodes.forEach(node => {
    dagreGraph.setNode(node.id, { 
      width: nodeWidth, 
      height: nodeHeight 
    });
  });

  // Add edges to dagre graph
  edges.forEach(edge => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // Calculate layout
  dagre.layout(dagreGraph);

  // Apply calculated positions to nodes
  const layoutedNodes = nodes.map(node => {
    const nodeWithPosition = dagreGraph.node(node.id);
    
    // Set handle positions based on layout direction
    const targetPosition = isHorizontal ? 'left' : 'top';
    const sourcePosition = isHorizontal ? 'right' : 'bottom';
    
    return {
      ...node,
      targetPosition,
      sourcePosition,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
    };
  });

  return { 
    nodes: layoutedNodes, 
    edges 
  };
}

/**
 * Calculates hierarchical layout for complex pipelines with parallel stages
 * @param {Array} nodes - Pipeline nodes
 * @param {Array} edges - Pipeline edges
 * @returns {Object} - Nodes with calculated levels and positions
 */
export function getHierarchicalLayout(nodes, edges) {
  // Build adjacency list
  const adjacencyList = {};
  const inDegree = {};
  
  nodes.forEach(node => {
    adjacencyList[node.id] = [];
    inDegree[node.id] = 0;
  });
  
  edges.forEach(edge => {
    adjacencyList[edge.source].push(edge.target);
    inDegree[edge.target]++;
  });
  
  // Topological sort to determine levels
  const levels = {};
  const queue = [];
  
  // Start with nodes that have no dependencies
  nodes.forEach(node => {
    if (inDegree[node.id] === 0) {
      queue.push(node.id);
      levels[node.id] = 0;
    }
  });
  
  while (queue.length > 0) {
    const current = queue.shift();
    const currentLevel = levels[current];
    
    adjacencyList[current].forEach(neighbor => {
      inDegree[neighbor]--;
      levels[neighbor] = Math.max(levels[neighbor] || 0, currentLevel + 1);
      
      if (inDegree[neighbor] === 0) {
        queue.push(neighbor);
      }
    });
  }
  
  // Group nodes by level
  const nodesByLevel = {};
  Object.entries(levels).forEach(([nodeId, level]) => {
    if (!nodesByLevel[level]) {
      nodesByLevel[level] = [];
    }
    nodesByLevel[level].push(nodeId);
  });
  
  // Calculate positions
  const levelWidth = 250;
  const nodeHeight = 100;
  const maxLevel = Math.max(...Object.values(levels));
  
  const layoutedNodes = nodes.map(node => {
    const level = levels[node.id];
    const nodesInLevel = nodesByLevel[level];
    const indexInLevel = nodesInLevel.indexOf(node.id);
    const totalInLevel = nodesInLevel.length;
    
    // Center nodes in each level
    const yOffset = (indexInLevel - (totalInLevel - 1) / 2) * nodeHeight;
    
    return {
      ...node,
      position: {
        x: level * levelWidth,
        y: yOffset,
      },
      data: {
        ...node.data,
        level,
        indexInLevel,
        totalInLevel,
      },
    };
  });
  
  return {
    nodes: layoutedNodes,
    edges,
    levels: nodesByLevel,
    maxLevel,
  };
}

/**
 * Creates a circular layout for pipeline visualization
 * @param {Array} nodes - Pipeline nodes
 * @param {Array} edges - Pipeline edges
 * @param {Object} options - Layout options
 * @returns {Object} - Nodes with circular positions
 */
export function getCircularLayout(nodes, edges, options = {}) {
  const {
    radius = 200,
    centerX = 0,
    centerY = 0,
    startAngle = 0,
  } = options;
  
  const nodeCount = nodes.length;
  const angleStep = (2 * Math.PI) / nodeCount;
  
  const layoutedNodes = nodes.map((node, index) => {
    const angle = startAngle + index * angleStep;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);
    
    return {
      ...node,
      position: { x, y },
    };
  });
  
  return {
    nodes: layoutedNodes,
    edges,
  };
}

/**
 * Creates a matrix layout for parallel job visualization
 * @param {Array} nodes - Pipeline nodes
 * @param {Array} edges - Pipeline edges
 * @param {Object} options - Layout options
 * @returns {Object} - Nodes with matrix positions
 */
export function getMatrixLayout(nodes, edges, options = {}) {
  const {
    columns = 3,
    nodeWidth = 200,
    nodeHeight = 100,
    marginX = 50,
    marginY = 50,
  } = options;
  
  const layoutedNodes = nodes.map((node, index) => {
    const row = Math.floor(index / columns);
    const col = index % columns;
    
    return {
      ...node,
      position: {
        x: col * (nodeWidth + marginX),
        y: row * (nodeHeight + marginY),
      },
    };
  });
  
  return {
    nodes: layoutedNodes,
    edges,
  };
}

/**
 * Applies force-directed layout for organic pipeline visualization
 * @param {Array} nodes - Pipeline nodes
 * @param {Array} edges - Pipeline edges
 * @param {Object} options - Layout options
 * @returns {Object} - Nodes with force-directed positions
 */
export function getForceDirectedLayout(nodes, edges, options = {}) {
  const {
    iterations = 100,
    repulsion = 1000,
    attraction = 0.1,
    damping = 0.9,
    centerForce = 0.01,
  } = options;
  
  // Initialize positions randomly if not set
  const layoutedNodes = nodes.map(node => ({
    ...node,
    position: node.position || {
      x: Math.random() * 400 - 200,
      y: Math.random() * 400 - 200,
    },
    velocity: { x: 0, y: 0 },
  }));
  
  // Build edge map for faster lookup
  const edgeMap = new Map();
  edges.forEach(edge => {
    if (!edgeMap.has(edge.source)) {
      edgeMap.set(edge.source, []);
    }
    edgeMap.get(edge.source).push(edge.target);
  });
  
  // Simulate force-directed layout
  for (let i = 0; i < iterations; i++) {
    // Calculate forces
    layoutedNodes.forEach(node => {
      let fx = 0, fy = 0;
      
      // Repulsion from other nodes
      layoutedNodes.forEach(other => {
        if (node.id !== other.id) {
          const dx = node.position.x - other.position.x;
          const dy = node.position.y - other.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          const force = repulsion / (distance * distance);
          
          fx += (dx / distance) * force;
          fy += (dy / distance) * force;
        }
      });
      
      // Attraction to connected nodes
      const connected = edgeMap.get(node.id) || [];
      connected.forEach(targetId => {
        const target = layoutedNodes.find(n => n.id === targetId);
        if (target) {
          const dx = target.position.x - node.position.x;
          const dy = target.position.y - node.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          fx += dx * attraction;
          fy += dy * attraction;
        }
      });
      
      // Center force
      fx -= node.position.x * centerForce;
      fy -= node.position.y * centerForce;
      
      // Update velocity and position
      node.velocity.x = (node.velocity.x + fx) * damping;
      node.velocity.y = (node.velocity.y + fy) * damping;
      
      node.position.x += node.velocity.x;
      node.position.y += node.velocity.y;
    });
  }
  
  // Clean up velocity property
  const finalNodes = layoutedNodes.map(node => {
    const { velocity, ...cleanNode } = node;
    return cleanNode;
  });
  
  return {
    nodes: finalNodes,
    edges,
  };
}
