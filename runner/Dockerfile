FROM node:18-alpine

# Install Docker CLI and other dependencies
RUN apk add --no-cache \
    docker-cli \
    git \
    curl \
    bash \
    tar \
    gzip

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S runner
RUN adduser -S runner -u 1001

# Create workspace directory
RUN mkdir -p /workspace && chown -R runner:runner /workspace

# Change ownership of the app directory
RUN chown -R runner:runner /app

USER runner

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

CMD ["npm", "start"]
