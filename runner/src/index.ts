import dotenv from 'dotenv';
import { logger } from './utils/logger';
import { RunnerService } from './services/runner';
import { QueueService } from './services/queue';
import { DockerService } from './services/docker';
import { ApiService } from './services/api';
import { HealthService } from './services/health';

// Load environment variables
dotenv.config();

class ChainOpsRunner {
  private runnerService: RunnerService;
  private queueService: QueueService;
  private dockerService: DockerService;
  private apiService: ApiService;
  private healthService: HealthService;
  private isShuttingDown = false;

  constructor() {
    this.apiService = new ApiService();
    this.dockerService = new DockerService();
    this.queueService = new QueueService();
    this.runnerService = new RunnerService(
      this.apiService,
      this.dockerService,
      this.queueService
    );
    this.healthService = new HealthService();
  }

  async start(): Promise<void> {
    try {
      logger.info('🚀 Starting ChainOps Runner...');

      // Initialize services
      await this.dockerService.initialize();
      logger.info('✅ Docker service initialized');

      await this.queueService.initialize();
      logger.info('✅ Queue service initialized');

      await this.apiService.initialize();
      logger.info('✅ API service initialized');

      // Start health service
      await this.healthService.start();
      logger.info('✅ Health service started on port 8080');

      // Start runner service
      await this.runnerService.start();
      logger.info('✅ Runner service started');

      logger.info('🎉 ChainOps Runner is ready and waiting for jobs!');
      
      // Log runner information
      const runnerInfo = await this.runnerService.getRunnerInfo();
      logger.info('📊 Runner Info:', runnerInfo);

    } catch (error) {
      logger.error('❌ Failed to start ChainOps Runner:', error);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    logger.info('🛑 Shutting down ChainOps Runner...');

    try {
      // Stop accepting new jobs
      await this.runnerService.stop();
      logger.info('✅ Runner service stopped');

      // Stop queue service
      await this.queueService.stop();
      logger.info('✅ Queue service stopped');

      // Stop health service
      await this.healthService.stop();
      logger.info('✅ Health service stopped');

      logger.info('👋 ChainOps Runner shutdown complete');
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
    }
  }
}

// Create runner instance
const runner = new ChainOpsRunner();

// Graceful shutdown handlers
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await runner.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await runner.stop();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the runner
runner.start().catch((error) => {
  logger.error('Failed to start runner:', error);
  process.exit(1);
});
