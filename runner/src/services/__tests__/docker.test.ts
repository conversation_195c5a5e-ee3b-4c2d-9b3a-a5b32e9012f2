import { DockerService } from '../docker';
import Docker from 'dockerode';

// Mock dockerode with proper implementation
jest.mock('dockerode', () => {
  return jest.fn().mockImplementation(() => ({
    ping: jest.fn(),
    info: jest.fn(),
    createContainer: jest.fn(),
    getContainer: jest.fn(),
    getImage: jest.fn(),
    pull: jest.fn(),
    listContainers: jest.fn(),
    pruneContainers: jest.fn(),
    pruneImages: jest.fn(),
    modem: {
      followProgress: jest.fn((stream, callback) => {
        // Immediately call callback to avoid timeout
        setTimeout(() => callback(null, []), 10);
      }),
    },
  }));
});

const mockDocker = Docker as jest.MockedClass<typeof Docker>;

describe('DockerService', () => {
  let dockerService: DockerService;
  let mockDockerInstance: jest.Mocked<Docker>;

  beforeEach(() => {
    mockDockerInstance = {
      ping: jest.fn(),
      createContainer: jest.fn(),
      getContainer: jest.fn(),
      getImage: jest.fn(),
      pull: jest.fn(),
      info: jest.fn(),
      pruneContainers: jest.fn(),
      pruneImages: jest.fn(),
      modem: {
        followProgress: jest.fn((stream, callback) => {
          // Immediately call callback to avoid timeout
          setTimeout(() => callback(null, []), 10);
        }),
      },
    } as any;

    mockDocker.mockImplementation(() => mockDockerInstance);
    dockerService = new DockerService();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize Docker service', async () => {
      mockDockerInstance.ping.mockResolvedValue(undefined);
      mockDockerInstance.info.mockResolvedValue({
        ServerVersion: '20.10.0',
        MemTotal: 8589934592,
        NCPU: 4,
      } as any);

      await dockerService.initialize();

      expect(mockDockerInstance.ping).toHaveBeenCalled();
      expect(mockDockerInstance.info).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      mockDockerInstance.ping.mockRejectedValue(new Error('Docker daemon not running'));

      await expect(dockerService.initialize()).rejects.toThrow('Docker daemon not running');
    });
  });

  describe('createContainer', () => {
    it('should create a container successfully', async () => {
      const mockConfig = {
        image: 'node:18',
        cmd: ['echo', 'Hello World'],
        env: { NODE_ENV: 'test' },
        workingDir: '/workspace',
        volumes: { '/host/path': '/container/path' },
      };

      const mockContainer = {
        id: 'container-123',
      };

      const mockImage = {
        inspect: jest.fn().mockResolvedValue({ Id: 'image-123' }),
      };

      mockDockerInstance.getImage.mockReturnValue(mockImage as any);
      mockDockerInstance.createContainer.mockResolvedValue(mockContainer as any);

      const containerId = await dockerService.createContainer(mockConfig);

      expect(containerId).toBeDefined();
      expect(mockDockerInstance.createContainer).toHaveBeenCalledWith({
        Image: mockConfig.image,
        Cmd: mockConfig.cmd,
        Env: ['NODE_ENV=test'],
        WorkingDir: '/workspace',
        HostConfig: {
          Binds: ['/host/path:/container/path'],
          NetworkMode: 'bridge',
          Memory: undefined,
          CpuQuota: undefined,
          CpuPeriod: undefined,
          AutoRemove: true,
        },
        name: expect.stringContaining('chainops-job-'),
        AttachStdout: true,
        AttachStderr: true,
      });
    });

    it('should handle container creation errors', async () => {
      const mockConfig = {
        image: 'invalid-image',
        cmd: ['echo', 'Hello World'],
      };

      const mockImage = {
        inspect: jest.fn().mockRejectedValue(new Error('Image not found')),
      };

      mockDockerInstance.getImage.mockReturnValue(mockImage as any);
      mockDockerInstance.pull.mockRejectedValue(new Error('Pull failed'));

      await expect(dockerService.createContainer(mockConfig)).rejects.toThrow();
    });
  });

  describe('executeContainer', () => {
    it('should execute a container successfully', async () => {
      const containerId = 'test-container-id';
      const mockContainer = {
        attach: jest.fn().mockResolvedValue({
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              // Simulate stdout data
              setTimeout(() => callback(Buffer.from('\x01\x00\x00\x00\x00\x00\x00\x0cHello World\n')), 10);
            }
          }),
        }),
        start: jest.fn().mockResolvedValue(undefined),
        wait: jest.fn().mockResolvedValue({ StatusCode: 0 }),
      };

      dockerService['runningContainers'].set(containerId, mockContainer as any);

      const result = await dockerService.executeContainer(containerId);

      expect(result.exitCode).toBe(0);
      expect(result.stdout).toBeDefined();
      expect(result.duration).toBeGreaterThanOrEqual(0);
    });
  });

  describe('pullImage', () => {
    it('should pull an image successfully', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') {
            setTimeout(callback, 10);
          }
        }),
      };

      mockDockerInstance.pull.mockResolvedValue(mockStream as any);

      await dockerService.pullImage('node:18');

      expect(mockDockerInstance.pull).toHaveBeenCalledWith('node:18');
    });

    it('should handle image pull errors', async () => {
      mockDockerInstance.pull.mockRejectedValue(new Error('Image not found'));

      await expect(dockerService.pullImage('invalid-image')).rejects.toThrow('Image not found');
    });
  });

  describe('getSystemInfo', () => {
    it('should return system information', async () => {
      const mockInfo = {
        ServerVersion: '20.10.0',
        Containers: 5,
        ContainersRunning: 2,
        Images: 10,
        MemTotal: 8589934592,
        NCPU: 4,
      };

      mockDockerInstance.info.mockResolvedValue(mockInfo as any);

      const info = await dockerService.getSystemInfo();

      expect(info).toEqual({
        version: '20.10.0',
        containers: 5,
        containersRunning: 2,
        images: 10,
        memoryTotal: 8589934592,
        cpus: 4,
      });
    });
  });

  describe('cleanup', () => {
    it('should cleanup running containers', async () => {
      const containerId1 = 'container1';
      const containerId2 = 'container2';

      const mockContainer1 = {
        stop: jest.fn().mockResolvedValue(undefined),
        kill: jest.fn().mockResolvedValue(undefined)
      };
      const mockContainer2 = {
        stop: jest.fn().mockResolvedValue(undefined),
        kill: jest.fn().mockResolvedValue(undefined)
      };

      dockerService['runningContainers'].set(containerId1, mockContainer1 as any);
      dockerService['runningContainers'].set(containerId2, mockContainer2 as any);

      mockDockerInstance.pruneContainers = jest.fn().mockResolvedValue(undefined);
      mockDockerInstance.pruneImages = jest.fn().mockResolvedValue(undefined);

      await dockerService.cleanup();

      expect(mockContainer1.stop).toHaveBeenCalled();
      expect(mockContainer2.stop).toHaveBeenCalled();
      expect(mockDockerInstance.pruneContainers).toHaveBeenCalled();
      expect(mockDockerInstance.pruneImages).toHaveBeenCalled();
    });
  });
});
