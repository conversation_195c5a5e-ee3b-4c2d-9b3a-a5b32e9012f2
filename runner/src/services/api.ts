import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

export interface JobUpdate {
  status: 'PENDING' | 'QUEUED' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
  logs?: string;
  artifacts?: any[];
  startedAt?: Date;
  finishedAt?: Date;
}

export interface RunnerRegistration {
  id: string;
  name: string;
  version: string;
  capabilities: string[];
  systemInfo: any;
}

export class ApiService {
  private api: AxiosInstance;
  private runnerToken: string;
  private runnerId: string;

  constructor() {
    this.runnerToken = process.env.RUNNER_TOKEN || 'runner-secret-token';
    this.runnerId = process.env.RUNNER_ID || `runner-${Date.now()}`;

    this.api = axios.create({
      baseURL: process.env.API_URL || 'http://localhost:3001',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.runnerToken}`,
        'X-Runner-ID': this.runnerId,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          logger.error(`API Error: ${error.response.status} ${error.response.data?.message || error.message}`);
        } else if (error.request) {
          logger.error('API Network Error:', error.message);
        } else {
          logger.error('API Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async initialize(): Promise<void> {
    try {
      // Test API connection
      await this.healthCheck();
      logger.info('API service connected');

      // Register runner
      await this.registerRunner();
      logger.info(`Runner registered with ID: ${this.runnerId}`);
    } catch (error) {
      logger.error('Failed to initialize API service:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.api.get('/health');
      return response.status === 200;
    } catch (error) {
      logger.error('API health check failed:', error);
      return false;
    }
  }

  async registerRunner(): Promise<void> {
    try {
      const registration: RunnerRegistration = {
        id: this.runnerId,
        name: `ChainOps Runner ${this.runnerId}`,
        version: process.env.npm_package_version || '1.0.0',
        capabilities: [
          'docker',
          'git',
          'nodejs',
          'python',
          'golang',
        ],
        systemInfo: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          memory: process.memoryUsage(),
        },
      };

      await this.api.post('/api/runners/register', registration);
      logger.info('Runner registered successfully');
    } catch (error) {
      logger.error('Failed to register runner:', error);
      // Don't throw here, registration might not be implemented yet
    }
  }

  async updateJobStatus(jobId: string, update: JobUpdate): Promise<void> {
    try {
      await this.api.put(`/api/jobs/${jobId}/status`, update);
      logger.debug(`Updated job ${jobId} status to ${update.status}`);
    } catch (error) {
      logger.error(`Failed to update job ${jobId} status:`, error);
      throw error;
    }
  }

  async uploadJobLogs(jobId: string, logs: string): Promise<void> {
    try {
      await this.api.post(`/api/jobs/${jobId}/logs`, { logs });
      logger.debug(`Uploaded logs for job ${jobId}`);
    } catch (error) {
      logger.error(`Failed to upload logs for job ${jobId}:`, error);
      throw error;
    }
  }

  async uploadJobArtifact(jobId: string, artifactName: string, artifactData: Buffer): Promise<void> {
    try {
      const formData = new FormData();
      formData.append('artifact', new Blob([artifactData]), artifactName);

      await this.api.post(`/api/jobs/${jobId}/artifacts`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      logger.debug(`Uploaded artifact ${artifactName} for job ${jobId}`);
    } catch (error) {
      logger.error(`Failed to upload artifact ${artifactName} for job ${jobId}:`, error);
      throw error;
    }
  }

  async getJobDetails(jobId: string): Promise<any> {
    try {
      const response = await this.api.get(`/api/jobs/${jobId}`);
      return response.data.data.job;
    } catch (error) {
      logger.error(`Failed to get job ${jobId} details:`, error);
      throw error;
    }
  }

  async getProjectSecrets(projectId: string): Promise<Record<string, string>> {
    try {
      const response = await this.api.get(`/api/projects/${projectId}/secrets`);
      return response.data.data.secrets || {};
    } catch (error) {
      logger.error(`Failed to get secrets for project ${projectId}:`, error);
      return {};
    }
  }

  async reportRunnerStatus(status: any): Promise<void> {
    try {
      await this.api.post('/api/runners/status', {
        runnerId: this.runnerId,
        status,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.debug('Failed to report runner status:', error);
      // Don't throw here, status reporting is optional
    }
  }

  async heartbeat(): Promise<void> {
    try {
      await this.api.post('/api/runners/heartbeat', {
        runnerId: this.runnerId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.debug('Failed to send heartbeat:', error);
      // Don't throw here, heartbeat is optional
    }
  }

  getRunnerId(): string {
    return this.runnerId;
  }

  async downloadFile(url: string): Promise<Buffer> {
    try {
      const response = await this.api.get(url, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data);
    } catch (error) {
      logger.error(`Failed to download file from ${url}:`, error);
      throw error;
    }
  }

  async cloneRepository(repoUrl: string, branch: string = 'main', targetDir: string): Promise<void> {
    try {
      await this.api.post('/api/git/clone', {
        repoUrl,
        branch,
        targetDir,
      });
      logger.info(`Cloned repository ${repoUrl} (${branch}) to ${targetDir}`);
    } catch (error) {
      logger.error(`Failed to clone repository ${repoUrl}:`, error);
      throw error;
    }
  }
}
