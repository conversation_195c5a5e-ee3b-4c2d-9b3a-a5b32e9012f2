import Docker from 'dockerode';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import * as tar from 'tar-fs';

export interface ContainerConfig {
  image: string;
  cmd?: string[];
  env?: Record<string, string>;
  workingDir?: string;
  volumes?: Record<string, string>;
  networkMode?: string;
  memory?: number;
  cpus?: number;
}

export interface ExecutionResult {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
}

export class DockerService {
  private docker: Docker;
  private runningContainers: Map<string, Docker.Container> = new Map();

  constructor() {
    this.docker = new Docker({
      socketPath: process.env.DOCKER_HOST || '/var/run/docker.sock',
    });
  }

  async initialize(): Promise<void> {
    try {
      // Test Docker connection
      await this.docker.ping();
      logger.info('Docker connection established');

      // Get Docker info
      const info = await this.docker.info();
      logger.info(`Docker version: ${info.ServerVersion}`);
      logger.info(`Available memory: ${Math.round(info.MemTotal / 1024 / 1024 / 1024)}GB`);
      logger.info(`Available CPUs: ${info.NCPU}`);

      // Pull common base images
      await this.pullCommonImages();
    } catch (error) {
      logger.error('Failed to initialize Docker service:', error);
      throw error;
    }
  }

  private async pullCommonImages(): Promise<void> {
    const commonImages = [
      'node:18-alpine',
      'python:3.11-alpine',
      'golang:1.21-alpine',
      'ubuntu:22.04',
      'alpine:latest',
    ];

    for (const image of commonImages) {
      try {
        logger.info(`Pulling image: ${image}`);
        await this.pullImage(image);
        logger.info(`Successfully pulled: ${image}`);
      } catch (error) {
        logger.warn(`Failed to pull image ${image}:`, error);
      }
    }
  }

  async pullImage(image: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.docker.pull(image, (err: any, stream: any) => {
        if (err) {
          reject(err);
          return;
        }

        this.docker.modem.followProgress(stream, (err: any, res: any) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
    });
  }

  async createContainer(config: ContainerConfig): Promise<string> {
    const containerId = uuidv4();
    const containerName = `chainops-job-${containerId}`;

    try {
      // Ensure image exists
      try {
        await this.docker.getImage(config.image).inspect();
      } catch (error) {
        logger.info(`Image ${config.image} not found locally, pulling...`);
        await this.pullImage(config.image);
      }

      // Prepare environment variables
      const env = Object.entries(config.env || {}).map(([key, value]) => `${key}=${value}`);

      // Prepare volume binds
      const binds = Object.entries(config.volumes || {}).map(([host, container]) => `${host}:${container}`);

      // Create container
      const container = await this.docker.createContainer({
        Image: config.image,
        Cmd: config.cmd,
        Env: env,
        WorkingDir: config.workingDir || '/workspace',
        HostConfig: {
          Binds: binds,
          NetworkMode: config.networkMode || 'bridge',
          Memory: config.memory ? config.memory * 1024 * 1024 : undefined, // Convert MB to bytes
          CpuQuota: config.cpus ? config.cpus * 100000 : undefined,
          CpuPeriod: config.cpus ? 100000 : undefined,
          AutoRemove: true,
        },
        name: containerName,
        AttachStdout: true,
        AttachStderr: true,
      });

      this.runningContainers.set(containerId, container);
      logger.info(`Created container: ${containerName}`);

      return containerId;
    } catch (error) {
      logger.error('Failed to create container:', error);
      throw error;
    }
  }

  async executeContainer(containerId: string): Promise<ExecutionResult> {
    const container = this.runningContainers.get(containerId);
    if (!container) {
      throw new Error(`Container ${containerId} not found`);
    }

    const startTime = Date.now();
    let stdout = '';
    let stderr = '';

    try {
      // Start container
      const stream = await container.attach({
        stream: true,
        stdout: true,
        stderr: true,
      });

      // Capture output
      stream.on('data', (chunk: Buffer) => {
        const data = chunk.toString();
        if (chunk[0] === 1) {
          stdout += data.slice(8); // Remove Docker stream header
        } else if (chunk[0] === 2) {
          stderr += data.slice(8); // Remove Docker stream header
        }
      });

      await container.start();

      // Wait for container to finish
      const result = await container.wait();
      const duration = Date.now() - startTime;

      // Clean up
      this.runningContainers.delete(containerId);

      return {
        exitCode: result.StatusCode,
        stdout,
        stderr,
        duration,
      };
    } catch (error) {
      this.runningContainers.delete(containerId);
      logger.error('Failed to execute container:', error);
      throw error;
    }
  }

  async stopContainer(containerId: string): Promise<void> {
    const container = this.runningContainers.get(containerId);
    if (!container) {
      return;
    }

    try {
      await container.stop({ t: 10 }); // 10 second grace period
      this.runningContainers.delete(containerId);
      logger.info(`Stopped container: ${containerId}`);
    } catch (error) {
      logger.error(`Failed to stop container ${containerId}:`, error);
      // Try to force kill
      try {
        await container.kill();
        this.runningContainers.delete(containerId);
        logger.info(`Force killed container: ${containerId}`);
      } catch (killError) {
        logger.error(`Failed to kill container ${containerId}:`, killError);
      }
    }
  }

  async copyToContainer(containerId: string, sourcePath: string, targetPath: string): Promise<void> {
    const container = this.runningContainers.get(containerId);
    if (!container) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      const tarStream = tar.pack(sourcePath);
      await container.putArchive(tarStream, { path: targetPath });
      logger.debug(`Copied ${sourcePath} to container ${containerId}:${targetPath}`);
    } catch (error) {
      logger.error('Failed to copy to container:', error);
      throw error;
    }
  }

  async copyFromContainer(containerId: string, sourcePath: string, targetPath: string): Promise<void> {
    const container = this.runningContainers.get(containerId);
    if (!container) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      const stream = await container.getArchive({ path: sourcePath });
      const extractStream = tar.extract(targetPath);
      
      stream.pipe(extractStream);
      
      await new Promise((resolve, reject) => {
        extractStream.on('finish', resolve);
        extractStream.on('error', reject);
      });

      logger.debug(`Copied ${sourcePath} from container ${containerId} to ${targetPath}`);
    } catch (error) {
      logger.error('Failed to copy from container:', error);
      throw error;
    }
  }

  async getContainerLogs(containerId: string): Promise<string> {
    const container = this.runningContainers.get(containerId);
    if (!container) {
      throw new Error(`Container ${containerId} not found`);
    }

    try {
      const logs = await container.logs({
        stdout: true,
        stderr: true,
        timestamps: true,
      });

      return logs.toString();
    } catch (error) {
      logger.error('Failed to get container logs:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up running containers...');
    
    const cleanupPromises = Array.from(this.runningContainers.keys()).map(
      containerId => this.stopContainer(containerId)
    );

    await Promise.allSettled(cleanupPromises);
    
    // Clean up dangling images and containers
    try {
      await this.docker.pruneContainers();
      await this.docker.pruneImages({ filters: { dangling: { true: true } } });
      logger.info('Docker cleanup completed');
    } catch (error) {
      logger.warn('Failed to prune Docker resources:', error);
    }
  }

  getRunningContainers(): string[] {
    return Array.from(this.runningContainers.keys());
  }

  async getSystemInfo(): Promise<any> {
    try {
      const info = await this.docker.info();
      return {
        version: info.ServerVersion,
        containers: info.Containers,
        containersRunning: info.ContainersRunning,
        images: info.Images,
        memoryTotal: info.MemTotal,
        cpus: info.NCPU,
      };
    } catch (error) {
      logger.error('Failed to get Docker system info:', error);
      return null;
    }
  }
}
