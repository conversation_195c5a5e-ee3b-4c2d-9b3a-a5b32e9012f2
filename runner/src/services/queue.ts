import Bull, { Queue, Job } from 'bull';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface JobData {
  id: string;
  type: string;
  payload: any;
  userId: string;
  projectId?: string;
  pipelineId?: string;
  runId?: string;
}

export class QueueService {
  private redis: Redis;
  private jobQueue: Queue;
  private isProcessing = false;

  constructor() {
    // Initialize Redis connection
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    this.redis = new Redis(redisUrl, {
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      lazyConnect: true,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });

    // Initialize job queue
    this.jobQueue = new Bull('job-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.setupEventHandlers();
  }

  private getRedisHost(): string {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.hostname;
  }

  private getRedisPort(): number {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return parseInt(url.port) || 6379;
  }

  private getRedisPassword(): string | undefined {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.password || undefined;
  }

  private setupEventHandlers(): void {
    this.jobQueue.on('completed', (job: Job) => {
      logger.info(`Job ${job.id} completed successfully`);
    });

    this.jobQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Job ${job.id} failed:`, err);
    });

    this.jobQueue.on('stalled', (job: Job) => {
      logger.warn(`Job ${job.id} stalled`);
    });

    this.jobQueue.on('progress', (job: Job, progress: number) => {
      logger.debug(`Job ${job.id} progress: ${progress}%`);
    });

    this.redis.on('connect', () => {
      logger.info('Connected to Redis');
    });

    this.redis.on('error', (error) => {
      logger.error('Redis connection error:', error);
    });

    this.redis.on('close', () => {
      logger.warn('Redis connection closed');
    });
  }

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      logger.info('Queue service connected to Redis');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  async startProcessing(processor: (job: Job<JobData>) => Promise<any>): Promise<void> {
    if (this.isProcessing) {
      logger.warn('Job processing already started');
      return;
    }

    this.isProcessing = true;
    
    // Process jobs with concurrency
    const concurrency = parseInt(process.env.RUNNER_CONCURRENCY || '2');
    
    this.jobQueue.process('execute-job', concurrency, async (job: Job<JobData>) => {
      logger.info(`Processing job ${job.id} of type ${job.data.type}`);
      
      try {
        const result = await processor(job);
        logger.info(`Job ${job.id} completed`);
        return result;
      } catch (error) {
        logger.error(`Job ${job.id} failed:`, error);
        throw error;
      }
    });

    logger.info(`Started processing jobs with concurrency: ${concurrency}`);
  }

  async stopProcessing(): Promise<void> {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    
    try {
      await this.jobQueue.pause(true); // Pause and wait for current jobs to complete
      logger.info('Job processing stopped');
    } catch (error) {
      logger.error('Error stopping job processing:', error);
    }
  }

  async stop(): Promise<void> {
    try {
      await this.stopProcessing();
      await this.jobQueue.close();
      await this.redis.quit();
      logger.info('Queue service stopped');
    } catch (error) {
      logger.error('Error stopping queue service:', error);
      throw error;
    }
  }

  async getQueueStats(): Promise<any> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.jobQueue.getWaiting(),
        this.jobQueue.getActive(),
        this.jobQueue.getCompleted(),
        this.jobQueue.getFailed(),
        this.jobQueue.getDelayed(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
      };
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      return null;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      logger.error('Queue service health check failed:', error);
      return false;
    }
  }

  // Method to update job progress
  async updateJobProgress(job: Job, progress: number, message?: string): Promise<void> {
    try {
      await job.progress(progress);
      if (message) {
        logger.info(`Job ${job.id}: ${message} (${progress}%)`);
      }
    } catch (error) {
      logger.error(`Failed to update job ${job.id} progress:`, error);
    }
  }

  // Method to add log to job
  async addJobLog(job: Job, log: string): Promise<void> {
    try {
      const currentLogs = job.data.logs || '';
      job.data.logs = currentLogs + log + '\n';
      await job.update(job.data);
    } catch (error) {
      logger.error(`Failed to add log to job ${job.id}:`, error);
    }
  }

  // Get active jobs for this runner
  async getActiveJobs(): Promise<Job<JobData>[]> {
    try {
      return await this.jobQueue.getActive();
    } catch (error) {
      logger.error('Failed to get active jobs:', error);
      return [];
    }
  }

  // Cancel a specific job
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.jobQueue.getJob(jobId);
      if (job) {
        await job.remove();
        logger.info(`Cancelled job ${jobId}`);
        return true;
      }
      return false;
    } catch (error) {
      logger.error(`Failed to cancel job ${jobId}:`, error);
      return false;
    }
  }

  // Get job by ID
  async getJob(jobId: string): Promise<Job<JobData> | null> {
    try {
      return await this.jobQueue.getJob(jobId);
    } catch (error) {
      logger.error(`Failed to get job ${jobId}:`, error);
      return null;
    }
  }
}
