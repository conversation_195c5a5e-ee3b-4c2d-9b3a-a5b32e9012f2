import { Job } from 'bull';
import { logger } from '../utils/logger';
import { DockerService, ContainerConfig, ExecutionResult } from './docker';
import { QueueService, JobData } from './queue';
import { ApiService, JobUpdate } from './api';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface RunnerInfo {
  id: string;
  version: string;
  status: 'idle' | 'busy' | 'error';
  activeJobs: number;
  totalJobsProcessed: number;
  systemInfo: any;
}

export class RunnerService {
  private apiService: ApiService;
  private dockerService: DockerService;
  private queueService: QueueService;
  private isRunning = false;
  private totalJobsProcessed = 0;
  private heartbeatInterval?: NodeJS.Timeout;
  private workspaceDir: string;

  constructor(
    apiService: ApiService,
    dockerService: DockerService,
    queueService: QueueService
  ) {
    this.apiService = apiService;
    this.dockerService = dockerService;
    this.queueService = queueService;
    this.workspaceDir = process.env.WORKSPACE_DIR || '/workspace';

    // Ensure workspace directory exists
    if (!fs.existsSync(this.workspaceDir)) {
      fs.mkdirSync(this.workspaceDir, { recursive: true });
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Runner service already running');
      return;
    }

    this.isRunning = true;

    // Start processing jobs
    await this.queueService.startProcessing(this.processJob.bind(this));

    // Start heartbeat
    this.startHeartbeat();

    logger.info('Runner service started');
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // Stop heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Stop processing jobs
    await this.queueService.stopProcessing();

    // Clean up Docker containers
    await this.dockerService.cleanup();

    logger.info('Runner service stopped');
  }

  private async processJob(job: Job<JobData>): Promise<any> {
    const jobId = job.data.id;
    const jobType = job.data.type;

    logger.info(`Starting job ${jobId} of type ${jobType}`);

    try {
      // Update job status to running
      await this.updateJobStatus(job, {
        status: 'RUNNING',
        startedAt: new Date(),
      });

      let result: any;

      switch (jobType) {
        case 'pipeline-run':
          result = await this.processPipelineRun(job);
          break;
        case 'job-execution':
          result = await this.processJobExecution(job);
          break;
        default:
          throw new Error(`Unknown job type: ${jobType}`);
      }

      // Update job status to success
      await this.updateJobStatus(job, {
        status: 'SUCCESS',
        finishedAt: new Date(),
      });

      this.totalJobsProcessed++;
      logger.info(`Job ${jobId} completed successfully`);

      return result;
    } catch (error) {
      logger.error(`Job ${jobId} failed:`, error);

      // Update job status to failed
      await this.updateJobStatus(job, {
        status: 'FAILED',
        finishedAt: new Date(),
        logs: `Job failed: ${error instanceof Error ? error.message : String(error)}`,
      });

      throw error;
    }
  }

  private async processPipelineRun(job: Job<JobData>): Promise<any> {
    const { payload } = job.data;
    const { pipelineRunId, config, variables = {} } = payload;

    logger.info(`Processing pipeline run ${pipelineRunId}`);

    // Create workspace for this pipeline run
    const runWorkspace = path.join(this.workspaceDir, `run-${pipelineRunId}`);
    fs.mkdirSync(runWorkspace, { recursive: true });

    try {
      const steps = config.steps || [];
      const results = [];

      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        await this.queueService.updateJobProgress(job, (i / steps.length) * 100, `Executing step: ${step.name}`);

        const stepResult = await this.executeStep(step, runWorkspace, variables, job);
        results.push(stepResult);

        if (stepResult.exitCode !== 0) {
          throw new Error(`Step "${step.name}" failed with exit code ${stepResult.exitCode}`);
        }
      }

      return { results };
    } finally {
      // Clean up workspace
      try {
        fs.rmSync(runWorkspace, { recursive: true, force: true });
      } catch (error) {
        logger.warn(`Failed to clean up workspace ${runWorkspace}:`, error);
      }
    }
  }

  private async processJobExecution(job: Job<JobData>): Promise<any> {
    const { payload } = job.data;
    const { jobConfig, environment = {} } = payload;

    logger.info(`Processing job execution`);

    // Create workspace for this job
    const jobWorkspace = path.join(this.workspaceDir, `job-${job.data.id}`);
    fs.mkdirSync(jobWorkspace, { recursive: true });

    try {
      const result = await this.executeStep(jobConfig, jobWorkspace, environment, job);
      return result;
    } finally {
      // Clean up workspace
      try {
        fs.rmSync(jobWorkspace, { recursive: true, force: true });
      } catch (error) {
        logger.warn(`Failed to clean up workspace ${jobWorkspace}:`, error);
      }
    }
  }

  private async executeStep(
    step: any,
    workspace: string,
    variables: Record<string, any>,
    job: Job<JobData>
  ): Promise<ExecutionResult> {
    logger.info(`Executing step: ${step.name}`);

    // Prepare environment variables
    const env = {
      ...process.env,
      ...variables,
      WORKSPACE: workspace,
      JOB_ID: job.data.id,
      RUNNER_ID: this.apiService.getRunnerId(),
    };

    // Determine container configuration
    const containerConfig: ContainerConfig = {
      image: step.image || 'node:18-alpine',
      cmd: step.run ? ['/bin/sh', '-c', step.run] : step.cmd,
      env,
      workingDir: '/workspace',
      volumes: {
        [workspace]: '/workspace',
      },
      memory: step.memory || 512, // MB
      cpus: step.cpus || 1,
    };

    // Create and execute container
    const containerId = await this.dockerService.createContainer(containerConfig);

    try {
      const result = await this.dockerService.executeContainer(containerId);

      // Stream logs to job
      if (result.stdout || result.stderr) {
        const logs = `STDOUT:\n${result.stdout}\n\nSTDERR:\n${result.stderr}`;
        await this.queueService.addJobLog(job, logs);
        await this.apiService.uploadJobLogs(job.data.id, logs);
      }

      // Check for artifacts
      await this.collectArtifacts(step, workspace, job);

      return result;
    } finally {
      // Container is automatically removed due to AutoRemove: true
    }
  }

  private async collectArtifacts(step: any, workspace: string, job: Job<JobData>): Promise<void> {
    if (!step.artifacts || !Array.isArray(step.artifacts)) {
      return;
    }

    for (const artifactPattern of step.artifacts) {
      try {
        const artifactPath = path.join(workspace, artifactPattern);
        
        if (fs.existsSync(artifactPath)) {
          const stats = fs.statSync(artifactPath);
          
          if (stats.isFile()) {
            const artifactData = fs.readFileSync(artifactPath);
            const artifactName = path.basename(artifactPath);
            
            await this.apiService.uploadJobArtifact(job.data.id, artifactName, artifactData);
            logger.info(`Uploaded artifact: ${artifactName}`);
          } else if (stats.isDirectory()) {
            // TODO: Handle directory artifacts (zip them)
            logger.warn(`Directory artifacts not yet supported: ${artifactPath}`);
          }
        }
      } catch (error) {
        logger.warn(`Failed to collect artifact ${artifactPattern}:`, error);
      }
    }
  }

  private async updateJobStatus(job: Job<JobData>, update: JobUpdate): Promise<void> {
    try {
      await this.apiService.updateJobStatus(job.data.id, update);
    } catch (error) {
      logger.error(`Failed to update job status for ${job.data.id}:`, error);
      // Don't throw here, job should continue even if status update fails
    }
  }

  private startHeartbeat(): void {
    const heartbeatInterval = parseInt(process.env.HEARTBEAT_INTERVAL || '30000'); // 30 seconds

    this.heartbeatInterval = setInterval(async () => {
      try {
        await this.apiService.heartbeat();
        
        // Report runner status
        const status = await this.getRunnerStatus();
        await this.apiService.reportRunnerStatus(status);
      } catch (error) {
        logger.debug('Heartbeat failed:', error);
      }
    }, heartbeatInterval);

    logger.info(`Heartbeat started with interval: ${heartbeatInterval}ms`);
  }

  private async getRunnerStatus(): Promise<any> {
    const activeJobs = await this.queueService.getActiveJobs();
    const queueStats = await this.queueService.getQueueStats();
    const dockerInfo = await this.dockerService.getSystemInfo();

    return {
      status: activeJobs.length > 0 ? 'busy' : 'idle',
      activeJobs: activeJobs.length,
      totalJobsProcessed: this.totalJobsProcessed,
      queueStats,
      dockerInfo,
      memory: process.memoryUsage(),
      uptime: process.uptime(),
    };
  }

  async getRunnerInfo(): Promise<RunnerInfo> {
    const activeJobs = await this.queueService.getActiveJobs();
    const dockerInfo = await this.dockerService.getSystemInfo();

    return {
      id: this.apiService.getRunnerId(),
      version: process.env.npm_package_version || '1.0.0',
      status: activeJobs.length > 0 ? 'busy' : 'idle',
      activeJobs: activeJobs.length,
      totalJobsProcessed: this.totalJobsProcessed,
      systemInfo: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        docker: dockerInfo,
      },
    };
  }
}
