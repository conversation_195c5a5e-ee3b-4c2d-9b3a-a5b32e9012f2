#!/bin/bash

# ChainOps Setup Script
# This script sets up the complete ChainOps CI/CD platform

set -e

echo "🚀 ChainOps Setup Script"
echo "========================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("Node.js (v18+)")
    else
        node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -lt 18 ]; then
            missing_deps+=("Node.js v18+ (current: $(node -v))")
        fi
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists docker; then
        missing_deps+=("Docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("Docker Compose")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing prerequisites:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        echo "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    # Root dependencies
    npm install
    
    # Backend dependencies
    cd backend
    npm install
    cd ..
    
    # Frontend dependencies
    cd frontend
    npm install
    cd ..
    
    # Runner dependencies
    cd runner
    npm install
    cd ..
    
    log_success "Dependencies installed"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        log_success "Created backend/.env"
    else
        log_warning "backend/.env already exists, skipping"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        cp frontend/.env.local.example frontend/.env.local
        log_success "Created frontend/.env.local"
    else
        log_warning "frontend/.env.local already exists, skipping"
    fi
    
    # Runner environment
    if [ ! -f "runner/.env" ]; then
        cp runner/.env.example runner/.env
        log_success "Created runner/.env"
    else
        log_warning "runner/.env already exists, skipping"
    fi
}

# Start Docker services
start_docker_services() {
    log_info "Starting Docker services (PostgreSQL, Redis, MinIO)..."
    
    docker-compose -f docker-compose.dev.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Check if PostgreSQL is ready
    local retries=30
    while [ $retries -gt 0 ]; do
        if docker exec chainops-postgres-dev pg_isready -U chainops >/dev/null 2>&1; then
            break
        fi
        retries=$((retries - 1))
        sleep 1
    done
    
    if [ $retries -eq 0 ]; then
        log_error "PostgreSQL failed to start"
        exit 1
    fi
    
    log_success "Docker services started"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    cd backend
    
    # Generate Prisma client
    npx prisma generate
    log_success "Generated Prisma client"
    
    # Run migrations
    npx prisma migrate dev --name init
    log_success "Database migrations completed"
    
    # Seed database
    npm run db:seed
    log_success "Database seeded with demo data"
    
    cd ..
}

# Build applications
build_applications() {
    log_info "Building applications..."
    
    # Build backend
    cd backend
    npm run build
    cd ..
    log_success "Backend built"
    
    # Build frontend
    cd frontend
    npm run build
    cd ..
    log_success "Frontend built"
    
    # Build runner
    cd runner
    npm run build
    cd ..
    log_success "Runner built"
}

# Display final information
display_final_info() {
    echo ""
    echo "🎉 ChainOps setup completed successfully!"
    echo "========================================"
    echo ""
    echo "📋 Demo Credentials:"
    echo "  Admin: <EMAIL> / admin123"
    echo "  Demo User: <EMAIL> / demo123"
    echo ""
    echo "🌐 Service URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:3001"
    echo "  API Health: http://localhost:3001/health"
    echo "  MinIO Console: http://localhost:9001 (chainops / chainops_password)"
    echo "  Prisma Studio: npx prisma studio (from backend directory)"
    echo ""
    echo "🚀 To start development:"
    echo "  make dev                 # Start all development servers"
    echo "  make dev-backend         # Start only backend"
    echo "  make dev-frontend        # Start only frontend"
    echo "  make dev-runner          # Start only runner"
    echo ""
    echo "🐳 Docker commands:"
    echo "  make docker-up           # Start all services with Docker"
    echo "  make docker-down         # Stop Docker services"
    echo "  make logs                # View logs"
    echo ""
    echo "🔧 Useful commands:"
    echo "  make db-studio           # Open database admin interface"
    echo "  make health              # Check service health"
    echo "  make test                # Run tests"
    echo "  make lint                # Run linting"
    echo ""
    echo "📚 Documentation:"
    echo "  README.md                # Main documentation"
    echo "  backend/README.md        # Backend documentation"
    echo "  frontend/README.md       # Frontend documentation"
    echo "  runner/README.md         # Runner documentation"
    echo ""
}

# Main setup function
main() {
    echo "This script will set up the complete ChainOps CI/CD platform."
    echo "It will:"
    echo "  1. Check prerequisites"
    echo "  2. Install dependencies"
    echo "  3. Setup environment files"
    echo "  4. Start Docker services"
    echo "  5. Setup database"
    echo "  6. Build applications"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
    
    echo ""
    
    check_prerequisites
    install_dependencies
    setup_environment
    start_docker_services
    setup_database
    build_applications
    display_final_info
}

# Run main function
main "$@"
