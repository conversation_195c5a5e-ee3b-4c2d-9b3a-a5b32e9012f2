import { Request, Response, NextFunction } from 'express';
import { notFoundHandler } from '../notFoundHandler';
import { CustomError } from '../errorHandler';

describe('notFoundHandler', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/test-route',
      method: 'GET',
      path: '/test-route',
      url: '/test-route',
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('basic functionality', () => {
    it('should call next with CustomError for not found route', () => {
      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
      expect(mockNext).toHaveBeenCalledWith(expect.any(CustomError));
      
      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /test-route not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle different route paths', () => {
      mockRequest.originalUrl = '/api/v1/users/123';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/v1/users/123 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle root path', () => {
      mockRequest.originalUrl = '/';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route / not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle empty path', () => {
      mockRequest.originalUrl = '';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route  not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle undefined originalUrl', () => {
      mockRequest.originalUrl = undefined;

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route undefined not found');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('different HTTP methods', () => {
    it('should handle GET request', () => {
      mockRequest.method = 'GET';
      mockRequest.originalUrl = '/api/users';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle POST request', () => {
      mockRequest.method = 'POST';
      mockRequest.originalUrl = '/api/users';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle PUT request', () => {
      mockRequest.method = 'PUT';
      mockRequest.originalUrl = '/api/users/123';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users/123 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle DELETE request', () => {
      mockRequest.method = 'DELETE';
      mockRequest.originalUrl = '/api/users/123';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users/123 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle PATCH request', () => {
      mockRequest.method = 'PATCH';
      mockRequest.originalUrl = '/api/users/123';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users/123 not found');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('complex routes', () => {
    it('should handle routes with query parameters', () => {
      mockRequest.originalUrl = '/api/users?page=1&limit=10';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users?page=1&limit=10 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle routes with fragments', () => {
      mockRequest.originalUrl = '/api/users#section1';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users#section1 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle routes with special characters', () => {
      mockRequest.originalUrl = '/api/users/@special-user/profile';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users/@special-user/profile not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle very long routes', () => {
      const longRoute = '/api/' + 'a'.repeat(100) + '/users/123';
      mockRequest.originalUrl = longRoute;

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe(`Route ${longRoute} not found`);
      expect(error.statusCode).toBe(404);
    });

    it('should handle routes with encoded characters', () => {
      mockRequest.originalUrl = '/api/users/john%20doe';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route /api/users/john%20doe not found');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('error object properties', () => {
    it('should create CustomError with correct properties', () => {
      mockRequest.originalUrl = '/test-route';

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error).toBeInstanceOf(CustomError);
      expect(error.message).toBe('Route /test-route not found');
      expect(error.statusCode).toBe(404);
      expect(error.name).toBe('CustomError');
    });

    it('should not modify response object', () => {
      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(mockResponse.send).not.toHaveBeenCalled();
    });

    it('should call next exactly once', () => {
      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });

  describe('edge cases', () => {
    it('should handle null originalUrl', () => {
      mockRequest.originalUrl = null as any;

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route null not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle numeric originalUrl', () => {
      mockRequest.originalUrl = 123 as any;

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route 123 not found');
      expect(error.statusCode).toBe(404);
    });

    it('should handle object originalUrl', () => {
      mockRequest.originalUrl = { path: '/test' } as any;

      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      expect(error.message).toBe('Route [object Object] not found');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('concurrent requests', () => {
    it('should handle multiple concurrent requests', () => {
      const requests = [
        { originalUrl: '/route1' },
        { originalUrl: '/route2' },
        { originalUrl: '/route3' },
      ];

      const nextFunctions = [jest.fn(), jest.fn(), jest.fn()];

      requests.forEach((req, index) => {
        notFoundHandler(req as Request, mockResponse as Response, nextFunctions[index]);
      });

      nextFunctions.forEach((nextFn, index) => {
        expect(nextFn).toHaveBeenCalledTimes(1);
        const error = nextFn.mock.calls[0][0];
        expect(error.message).toBe(`Route /route${index + 1} not found`);
        expect(error.statusCode).toBe(404);
      });
    });
  });

  describe('integration with error handling', () => {
    it('should create error that can be handled by error middleware', () => {
      notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);

      const error = (mockNext as jest.Mock).mock.calls[0][0];
      
      // Verify the error has all properties needed by error handler
      expect(error.message).toBeDefined();
      expect(error.statusCode).toBeDefined();
      expect(error.name).toBeDefined();
      expect(typeof error.message).toBe('string');
      expect(typeof error.statusCode).toBe('number');
      expect(error.statusCode).toBe(404);
    });
  });
});
