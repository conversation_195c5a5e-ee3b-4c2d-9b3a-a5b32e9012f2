import { DatabaseService } from '../database';

// Mock Prisma Client
const mockPrismaClient = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $executeRaw: jest.fn(),
  user: {
    findMany: jest.fn(),
  },
};

// Mock the Prisma Client constructor
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrismaClient),
}));

// Mock console.log to avoid noise in tests
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('DatabaseService', () => {
  beforeEach(() => {
    // Reset singleton instance
    (DatabaseService as any).instance = null;
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBeDefined();
    });

    it('should create new instance if none exists', () => {
      const instance = DatabaseService.getInstance();
      expect(instance).toBeDefined();
    });

    it('should reuse existing instance', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initialize', () => {
    it('should initialize database service', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
      expect(mockPrismaClient.$connect).toHaveBeenCalled();
    });

    it('should handle multiple initialization calls', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      await DatabaseService.initialize();
      await DatabaseService.initialize();
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(2);
    });

    it('should handle initialization errors', async () => {
      mockPrismaClient.$connect.mockRejectedValue(new Error('Connection failed'));
      await expect(DatabaseService.initialize()).rejects.toThrow('Connection failed');
    });
  });

  describe('close', () => {
    it('should close database connection', async () => {
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);
      await expect(DatabaseService.close()).resolves.not.toThrow();
      expect(mockPrismaClient.$disconnect).toHaveBeenCalled();
    });

    it('should handle multiple close calls', async () => {
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);
      await DatabaseService.close();
      await DatabaseService.close();
      expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(2);
    });

    it('should handle close errors', async () => {
      mockPrismaClient.$disconnect.mockRejectedValue(new Error('Disconnect failed'));
      await expect(DatabaseService.close()).rejects.toThrow('Disconnect failed');
    });
  });

  describe('getClient', () => {
    it('should return prisma client', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
      expect(typeof client.$connect).toBe('function');
      expect(typeof client.$disconnect).toBe('function');
    });

    it('should return same client instance', () => {
      const client1 = DatabaseService.getClient();
      const client2 = DatabaseService.getClient();
      expect(client1).toBe(client2);
    });
  });

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(true);
      expect(mockPrismaClient.$executeRaw).toHaveBeenCalled();
    });

    it('should handle health check errors gracefully', async () => {
      mockPrismaClient.$executeRaw.mockRejectedValue(new Error('Database error'));
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should return false for invalid response', async () => {
      mockPrismaClient.$executeRaw.mockResolvedValue(null);
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);
    });
  });

  describe('runMigrations', () => {
    it('should run migrations in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should run migrations in test environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'test';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle undefined NODE_ENV', async () => {
      const originalEnv = process.env.NODE_ENV;
      delete process.env.NODE_ENV;

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle empty NODE_ENV', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = '';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle staging environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'staging';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle custom environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'custom';

      await expect(DatabaseService.runMigrations()).resolves.not.toThrow();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('error scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      const initPromises = [
        DatabaseService.initialize(),
        DatabaseService.initialize(),
        DatabaseService.initialize(),
      ];
      await Promise.all(initPromises);

      const closePromises = [
        DatabaseService.close(),
        DatabaseService.close(),
        DatabaseService.close(),
      ];
      await Promise.all(closePromises);

      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(3);
      expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(3);
    });

    it('should handle service lifecycle', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.initialize();
      const isHealthy = await DatabaseService.healthCheck();
      const client = DatabaseService.getClient();
      await DatabaseService.close();

      expect(isHealthy).toBe(true);
      expect(client).toBeDefined();
    });

    it('should handle initialize after close', async () => {
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);
      mockPrismaClient.$connect.mockResolvedValue(undefined);

      await DatabaseService.close();
      await expect(DatabaseService.initialize()).resolves.not.toThrow();
    });

    it('should handle getClient when not initialized', () => {
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();
    });
  });

  describe('integration scenarios', () => {
    it('should handle full service workflow', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.initialize();
      const client = DatabaseService.getClient();
      expect(client).toBeDefined();

      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(true);

      await DatabaseService.runMigrations();
      await DatabaseService.close();
    });

    it('should handle multiple service instances', () => {
      const instance1 = DatabaseService.getInstance();
      const instance2 = DatabaseService.getInstance();
      const instance3 = DatabaseService.getInstance();

      expect(instance1).toBe(instance2);
      expect(instance2).toBe(instance3);
    });

    it('should handle rapid operations', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      const operations = [];
      
      for (let i = 0; i < 5; i++) {
        operations.push(DatabaseService.initialize());
        operations.push(DatabaseService.healthCheck());
        operations.push(DatabaseService.close());
      }

      await Promise.all(operations);
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(5);
      expect(mockPrismaClient.$executeRaw).toHaveBeenCalledTimes(5);
      expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(5);
    });
  });

  describe('edge cases', () => {
    it('should handle service state consistency', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      const client1 = DatabaseService.getClient();
      await DatabaseService.initialize();
      const client2 = DatabaseService.getClient();
      await DatabaseService.close();
      const client3 = DatabaseService.getClient();

      expect(client1).toBe(client2);
      expect(client2).toBe(client3);
    });

    it('should handle null/undefined responses', async () => {
      mockPrismaClient.$executeRaw.mockResolvedValue(undefined);
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should handle network timeouts', async () => {
      mockPrismaClient.$executeRaw.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );

      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should handle database connection failures', async () => {
      mockPrismaClient.$connect.mockRejectedValue(new Error('Connection refused'));

      try {
        await DatabaseService.initialize();
      } catch (error) {
        expect(error.message).toBe('Connection refused');
      }
    });

    it('should handle migration errors in development', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // Mock console.log to capture migration logs
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await DatabaseService.runMigrations();
      expect(consoleSpy).toHaveBeenCalledWith('Running database migrations...');

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });

    it('should skip migrations in production environment', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await DatabaseService.runMigrations();
      expect(consoleSpy).toHaveBeenCalledWith('Skipping migrations in production');

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalEnv;
    });

    it('should handle singleton instance creation', () => {
      // Reset instance
      (DatabaseService as any).instance = null;

      const instance1 = DatabaseService.getInstance();
      expect(instance1).toBeDefined();

      // Verify singleton behavior
      const instance2 = DatabaseService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should handle client creation and reuse', () => {
      const client1 = DatabaseService.getClient();
      const client2 = DatabaseService.getClient();

      expect(client1).toBe(client2);
      expect(client1).toBe(mockPrismaClient);
    });

    it('should handle health check with different response types', async () => {
      // Test with number response
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      let isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(true);

      // Test with zero response
      mockPrismaClient.$executeRaw.mockResolvedValue(0);
      isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);

      // Test with null response
      mockPrismaClient.$executeRaw.mockResolvedValue(null);
      isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);
    });

    it('should handle concurrent health checks', async () => {
      mockPrismaClient.$executeRaw.mockResolvedValue(1);

      const healthPromises = [
        DatabaseService.healthCheck(),
        DatabaseService.healthCheck(),
        DatabaseService.healthCheck(),
      ];

      const results = await Promise.all(healthPromises);
      results.forEach(result => expect(result).toBe(true));
      expect(mockPrismaClient.$executeRaw).toHaveBeenCalledTimes(3);
    });

    it('should handle environment variable edge cases', async () => {
      const originalEnv = process.env.NODE_ENV;

      // Test with null NODE_ENV
      process.env.NODE_ENV = null as any;
      await DatabaseService.runMigrations();

      // Test with empty string NODE_ENV
      process.env.NODE_ENV = '';
      await DatabaseService.runMigrations();

      // Test with whitespace NODE_ENV
      process.env.NODE_ENV = '   ';
      await DatabaseService.runMigrations();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle service cleanup', async () => {
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      await DatabaseService.close();
      expect(mockPrismaClient.$disconnect).toHaveBeenCalled();

      // Multiple close calls should be safe
      await DatabaseService.close();
      expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(2);
    });
  });

  describe('comprehensive integration tests', () => {
    it('should handle complete service lifecycle with errors', async () => {
      // Initialize with success
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      await DatabaseService.initialize();

      // Health check with failure
      mockPrismaClient.$executeRaw.mockRejectedValue(new Error('Query failed'));
      const isHealthy = await DatabaseService.healthCheck();
      expect(isHealthy).toBe(false);

      // Close with success
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);
      await DatabaseService.close();
    });

    it('should handle rapid successive operations', async () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      mockPrismaClient.$executeRaw.mockResolvedValue(1);
      mockPrismaClient.$disconnect.mockResolvedValue(undefined);

      // Rapid operations
      for (let i = 0; i < 10; i++) {
        await DatabaseService.initialize();
        await DatabaseService.healthCheck();
        await DatabaseService.close();
      }

      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(10);
      expect(mockPrismaClient.$executeRaw).toHaveBeenCalledTimes(10);
      expect(mockPrismaClient.$disconnect).toHaveBeenCalledTimes(10);
    });

    it('should handle mixed success and failure scenarios', async () => {
      // Success then failure pattern
      mockPrismaClient.$connect
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Connection failed'))
        .mockResolvedValueOnce(undefined);

      await DatabaseService.initialize(); // Success

      try {
        await DatabaseService.initialize(); // Failure
      } catch (error) {
        expect(error.message).toBe('Connection failed');
      }

      await DatabaseService.initialize(); // Success again
    });
  });
});
