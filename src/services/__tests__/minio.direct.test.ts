// Direct test file to ensure Je<PERSON> recognizes Minio Service coverage
import { MinioService } from '../minio';

// Mock Minio Client with comprehensive coverage
const mockMinioClient = {
  bucketExists: jest.fn(),
  makeBucket: jest.fn(),
  putObject: jest.fn(),
  getObject: jest.fn(),
  removeObject: jest.fn(),
  listObjects: jest.fn(),
  statObject: jest.fn(),
  presignedGetObject: jest.fn(),
  presignedPutObject: jest.fn(),
};

// Mock Minio constructor
jest.mock('minio', () => ({
  Client: jest.fn(() => mockMinioClient),
}));

// Mock console.log to avoid noise
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('MinioService Direct Coverage Tests', () => {
  beforeEach(() => {
    // Reset singleton instance
    (MinioService as any).instance = null;
    jest.clearAllMocks();
  });

  describe('Core Service Methods', () => {
    it('should test getInstance method directly', () => {
      const instance1 = MinioService.getInstance();
      const instance2 = MinioService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should test initialize when bucket exists', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      
      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).not.toHaveBeenCalled();
    });

    it('should test initialize when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockResolvedValue(undefined);
      
      await MinioService.initialize();
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
      expect(mockMinioClient.makeBucket).toHaveBeenCalled();
    });

    it('should test initialize with bucket check error', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Bucket check failed'));
      
      await expect(MinioService.initialize()).rejects.toThrow('Bucket check failed');
    });

    it('should test initialize with bucket creation error', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockRejectedValue(new Error('Bucket creation failed'));
      
      await expect(MinioService.initialize()).rejects.toThrow('Bucket creation failed');
    });

    it('should test healthCheck with success', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      
      const result = await MinioService.healthCheck();
      expect(result).toBe(true);
      expect(mockMinioClient.bucketExists).toHaveBeenCalled();
    });

    it('should test healthCheck with error', async () => {
      mockMinioClient.bucketExists.mockRejectedValue(new Error('Health check failed'));
      
      const result = await MinioService.healthCheck();
      expect(result).toBe(false);
    });

    it('should test healthCheck when bucket does not exist', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      
      const result = await MinioService.healthCheck();
      expect(result).toBe(false);
    });
  });

  describe('File Operations', () => {
    it('should test uploadFile with success', async () => {
      const mockEtag = 'test-etag-123';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test content');
      const result = await MinioService.uploadFile('test.txt', buffer);
      
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer
      );
    });

    it('should test uploadFile with metadata', async () => {
      const mockEtag = 'test-etag-456';
      mockMinioClient.putObject.mockResolvedValue({ etag: mockEtag });
      
      const buffer = Buffer.from('test content');
      const metadata = { 'Content-Type': 'text/plain' };
      const result = await MinioService.uploadFile('test.txt', buffer, metadata);
      
      expect(result).toBe(mockEtag);
      expect(mockMinioClient.putObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        buffer,
        metadata
      );
    });

    it('should test uploadFile with error', async () => {
      mockMinioClient.putObject.mockRejectedValue(new Error('Upload failed'));
      
      const buffer = Buffer.from('test content');
      await expect(MinioService.uploadFile('test.txt', buffer)).rejects.toThrow('Upload failed');
    });

    it('should test downloadFile with success', async () => {
      const mockStream = { pipe: jest.fn(), on: jest.fn() };
      mockMinioClient.getObject.mockResolvedValue(mockStream);
      
      const result = await MinioService.downloadFile('test.txt');
      expect(result).toBe(mockStream);
      expect(mockMinioClient.getObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt'
      );
    });

    it('should test downloadFile with error', async () => {
      mockMinioClient.getObject.mockRejectedValue(new Error('Download failed'));
      
      await expect(MinioService.downloadFile('test.txt')).rejects.toThrow('Download failed');
    });

    it('should test deleteFile with success', async () => {
      mockMinioClient.removeObject.mockResolvedValue(undefined);
      
      await MinioService.deleteFile('test.txt');
      expect(mockMinioClient.removeObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt'
      );
    });

    it('should test deleteFile with error', async () => {
      mockMinioClient.removeObject.mockRejectedValue(new Error('Delete failed'));
      
      await expect(MinioService.deleteFile('test.txt')).rejects.toThrow('Delete failed');
    });

    it('should test getFileInfo with success', async () => {
      const mockStat = {
        size: 1024,
        lastModified: new Date(),
        etag: 'test-etag',
        contentType: 'text/plain',
      };
      mockMinioClient.statObject.mockResolvedValue(mockStat);
      
      const result = await MinioService.getFileInfo('test.txt');
      expect(result).toBe(mockStat);
      expect(mockMinioClient.statObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt'
      );
    });

    it('should test getFileInfo with error', async () => {
      mockMinioClient.statObject.mockRejectedValue(new Error('Stat failed'));
      
      await expect(MinioService.getFileInfo('test.txt')).rejects.toThrow('Stat failed');
    });
  });

  describe('List Operations', () => {
    it('should test listFiles with success', async () => {
      const mockObjects = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 },
      ];
      
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            mockObjects.forEach(obj => callback(obj));
          } else if (event === 'end') {
            callback();
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      const result = await MinioService.listFiles();
      expect(result).toEqual(mockObjects);
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        undefined,
        true
      );
    });

    it('should test listFiles with prefix', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'end') callback();
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await MinioService.listFiles('logs/');
      expect(mockMinioClient.listObjects).toHaveBeenCalledWith(
        expect.any(String),
        'logs/',
        true
      );
    });

    it('should test listFiles with error', async () => {
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            callback(new Error('List failed'));
          }
        }),
      };
      
      mockMinioClient.listObjects.mockReturnValue(mockStream);
      
      await expect(MinioService.listFiles()).rejects.toThrow('List failed');
    });
  });

  describe('Presigned URL Operations', () => {
    it('should test getPresignedUrl for GET', async () => {
      const mockUrl = 'https://example.com/test.txt?signature=abc123';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      const result = await MinioService.getPresignedUrl('test.txt', 'GET');
      expect(result).toBe(mockUrl);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        3600
      );
    });

    it('should test getPresignedUrl for PUT', async () => {
      const mockUrl = 'https://example.com/test.txt?signature=def456';
      mockMinioClient.presignedPutObject.mockResolvedValue(mockUrl);
      
      const result = await MinioService.getPresignedUrl('test.txt', 'PUT');
      expect(result).toBe(mockUrl);
      expect(mockMinioClient.presignedPutObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        3600
      );
    });

    it('should test getPresignedUrl with custom expiry', async () => {
      const mockUrl = 'https://example.com/test.txt?signature=ghi789';
      mockMinioClient.presignedGetObject.mockResolvedValue(mockUrl);
      
      await MinioService.getPresignedUrl('test.txt', 'GET', 7200);
      expect(mockMinioClient.presignedGetObject).toHaveBeenCalledWith(
        expect.any(String),
        'test.txt',
        7200
      );
    });

    it('should test getPresignedUrl with error', async () => {
      mockMinioClient.presignedGetObject.mockRejectedValue(new Error('Presign failed'));
      
      await expect(MinioService.getPresignedUrl('test.txt', 'GET')).rejects.toThrow('Presign failed');
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle complete file workflow', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(true);
      mockMinioClient.putObject.mockResolvedValue({ etag: 'workflow-etag' });
      mockMinioClient.getObject.mockResolvedValue({ pipe: jest.fn() });
      mockMinioClient.statObject.mockResolvedValue({ size: 1024 });
      mockMinioClient.removeObject.mockResolvedValue(undefined);
      
      await MinioService.initialize();
      
      const buffer = Buffer.from('workflow test');
      const etag = await MinioService.uploadFile('workflow.txt', buffer);
      const stream = await MinioService.downloadFile('workflow.txt');
      const info = await MinioService.getFileInfo('workflow.txt');
      await MinioService.deleteFile('workflow.txt');
      
      expect(etag).toBe('workflow-etag');
      expect(stream).toBeDefined();
      expect(info.size).toBe(1024);
    });

    it('should handle service initialization lifecycle', async () => {
      mockMinioClient.bucketExists.mockResolvedValue(false);
      mockMinioClient.makeBucket.mockResolvedValue(undefined);
      
      await MinioService.initialize();
      
      const isHealthy = await MinioService.healthCheck();
      expect(isHealthy).toBe(false); // Because we mocked bucketExists to return false for health check
    });
  });
});
