// Direct test file to ensure Je<PERSON> recognizes Queue Service coverage
import { QueueService } from '../queue';

// Mock Redis with more comprehensive coverage
const mockRedisClient = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  ping: jest.fn(),
  lPush: jest.fn(),
  rPop: jest.fn(),
  lLen: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  isReady: true,
};

// Mock Redis constructor
jest.mock('redis', () => ({
  createClient: jest.fn(() => mockRedisClient),
}));

// Mock console.log to avoid noise
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

describe('QueueService Direct Coverage Tests', () => {
  beforeEach(() => {
    // Reset singleton instance
    (QueueService as any).instance = null;
    jest.clearAllMocks();
  });

  describe('Core Service Methods', () => {
    it('should test getInstance method directly', () => {
      const instance1 = QueueService.getInstance();
      const instance2 = QueueService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should test initialize method with success', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      await QueueService.initialize();
      expect(mockRedisClient.connect).toHaveBeenCalled();
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should test initialize method with error', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('Connection failed'));
      await expect(QueueService.initialize()).rejects.toThrow('Connection failed');
    });

    it('should test close method with success', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      await QueueService.close();
      expect(mockRedisClient.disconnect).toHaveBeenCalled();
      expect(mockRedisClient.off).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should test close method with error', async () => {
      mockRedisClient.disconnect.mockRejectedValue(new Error('Disconnect failed'));
      await expect(QueueService.close()).rejects.toThrow('Disconnect failed');
    });

    it('should test healthCheck with success', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');
      const result = await QueueService.healthCheck();
      expect(result).toBe(true);
      expect(mockRedisClient.ping).toHaveBeenCalled();
    });

    it('should test healthCheck with error', async () => {
      mockRedisClient.ping.mockRejectedValue(new Error('Ping failed'));
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
    });

    it('should test healthCheck with wrong response', async () => {
      mockRedisClient.ping.mockResolvedValue('WRONG');
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
    });

    it('should test healthCheck when client not ready', async () => {
      mockRedisClient.isReady = false;
      const result = await QueueService.healthCheck();
      expect(result).toBe(false);
      mockRedisClient.isReady = true; // Reset
    });
  });

  describe('Queue Operations', () => {
    it('should test addJob with success', async () => {
      mockRedisClient.lPush.mockResolvedValue(1);
      const job = { id: 'test-job', type: 'test', data: { test: 'data' } };
      
      await QueueService.addJob('test-queue', job);
      expect(mockRedisClient.lPush).toHaveBeenCalledWith('test-queue', JSON.stringify(job));
    });

    it('should test addJob with error', async () => {
      mockRedisClient.lPush.mockRejectedValue(new Error('Push failed'));
      const job = { id: 'test-job', type: 'test' };
      
      await expect(QueueService.addJob('test-queue', job)).rejects.toThrow('Push failed');
    });

    it('should test getJob with success', async () => {
      const job = { id: 'test-job', type: 'test' };
      mockRedisClient.rPop.mockResolvedValue(JSON.stringify(job));
      
      const result = await QueueService.getJob('test-queue');
      expect(result).toEqual(job);
      expect(mockRedisClient.rPop).toHaveBeenCalledWith('test-queue');
    });

    it('should test getJob with null result', async () => {
      mockRedisClient.rPop.mockResolvedValue(null);
      
      const result = await QueueService.getJob('test-queue');
      expect(result).toBeNull();
    });

    it('should test getJob with error', async () => {
      mockRedisClient.rPop.mockRejectedValue(new Error('Pop failed'));
      
      await expect(QueueService.getJob('test-queue')).rejects.toThrow('Pop failed');
    });

    it('should test getJob with invalid JSON', async () => {
      mockRedisClient.rPop.mockResolvedValue('invalid-json');
      
      await expect(QueueService.getJob('test-queue')).rejects.toThrow();
    });

    it('should test getQueueLength with success', async () => {
      mockRedisClient.lLen.mockResolvedValue(5);
      
      const result = await QueueService.getQueueLength('test-queue');
      expect(result).toBe(5);
      expect(mockRedisClient.lLen).toHaveBeenCalledWith('test-queue');
    });

    it('should test getQueueLength with error', async () => {
      mockRedisClient.lLen.mockRejectedValue(new Error('Length failed'));
      
      await expect(QueueService.getQueueLength('test-queue')).rejects.toThrow('Length failed');
    });

    it('should test clearQueue with success', async () => {
      mockRedisClient.del.mockResolvedValue(1);
      
      await QueueService.clearQueue('test-queue');
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-queue');
    });

    it('should test clearQueue with error', async () => {
      mockRedisClient.del.mockRejectedValue(new Error('Delete failed'));
      
      await expect(QueueService.clearQueue('test-queue')).rejects.toThrow('Delete failed');
    });

    it('should test queueExists with true result', async () => {
      mockRedisClient.exists.mockResolvedValue(1);
      
      const result = await QueueService.queueExists('test-queue');
      expect(result).toBe(true);
      expect(mockRedisClient.exists).toHaveBeenCalledWith('test-queue');
    });

    it('should test queueExists with false result', async () => {
      mockRedisClient.exists.mockResolvedValue(0);
      
      const result = await QueueService.queueExists('test-queue');
      expect(result).toBe(false);
    });

    it('should test queueExists with error', async () => {
      mockRedisClient.exists.mockRejectedValue(new Error('Exists failed'));
      
      await expect(QueueService.queueExists('test-queue')).rejects.toThrow('Exists failed');
    });
  });

  describe('Error Handling', () => {
    it('should handle error event setup', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      let errorHandler: Function;
      
      mockRedisClient.on.mockImplementation((event, handler) => {
        if (event === 'error') {
          errorHandler = handler;
        }
      });
      
      await QueueService.initialize();
      
      // Simulate error event
      if (errorHandler) {
        errorHandler(new Error('Redis error'));
      }
      
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should handle error event cleanup', async () => {
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      
      await QueueService.close();
      
      expect(mockRedisClient.off).toHaveBeenCalledWith('error', expect.any(Function));
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle multiple operations in sequence', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.lPush.mockResolvedValue(1);
      mockRedisClient.rPop.mockResolvedValue(JSON.stringify({ id: 'test' }));
      mockRedisClient.lLen.mockResolvedValue(0);
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      
      await QueueService.initialize();
      await QueueService.addJob('test', { id: 'test', type: 'test' });
      const job = await QueueService.getJob('test');
      const length = await QueueService.getQueueLength('test');
      await QueueService.close();
      
      expect(job).toBeDefined();
      expect(length).toBe(0);
    });

    it('should handle service lifecycle', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);
      mockRedisClient.ping.mockResolvedValue('PONG');
      mockRedisClient.disconnect.mockResolvedValue(undefined);
      
      await QueueService.initialize();
      const isHealthy = await QueueService.healthCheck();
      await QueueService.close();
      
      expect(isHealthy).toBe(true);
    });
  });
});
