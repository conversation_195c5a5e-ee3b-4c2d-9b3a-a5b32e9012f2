#!/bin/bash

# ChainOps Startup Script with Custom Ports
# This script starts ChainOps with custom ports to avoid conflicts

set -e

echo "🚀 Starting ChainOps CI/CD Platform"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists node; then
        missing_deps+=("Node.js (v18+)")
    else
        node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -lt 18 ]; then
            missing_deps+=("Node.js v18+ (current: $(node -v))")
        fi
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists docker; then
        missing_deps+=("Docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("Docker Compose")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing prerequisites:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        echo "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Check if ports are available
check_ports() {
    log_info "Checking if ports are available..."
    
    local ports=(4000 4001 5433 6380 9002 9003)
    local busy_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            busy_ports+=($port)
        fi
    done
    
    if [ ${#busy_ports[@]} -ne 0 ]; then
        log_warning "The following ports are already in use:"
        for port in "${busy_ports[@]}"; do
            echo "  - Port $port"
        done
        echo ""
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Startup cancelled."
            exit 0
        fi
    else
        log_success "All ports are available"
    fi
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        log_success "Created backend/.env"
    else
        log_info "backend/.env already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env.local" ]; then
        cp frontend/.env.local.example frontend/.env.local
        log_success "Created frontend/.env.local"
    else
        log_info "frontend/.env.local already exists"
    fi
    
    # Runner environment
    if [ ! -f "runner/.env" ]; then
        cp runner/.env.example runner/.env
        log_success "Created runner/.env"
    else
        log_info "runner/.env already exists"
    fi
}

# Install dependencies if needed
install_dependencies() {
    log_info "Checking dependencies..."
    
    if [ ! -d "node_modules" ] || [ ! -d "backend/node_modules" ] || [ ! -d "frontend/node_modules" ] || [ ! -d "runner/node_modules" ]; then
        log_info "Installing dependencies..."
        
        # Root dependencies
        npm install
        
        # Backend dependencies
        cd backend && npm install && cd ..
        
        # Frontend dependencies
        cd frontend && npm install && cd ..
        
        # Runner dependencies
        cd runner && npm install && cd ..
        
        log_success "Dependencies installed"
    else
        log_success "Dependencies already installed"
    fi
}

# Start Docker services
start_docker_services() {
    log_info "Starting Docker services..."
    
    # Stop any existing services
    docker-compose -f docker-compose.dev.yml down >/dev/null 2>&1 || true
    
    # Start services
    docker-compose -f docker-compose.dev.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 15
    
    # Check if PostgreSQL is ready
    local retries=30
    while [ $retries -gt 0 ]; do
        if docker exec chainops-postgres-dev pg_isready -U chainops >/dev/null 2>&1; then
            break
        fi
        retries=$((retries - 1))
        sleep 1
    done
    
    if [ $retries -eq 0 ]; then
        log_error "PostgreSQL failed to start"
        exit 1
    fi
    
    log_success "Docker services started"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    cd backend
    
    # Generate Prisma client
    npx prisma generate >/dev/null 2>&1
    log_success "Generated Prisma client"
    
    # Check if database exists and has tables
    if ! npx prisma db pull >/dev/null 2>&1; then
        # Run migrations
        npx prisma migrate dev --name init >/dev/null 2>&1
        log_success "Database migrations completed"
        
        # Seed database
        npm run db:seed >/dev/null 2>&1
        log_success "Database seeded with demo data"
    else
        log_success "Database already exists"
    fi
    
    cd ..
}

# Start application services
start_applications() {
    log_info "Starting application services..."
    
    # Start backend in background
    cd backend
    npm run dev > ../backend.log 2>&1 &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 5
    
    # Start frontend in background
    cd frontend
    npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    cd ..
    
    # Wait a moment for frontend to start
    sleep 5
    
    # Start runner in background (optional)
    cd runner
    npm run dev > ../runner.log 2>&1 &
    RUNNER_PID=$!
    cd ..
    
    # Save PIDs for cleanup
    echo $BACKEND_PID > backend.pid
    echo $FRONTEND_PID > frontend.pid
    echo $RUNNER_PID > runner.pid
    
    log_success "Application services started"
}

# Health check
health_check() {
    log_info "Performing health checks..."
    
    # Wait for services to be fully ready
    sleep 10
    
    # Check backend
    local retries=30
    while [ $retries -gt 0 ]; do
        if curl -f http://localhost:4001/health >/dev/null 2>&1; then
            log_success "Backend is healthy"
            break
        fi
        retries=$((retries - 1))
        sleep 2
    done
    
    if [ $retries -eq 0 ]; then
        log_warning "Backend health check failed"
    fi
    
    # Check frontend
    retries=30
    while [ $retries -gt 0 ]; do
        if curl -f http://localhost:4000 >/dev/null 2>&1; then
            log_success "Frontend is accessible"
            break
        fi
        retries=$((retries - 1))
        sleep 2
    done
    
    if [ $retries -eq 0 ]; then
        log_warning "Frontend health check failed"
    fi
}

# Display final information
display_final_info() {
    echo ""
    echo "🎉 ChainOps is now running!"
    echo "=========================="
    echo ""
    echo "📋 Demo Credentials:"
    echo "  Admin: <EMAIL> / admin123"
    echo "  Demo User: <EMAIL> / demo123"
    echo ""
    echo "🌐 Service URLs:"
    echo "  Frontend: http://localhost:4000"
    echo "  Backend API: http://localhost:4001"
    echo "  API Health: http://localhost:4001/health"
    echo "  MinIO Console: http://localhost:9003 (chainops / chainops_password)"
    echo ""
    echo "📊 Service Status:"
    echo "  PostgreSQL: localhost:5433"
    echo "  Redis: localhost:6380"
    echo "  MinIO: localhost:9002"
    echo ""
    echo "📝 Logs:"
    echo "  Backend: tail -f backend.log"
    echo "  Frontend: tail -f frontend.log"
    echo "  Runner: tail -f runner.log"
    echo "  Docker: docker-compose -f docker-compose.dev.yml logs -f"
    echo ""
    echo "🛑 To stop ChainOps:"
    echo "  ./stop-chainops.sh"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  - Check logs if services don't start"
    echo "  - Ensure all ports are available"
    echo "  - Run 'docker-compose -f docker-compose.dev.yml ps' to check Docker services"
    echo ""
}

# Cleanup function
cleanup() {
    echo ""
    log_info "Cleaning up..."
    
    # Kill application processes
    if [ -f backend.pid ]; then
        kill $(cat backend.pid) 2>/dev/null || true
        rm backend.pid
    fi
    
    if [ -f frontend.pid ]; then
        kill $(cat frontend.pid) 2>/dev/null || true
        rm frontend.pid
    fi
    
    if [ -f runner.pid ]; then
        kill $(cat runner.pid) 2>/dev/null || true
        rm runner.pid
    fi
    
    # Stop Docker services
    docker-compose -f docker-compose.dev.yml down >/dev/null 2>&1 || true
    
    log_info "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    echo "This script will start ChainOps with the following configuration:"
    echo "  Frontend: http://localhost:4000"
    echo "  Backend: http://localhost:4001"
    echo "  PostgreSQL: localhost:5433"
    echo "  Redis: localhost:6380"
    echo "  MinIO: localhost:9002/9003"
    echo ""
    
    read -p "Do you want to continue? (Y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        echo "Startup cancelled."
        exit 0
    fi
    
    echo ""
    
    check_prerequisites
    check_ports
    setup_environment
    install_dependencies
    start_docker_services
    setup_database
    start_applications
    health_check
    display_final_info
    
    # Keep script running
    log_info "ChainOps is running. Press Ctrl+C to stop."
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
