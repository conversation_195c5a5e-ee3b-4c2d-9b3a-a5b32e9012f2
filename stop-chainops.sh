#!/bin/bash

# ChainOps Stop Script
# This script stops all ChainOps services

echo "🛑 Stopping ChainOps CI/CD Platform"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Stop application processes
stop_applications() {
    log_info "Stopping application services..."
    
    # Kill backend process
    if [ -f backend.pid ]; then
        local pid=$(cat backend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log_success "Backend stopped"
        fi
        rm backend.pid
    fi
    
    # Kill frontend process
    if [ -f frontend.pid ]; then
        local pid=$(cat frontend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log_success "Frontend stopped"
        fi
        rm frontend.pid
    fi
    
    # Kill runner process
    if [ -f runner.pid ]; then
        local pid=$(cat runner.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            log_success "Runner stopped"
        fi
        rm runner.pid
    fi
    
    # Kill any remaining Node.js processes on our ports
    local pids=$(lsof -ti:4000,4001 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        echo $pids | xargs kill 2>/dev/null || true
        log_success "Cleaned up remaining processes"
    fi
}

# Stop Docker services
stop_docker_services() {
    log_info "Stopping Docker services..."
    
    if docker-compose -f docker-compose.dev.yml down; then
        log_success "Docker services stopped"
    else
        log_warning "Failed to stop some Docker services"
    fi
}

# Clean up log files
cleanup_logs() {
    log_info "Cleaning up log files..."
    
    rm -f backend.log frontend.log runner.log
    log_success "Log files cleaned up"
}

# Main execution
main() {
    stop_applications
    stop_docker_services
    cleanup_logs
    
    echo ""
    echo "🎉 ChainOps has been stopped successfully!"
    echo ""
    echo "To start ChainOps again, run:"
    echo "  ./start-chainops.sh"
    echo ""
}

# Run main function
main "$@"
