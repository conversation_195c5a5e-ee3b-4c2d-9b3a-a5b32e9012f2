#!/usr/bin/env node

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:4000';
const BACKEND_URL = 'http://localhost:8081';

async function testFrontendPages() {
  console.log('🧪 Testing Frontend Pages...\n');
  
  const pages = [
    { url: `${FRONTEND_URL}`, name: 'Home Page' },
    { url: `${FRONTEND_URL}/auth/login`, name: 'Login Page' },
    { url: `${FRONTEND_URL}/dashboard`, name: 'Dashboard Page' },
    { url: `${FRONTEND_URL}/pipelines`, name: 'Pipelines Page' },
    { url: `${FRONTEND_URL}/projects`, name: 'Projects Page' },
    { url: `${FRONTEND_URL}/jobs`, name: 'Jobs Page' },
    { url: `${FRONTEND_URL}/environments`, name: 'Environments Page' },
    { url: `${FRONTEND_URL}/secrets`, name: 'Secrets Page' },
  ];
  
  const results = [];
  
  for (const page of pages) {
    try {
      console.log(`🔍 Testing ${page.name}...`);
      const response = await axios.get(page.url, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept 4xx as valid (might be auth redirects)
      });
      
      if (response.status === 200) {
        console.log(`✅ ${page.name} - OK (${response.status})`);
        results.push({ page: page.name, status: 'PASS', code: response.status });
      } else if (response.status === 404) {
        console.log(`❌ ${page.name} - NOT FOUND (${response.status})`);
        results.push({ page: page.name, status: 'FAIL', code: response.status });
      } else {
        console.log(`⚠️  ${page.name} - ${response.status} (might be redirect)`);
        results.push({ page: page.name, status: 'WARN', code: response.status });
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${page.name} - CONNECTION REFUSED`);
        results.push({ page: page.name, status: 'FAIL', code: 'CONN_REFUSED' });
      } else {
        console.log(`❌ ${page.name} - ERROR: ${error.message}`);
        results.push({ page: page.name, status: 'FAIL', code: error.code || 'ERROR' });
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  return results;
}

async function testBackendAPI() {
  console.log('\n🔧 Testing Backend API...\n');
  
  try {
    // Test login
    console.log('🔐 Testing login...');
    const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
      login: '<EMAIL>',
      password: 'demo123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Backend login - SUCCESS');
      const token = loginResponse.data.data.token;
      
      // Test authenticated endpoints
      const endpoints = [
        '/api/projects',
        '/api/pipelines',
        '/api/jobs',
        '/auth/me'
      ];
      
      for (const endpoint of endpoints) {
        try {
          const response = await axios.get(`${BACKEND_URL}${endpoint}`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (response.data.success) {
            console.log(`✅ ${endpoint} - SUCCESS`);
          } else {
            console.log(`❌ ${endpoint} - FAILED`);
          }
        } catch (error) {
          console.log(`❌ ${endpoint} - ERROR: ${error.message}`);
        }
      }
      
      return true;
    } else {
      console.log('❌ Backend login - FAILED');
      return false;
    }
  } catch (error) {
    console.log(`❌ Backend API - ERROR: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 ChainOps Frontend & Backend Integration Test\n');
  console.log('='.repeat(50));
  
  // Test frontend pages
  const frontendResults = await testFrontendPages();
  
  // Test backend API
  const backendWorking = await testBackendAPI();
  
  // Summary
  console.log('\n📋 Test Summary:');
  console.log('='.repeat(50));
  
  console.log('\n🌐 Frontend Pages:');
  frontendResults.forEach(result => {
    const icon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
    console.log(`${icon} ${result.page}: ${result.status} (${result.code})`);
  });
  
  console.log('\n🔧 Backend API:');
  console.log(`${backendWorking ? '✅' : '❌'} Backend API: ${backendWorking ? 'WORKING' : 'FAILED'}`);
  
  const passedPages = frontendResults.filter(r => r.status === 'PASS').length;
  const totalPages = frontendResults.length;
  
  console.log('\n🎯 Overall Results:');
  console.log(`Frontend: ${passedPages}/${totalPages} pages accessible`);
  console.log(`Backend: ${backendWorking ? 'Working' : 'Failed'}`);
  
  if (passedPages >= totalPages * 0.8 && backendWorking) {
    console.log('\n🎉 SUCCESS! ChainOps platform is functional! 🚀');
    console.log('\n📝 Next Steps:');
    console.log('1. Visit http://localhost:4000/auth/login');
    console.log('2. Login with: <EMAIL> / demo123');
    console.log('3. Explore the platform features');
  } else {
    console.log('\n⚠️  Some issues detected. Please check the results above.');
  }
}

main().catch(console.error);
