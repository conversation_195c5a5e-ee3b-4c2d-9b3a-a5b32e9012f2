#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:8081';
const DEMO_EMAIL = '<EMAIL>';
const DEMO_PASSWORD = 'demo123';

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${API_BASE}/auth/login`, {
      login: DEMO_EMAIL,
      password: DEMO_PASSWORD
    });
    
    if (response.data.success && response.data.data) {
      authToken = response.data.data.token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.log('❌ Login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.response?.data || error.message);
    return false;
  }
}

async function testAPI(endpoint, description) {
  try {
    console.log(`🧪 Testing ${description}...`);
    const response = await axios.get(`${API_BASE}${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log(`✅ ${description} - SUCCESS`);
      return response.data.data;
    } else {
      console.log(`❌ ${description} - FAILED:`, response.data.message);
      return null;
    }
  } catch (error) {
    console.log(`❌ ${description} - ERROR:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function triggerPipelineRun(pipelineId) {
  try {
    console.log(`🚀 Triggering pipeline run for pipeline: ${pipelineId}`);
    const response = await axios.post(`${API_BASE}/api/pipelines/${pipelineId}/runs`, {
      branch: 'main',
      commit: 'abc123',
      message: 'Test pipeline execution'
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ Pipeline run triggered successfully');
      return response.data.data;
    } else {
      console.log('❌ Failed to trigger pipeline run:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Pipeline run error:', error.response?.data?.message || error.message);
    return null;
  }
}

async function main() {
  console.log('🧪 ChainOps Platform Functionality Test\n');
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    process.exit(1);
  }
  
  console.log('\n📊 Testing API Endpoints...\n');
  
  // Step 2: Test all major endpoints
  const tests = [
    { endpoint: '/api/projects', description: 'Projects API' },
    { endpoint: '/api/pipelines', description: 'Pipelines API' },
    { endpoint: '/api/jobs', description: 'Jobs API' },
    { endpoint: '/auth/me', description: 'User Profile API' },
  ];
  
  const results = {};
  for (const test of tests) {
    results[test.description] = await testAPI(test.endpoint, test.description);
    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
  }
  
  // Step 3: Test pipeline execution
  console.log('\n🚀 Testing Pipeline Execution...\n');
  
  const pipelines = results['Pipelines API'];
  if (pipelines && pipelines.pipelines && pipelines.pipelines.length > 0) {
    const firstPipeline = pipelines.pipelines[0];
    console.log(`📋 Found pipeline: "${firstPipeline.name}" (${firstPipeline.id})`);
    
    const runResult = await triggerPipelineRun(firstPipeline.id);
    if (runResult) {
      console.log(`✅ Pipeline run created with ID: ${runResult.id || runResult.run?.id}`);
    }
  } else {
    console.log('❌ No pipelines found to test execution');
  }
  
  // Step 4: Summary
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  const testResults = [
    { name: 'Authentication', status: loginSuccess ? '✅ PASS' : '❌ FAIL' },
    { name: 'Projects API', status: results['Projects API'] ? '✅ PASS' : '❌ FAIL' },
    { name: 'Pipelines API', status: results['Pipelines API'] ? '✅ PASS' : '❌ FAIL' },
    { name: 'Jobs API', status: results['Jobs API'] ? '✅ PASS' : '❌ FAIL' },
    { name: 'User Profile API', status: results['User Profile API'] ? '✅ PASS' : '❌ FAIL' },
  ];
  
  testResults.forEach(test => {
    console.log(`${test.name}: ${test.status}`);
  });
  
  const passedTests = testResults.filter(test => test.status.includes('✅')).length;
  const totalTests = testResults.length;
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! ChainOps platform is fully functional! 🚀');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  console.log('\n🌐 Frontend URL: http://localhost:4000');
  console.log('🔐 Login: <EMAIL> / demo123');
  console.log('📚 Backend API: http://localhost:8081');
}

main().catch(console.error);
